# Api Teedy - Ecommerce

## Technos And Requirement

- <PERSON><PERSON> 11.x
- Backpack 6.x
- PHP > 8.2
- composer 2

## Installation
### Basic installation
Clone repo and after execute this commands

```bash
composer install
...
# Start with basic User and products
php artisan migrate:fresh --seed
```

The seed create 3 roles in database, 4 users. 1 is a `super_admin`, 1 an `admin`, 2 `client` (1 regular, 1 veteran). Only the `super_admin` role can change roles and permissions in the admin panel.

In local, start your server...
```bash
php artisan serve
```

To build front-end assets, execute
```bash
npm i
npm run dev
```

php artisan storage:link

... And have fun

### For BlueLink Connexion

enter this different value in `.env` doc for your environment

```bash
# For activate sync in your environment
SYNC_BLUELINK=true
# PROD
BL_HOST="http://saas.bluelinkerp.com:8880"
BL_API_KEY=1A630ECF-BFB5-4973-B8AF-30767AA3ACAE
# DEV
BL_HOST="http://saas.bluelinkerp.com:37183"
BL_API_KEY=2E960E69-EAFD-49B0-980A-55DD56731ABA
```

## PostMan API Documentation
@DEPRECATED Old Ducumentation

<https://documenter.getpostman.com/view/********/TzRPjpiT>

Documentation Postman

<https://documenter.getpostman.com/view/3816130/2s83zfQkW4>

## MCD
<https://www.figma.com/file/IjrQggPQ3efgjHgBCul7WB?>

## Moneris Doc
Moneris documentation: <https://developer.moneris.com/en/Documentation/NA/E-Commerce%20Solutions/API>

Moneris test store login page(credential in 1password): https://esqa.moneris.com/mpg/

The username and password are the same as the moneris account, and the store id is mentionned below

Moneris store: monca10176

Moneris current data key: O1UBSMpelItCCvE9iLxT

To quickly test transactions, refunds and cancellations, execute the desired operation in the administration or initiation a transaction with the frontend application. you can then search for previous transactions using the Reports tab on Moneris.

If there is ever an issue with transactions and it needs to be refunded or void, it can all be done manually in moneris using the order ID.
## BlueLink Doc - ERP
<https://help.bluelinkerp.com/home/<USER>

The basic connection is 

Username: WebAPI

MDP: WebAPI

Got questions? Contact the helpdesk(ask Teedy if logins are not in 1password): https://helpdesk.bluelinkerp.com/

## Teedy operations Synced with BlueLink

Orders: Create

Users: Create, Update (limited)

Prescriptions: Create, Update

Product: Sync inventory

## BlueLink Application VPN + remote desktop installation

Download ciscovpn anyconnect here: <https://bluelink.ca/ftp/ciscovpn> 
for mac: anyconnect-macos-permalink.dmg
windows: anyconnect-win-permalink.msi

Install and connect using the documentation (available in 1password teedy)

Once vpn is installed, use the profile group: Profile29183

The username must be in this format teedy\Username

Once the vpn is connected, install windows remote desktop

bluelink application login

username: ilan
password: 123


## The great import from V1 to V2 steps

step 0: BACKUP YOUR DATABASE
step 0.5: export the pages and global_content table from stage to keep new content
step 1: generate the required json files from Teedy v1 API routes and store them in the right folder (REMEMBER TO PUT THEM BACK IN COMMENTS BEFORE PUSHING ANYTHING ON V1)
Step 2: generate the required json files using postman API, store them in the right folder and the stored procedures for the following functions; BLWeb_GetInventoryPROC, BLWeb_GetPhysicianPROC, BLWeb_GetPrescriptionsPROC, BLWeb_GetCustomerPROC
Step 2.5: maybe run php artisan migrate:fresh --seed to reset the db, migrations are not super stable and a reset might be needed.
Step 3: run php artisan app:import-all

step 4: reimport the pages and global_content table from stage

Items with a warning sign in the listing page means that they did not match up with any results in bluelink and manual modifications are needed.

step 5: change env variable to true: MONERIS_SYNC=true, MONERIS_TEST_MODE=false 

step 6: manually get postal code for expedition page.


