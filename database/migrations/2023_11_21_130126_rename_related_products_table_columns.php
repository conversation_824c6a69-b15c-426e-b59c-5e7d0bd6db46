<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('related_products', 'product_id'))
            Schema::table('related_products', function (Blueprint $table) {
                $table->renameColumn('product_id', 'product_detail_id');
            });

        if (Schema::hasColumn('related_products', 'related_product_id'))
            Schema::table('related_products', function (Blueprint $table) {
                $table->renameColumn('related_product_id', 'related_product_detail_id');
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('related_products', 'product_detail_id'))
            Schema::table('related_products', function (Blueprint $table) {
                $table->renameColumn('product_detail_id', 'product_id');
            });

        if (Schema::hasColumn('related_products', 'related_product_detail_id'))
            Schema::table('related_products', function (Blueprint $table) {
                $table->renameColumn('related_product_detail_id', 'related_product_id');
            });
    }
};
