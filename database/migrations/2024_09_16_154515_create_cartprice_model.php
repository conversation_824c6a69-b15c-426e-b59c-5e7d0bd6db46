<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cartprice', function (Blueprint $table) {
            $table->unsignedInteger('id', true)->primary();
            $table->unsignedBigInteger('fk_user_id');
            $table->unsignedInteger('fk_promo_code_id')->nullable();
            $table->date('shipping_date')->nullable();
            $table->string('shipping_time', 255)->nullable()->collation('utf8mb4_unicode_ci');
            $table->timestamp('TTL');
            $table->timestamps();

            $table->foreign('fk_user_id')->references('id')->on('users');
            $table->foreign('fk_promo_code_id')->references('id')->on('promo_codes');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // drop foreign keys
        Schema::table('cartprice', function (Blueprint $table) {
            $table->dropForeign(['fk_promo_code_id']);
            $table->dropForeign(['fk_user_id']);
        });
        Schema::dropIfExists('cartprice');
    }
};
