<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // rename column user_quota in user_quota_remaining in user table
        Schema::table('user_details', function (Blueprint $table) {
            $table->renameColumn('quota_user', 'quota_user_remaining');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_details', function (Blueprint $table) {
            $table->renameColumn('quota_user_remaining', 'quota_user');
        });
    }
};
