<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserDetailTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('users', function (Blueprint $table) {
            $table->renameColumn('name', 'firstname');
            $table->string('lastname')->after('firstname');
            $table->boolean('archived')->nullable()->after('password');
            $table->string('status', 50)->nullable()->after('password');
            $table->string('proof_identity')->nullable();
            $table->string('api_token', 255)->nullable()->after('password');
        });
        Schema::create('user_details', function (Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->unsignedBigInteger('fk_user_id');
            $table->string('moneris_id')->nullable();
            $table->string('bluelink_id')->nullable();
            $table->string('teedy_client_id')->nullable();
            $table->string('phone')->nullable();
            $table->date('birth_date');
            $table->double('quota_user')->nullable();
            $table->string('language', 3);
            $table->boolean('veteran')->default(false);
            $table->double('quota_veteran_remaining')->nullable();
            $table->double('quota_veteran_allowed')->nullable();
            $table->date('anniversary_date')->nullable();
            $table->boolean('licence_acmpr')->nullable();
            $table->string('gender')->nullable();
            $table->string('inscription_choice')->nullable();
            $table->text('symptome')->nullable();
            $table->text('symptome_other')->nullable();
            $table->text('consumption')->nullable();
            $table->text('consumption_other')->nullable();
            $table->timestamps();
            $table->foreign('fk_user_id')->references('id')->on('users')->onDelete('CASCADE');
        });

        Schema::create('addresses', function (Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->unsignedBigInteger('fk_user_id');
            $table->string('address')->nullable();
            $table->string('city')->nullable();
            $table->string('pc', 10)->nullable();
            $table->string('province', 25)->nullable();
            $table->boolean('delivery')->default(false);
            $table->boolean('billing')->default(false);
            $table->boolean('last_use')->default(true);
            $table->timestamps();
            $table->foreign('fk_user_id')->references('id')->on('users')->onDelete('CASCADE');
        });

        Schema::create('prescriptions', function (Blueprint $table) {
            $table->unsignedInteger('id', true);
            $table->unsignedBigInteger('fk_user_id');

            $table->string('prescription_number')->nullable();
            $table->string('code_admin')->nullable();
            $table->string('doc_name')->nullable();
            $table->string('clinic_name')->nullable();
            $table->string('daily_dosage')->nullable();
            $table->integer('validity')->nullable(); 
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->string('status')->default('pending');
            $table->string('prescription_photo')->nullable();

            $table->timestamps();
            $table->foreign('fk_user_id')->references('id')->on('users')->onDelete('CASCADE');
        });

        Schema::create('group_users', function (Blueprint $table) {
            $table->increments('id')->unsigned();
            $table->string('group_name');
            $table->timestamps();
        });
        Schema::create('users_group_users', function (Blueprint $table) {
            $table->unsignedBigInteger('fk_user_id');
            $table->unsignedInteger('fk_group_user_id');
            $table->foreign('fk_user_id')->references('id')->on('users')->onDelete('CASCADE');
            $table->foreign('fk_group_user_id')->references('id')->on('group_users')->onDelete('CASCADE');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users_group_users');
        Schema::dropIfExists('group_users');
        Schema::dropIfExists('prescriptions');
        Schema::dropIfExists('addresses');
        Schema::dropIfExists('user_details');
        Schema::table('users', function ($table) {
            $table->renameColumn('firstname', 'name');
            $table->dropColumn('lastname');
            $table->dropColumn('archived');
            $table->dropColumn('status');
            $table->dropColumn('api_token');
        });
    }
}
