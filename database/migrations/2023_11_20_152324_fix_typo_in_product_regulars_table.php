<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasColumn('product_regulars', 'effect_sleeepy'))
            Schema::table('product_regulars', function (Blueprint $table) {
                $table->renameColumn('effect_sleeepy', 'effect_sleepy');
            });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('product_regulars', 'effect_sleepy'))
            Schema::table('product_regulars', function (Blueprint $table) {
                $table->renameColumn('effect_sleepy', 'effect_sleeepy');
            });
    }
};
