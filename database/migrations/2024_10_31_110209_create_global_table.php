<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateGlobalTable extends Migration
{
    public function up()
    {
        Schema::create('global_content', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Name of content (e.g., 'Home Banner')
            $table->string('type'); // Type of content (e.g., 'home_banner', 'testimonial')
            $table->json('data')->default(json_encode([])); // Store content data as JSON
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('global_content');
    }
}