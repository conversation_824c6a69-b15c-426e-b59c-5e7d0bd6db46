<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //add bl_displayuom field to product_regular table
        Schema::table('product_accessories', function (Blueprint $table) {
            $table->string('bl_displayuom', 50)->nullable();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //drop bl_displayuom field from product_regular table
        Schema::table('product_accessories', function (Blueprint $table) {
            $table->dropColumn('bl_displayuom');
        });
    }
};
