<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // rename the column image into logo
        Schema::table('producers', function (Blueprint $table) {
            $table->renameColumn('image', 'logo');
            $table->string('banner')->nullable()->after('logo');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('producers', function (Blueprint $table) {
            $table->renameColumn('logo', 'image');
            $table->dropColumn('banner');
        });
    }
};
