<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //create a user flag to check if its been confirmed with bluelink
        Schema::table('product_details', function (Blueprint $table) {
            $table->date('packaging_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //drop the column
        Schema::table('product_details', function (Blueprint $table) {
            $table->dropColumn('packaging_date');
        });
    }
};
