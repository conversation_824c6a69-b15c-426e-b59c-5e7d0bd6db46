<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //add cardholder field to orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->text('cardholder')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //down
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('cardholder');
        });
    }
};
