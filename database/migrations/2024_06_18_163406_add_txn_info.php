<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add txn_number and reference_number to orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->string('txn_number')->nullable();
            $table->string('reference_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //drop txn_number and reference_number from orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('txn_number');
            $table->dropColumn('reference_number');
        });
    }
};
