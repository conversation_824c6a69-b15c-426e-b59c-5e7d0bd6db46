<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //alter proof identity size to text
        Schema::table('users', function (Blueprint $table) {
            $table->text('proof_identity')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //reverse
        Schema::table('users', function (Blueprint $table) {
            $table->string('proof_identity')->nullable()->change();
        });
    }
};
