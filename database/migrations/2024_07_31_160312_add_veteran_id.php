<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add veteran_id to user_details table
        Schema::table('user_details', function (Blueprint $table) {
            $table->string('veteran_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //drop veteran_id from user_details table
        Schema::table('user_details', function (Blueprint $table) {
            $table->dropColumn('veteran_id');
        });
    }
};
