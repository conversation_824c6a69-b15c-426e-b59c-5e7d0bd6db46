<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePromoBannersTable extends Migration
{
    public function up()
    {
        Schema::create('promo_slides', function (Blueprint $table) {
            $table->id();
            $table->string('title');

            $table->text('image_fr');
            $table->text('image_m_fr')->nullable();
            $table->string('text_fr')->nullable();
            $table->string('label_fr')->nullable();
            $table->string('link_fr')->nullable();

            $table->text('image_en')->nullable();
            $table->text('image_m_en')->nullable();
            $table->string('text_en')->nullable();
            $table->string('label_en')->nullable();
            $table->string('link_en')->nullable();

            $table->boolean('published');
            $table->timestamps();
        });

        Schema::create('promo_banners', function (Blueprint $table) {
            $table->id();
            $table->string('content_fr');
            $table->string('content_en');

            $table->boolean('published');
            $table->timestamps();
        });
    }

    public function down()
    {
        Schema::dropIfExists('promo_banners');
        Schema::dropIfExists('promo_slides');
    }
}
