<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('producers', function (Blueprint $table) {
            $table->string('meta_title_fr')->nullable()->after('description_en');
            $table->string('meta_title_en')->nullable()->after('meta_title_fr');
            $table->string('meta_description_fr')->nullable()->after('meta_title_en');
            $table->string('meta_description_en')->nullable()->after('meta_description_fr');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('producers', function (Blueprint $table) {
            $table->dropColumn('meta_title_fr');
            $table->dropColumn('meta_title_en');
            $table->dropColumn('meta_description_fr');
            $table->dropColumn('meta_description_en');
        });
    }
};
