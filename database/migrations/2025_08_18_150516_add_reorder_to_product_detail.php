<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('product_details', function (Blueprint $table) {
            $table->integer('cat_parent_id')->default(0)->nullable();
            $table->integer('cat_lft')->default(0)->after('cat_parent_id');
            $table->integer('cat_rgt')->default(0)->after('cat_lft');
            $table->integer('cat_depth')->default(0)->after('cat_rgt');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_details', function (Blueprint $table) {
            $table->dropColumn('cat_parent_id');
            $table->dropColumn('cat_lft');
            $table->dropColumn('cat_rgt');
            $table->dropColumn('cat_depth');
        });
    }
};
