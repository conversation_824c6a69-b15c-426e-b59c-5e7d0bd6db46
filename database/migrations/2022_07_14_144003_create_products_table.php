<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_details', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedInteger('fk_producer_id')->nullable();
            $table->unsignedInteger('fk_variation_of_id')->nullable();
            $table->string('title_fr');
            $table->string('title_en')->nullable();
            $table->string('slug_fr');
            $table->string('slug_en')->nullable();
            $table->text('description_fr');
            $table->text('description_en')->nullable();

            $table->string('meta_title_fr')->nullable();
            $table->string('meta_title_en')->nullable();
            $table->string('meta_description_fr')->nullable();
            $table->string('meta_description_en')->nullable();

            $table->string('status')->nullable();
            $table->string('labels')->nullable();

            $table->string('sku');
            $table->double('price');
            $table->unsignedInteger('stock');
            $table->unsignedInteger('max_per_person')->nullable();
            $table->boolean('is_applicable_veteran')->nullable();
            $table->unsignedInteger('nb_max_veteran')->nullable();
            $table->boolean('publish')->nullable();
            $table->boolean('featured')->nullable();

            $table->integer('parent_id')->nullable();
            $table->integer('lft')->nullable();
            $table->integer('rgt')->nullable();
            $table->integer('depth')->nullable();

            $table->morphs('productable');
            $table->timestamps();
            $table->foreign('fk_producer_id')->references('id')->on('producers')->onDelete('cascade');
            $table->foreign('fk_variation_of_id')->references('id')->on('product_details');
        });

        Schema::create('product_accessories', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();

            $table->double('size')->nullable();
            $table->string('unity')->nullable();

            $table->timestamps();

        });

        Schema::create('product_regulars', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedInteger('fk_category_id');

            $table->string('type')->nullable();
            $table->string('intensity')->nullable();
            $table->string('dominant')->nullable();
            $table->text('cannabinoids')->nullable();
            $table->text('aromas')->nullable();
            $table->text('terpenes')->nullable();
            $table->double('terpene_total')->nullable();

            $table->double('effect_relaxed')->nullable();
            $table->double('effect_sleeepy')->nullable();
            $table->double('effect_euphoric')->nullable();
            $table->double('effect_energic')->nullable();
            $table->double('effect_happy')->nullable();
            $table->double('benefits_stress')->nullable();
            $table->double('benefits_pain')->nullable();
            $table->double('benefits_depress')->nullable();
            $table->double('benefits_appetite')->nullable();
            $table->double('benefits_sleep')->nullable();

            $table->double('format')->nullable();
            $table->string('unity')->nullable();
            $table->double('quota_value')->nullable();
            $table->string('price_per_unit')->nullable();
            $table->integer('sub_format_quantity')->nullable();
            $table->double('sub_format')->nullable();
            $table->string('sub_format_unity')->nullable();
            $table->text('certificat')->nullable();

            $table->foreign('fk_category_id')->references('id')->on('product_categories')->onDelete('cascade');
            $table->timestamps();

        });

        Schema::create('product_clones', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();

            $table->string('type')->nullable();
            $table->string('intensity')->nullable();
            $table->string('dominant')->nullable();
            $table->text('cannabinoids')->nullable();
            $table->text('aromas')->nullable();
            $table->text('terpenes')->nullable();
            $table->double('terpene_total')->nullable();
            $table->text('additional_details')->nullable();

            $table->double('format')->nullable();
            $table->string('unity')->nullable();
            $table->double('quota_value')->nullable();
            $table->string('price_per_unit')->nullable();
            $table->integer('sub_format_quatity')->nullable();
            $table->double('sub_format')->nullable();
            $table->string('sub_format_unity')->nullable();
            $table->text('certificat')->nullable();

            $table->timestamps();
        });

        Schema::create('product_images', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedInteger('fk_productdetail_id');
            $table->string('alt_text')->nullable();
            $table->text('path')->nullable();
            $table->boolean('primary')->nullable();
            $table->boolean('og')->nullable();

            $table->tinyInteger('parent_id')->nullable();
            $table->tinyInteger('lft')->nullable();
            $table->tinyInteger('rgt')->nullable();
            $table->tinyInteger('depth')->nullable();
            $table->timestamps();
            $table->foreign('fk_productdetail_id')->references('id')->on('product_details')->onDelete('cascade');
        });

        Schema::create('related_products', function (Blueprint $table) {
            $table->unsignedInteger('product_id');
            $table->integer('related_product_id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('product_clones');
        Schema::dropIfExists('product_regulars');
        Schema::dropIfExists('product_accessories');
        Schema::dropIfExists('product_variations'); // Temporary, to get rid of unused table
        Schema::dropIfExists('product_details');
        Schema::dropIfExists('product_images');
        Schema::dropIfExists('images'); // Temporary, to get rid of unused table
        Schema::dropIfExists('related_products');
    }
}

