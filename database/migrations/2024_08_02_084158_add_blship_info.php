<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add shipcode and shipname field to addresses table. shipcode max size is 10 characters
        Schema::table('addresses', function (Blueprint $table) {
            $table->string('shipcode', 10)->nullable();
            $table->string('shipname')->nullable();
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //drop shipcode and shipname field from addresses table
        Schema::table('addresses', function (Blueprint $table) {
            $table->dropColumn('shipcode');
            $table->dropColumn('shipname');
        });
    }
};
