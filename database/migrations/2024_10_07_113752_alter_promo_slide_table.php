<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('promo_slides', function (Blueprint $table) {
            $table->text('image_fr')->nullable()->change();
            $table->string('link_fr')->nullable();
            $table->string('link_en')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('promo_slides', function (Blueprint $table) {
            $table->text('image_fr')->nullable(false)->change();
            $table->dropColumn('link_fr');
            $table->dropColumn('link_en');
        });
    }
};
