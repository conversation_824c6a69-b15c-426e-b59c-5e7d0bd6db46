<?php

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up(): void
    {
        // Step 1: Add the column without the unique constraint
        Schema::table('users', function (Blueprint $table) {
            $table->string('folder_token')->nullable();
        });

        // Step 2: Generate a unique token for each user
        \App\Models\User::all()->each(function ($user) {
            $user->folder_token = (string) Str::uuid();
            $user->save();
        });

        // Step 3: Add the unique constraint
        Schema::table('users', function (Blueprint $table) {
            $table->string('folder_token')->unique()->change();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('folder_token');
        });
    }
};
