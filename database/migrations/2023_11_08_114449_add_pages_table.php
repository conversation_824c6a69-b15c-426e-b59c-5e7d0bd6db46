<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pages', function (Blueprint $table) {
            $table->id();

            $table->string('category');

            $table->string('title_fr');
            $table->string('title_en');
            $table->text('subtitle_fr')->nullable();
            $table->text('subtitle_en')->nullable();
            $table->string('slug_fr');
            $table->string('slug_en');
            $table->text('content_fr');
            $table->text('content_en');

            $table->string('meta_title_fr')->nullable();
            $table->string('meta_title_en')->nullable();
            $table->string('meta_description_fr')->nullable();
            $table->string('meta_description_en')->nullable();

            $table->boolean('image')->nullable();
            $table->boolean('show_on_top')->default(false);
            $table->boolean('publish')->default(true);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pages');
    }
};
