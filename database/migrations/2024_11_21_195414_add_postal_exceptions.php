<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //add this field postal_codes_included
        Schema::table('shippings', function (Blueprint $table) {
            $table->text('postal_codes_included')->nullable();
            $table->text('postal_codes_excluded')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('shippings', function (Blueprint $table) {
            $table->dropColumn('postal_codes_included');
            $table->dropColumn('postal_codes_excluded');
        });
    }
};
