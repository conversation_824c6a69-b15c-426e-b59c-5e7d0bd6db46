<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('promo_slides', function (Blueprint $table) {
            $table->dropColumn(['text_fr', 'link_fr', 'text_en', 'link_en']);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('promo_slides', function (Blueprint $table) {
            $table->string('text_fr')->nullable();
            $table->string('link_fr')->nullable();
            $table->string('text_en')->nullable();
            $table->string('link_en')->nullable();
        });
    }
};
