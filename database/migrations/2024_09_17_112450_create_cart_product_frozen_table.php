<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cart_products_frozen', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('fk_product_id');
            $table->unsignedInteger('fk_cart_id');
            $table->integer('qty')->default(0);
            $table->timestamps();
            $table->foreign('fk_product_id')->references('id')->on('product_details')->onDelete('CASCADE');
            $table->foreign('fk_cart_id')->references('id')->on('cartprice')->onDelete('CASCADE');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cart_products_frozen', function (Blueprint $table) {
            $table->dropForeign(['fk_product_id']);
            $table->dropForeign(['fk_cart_id']);
        });
    
        Schema::dropIfExists('cart_products_frozen');
    }
};
