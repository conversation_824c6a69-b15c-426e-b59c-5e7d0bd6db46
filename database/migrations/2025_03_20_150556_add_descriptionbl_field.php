<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //add description_bl field to product_details table
        Schema::table('product_details', function (Blueprint $table) {
            $table->text('description_bl')->nullable()->after('bluelink_synced');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //drop description_bl field from product_details table
        Schema::table('product_details', function (Blueprint $table) {
            $table->dropColumn('description_bl');
        });
    }
};
