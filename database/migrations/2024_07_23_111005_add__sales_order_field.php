<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        //add sales order number field to the orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->string('sales_order_number')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //drop sales order number field from the orders table
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn('sales_order_number');
        });
    }
};
