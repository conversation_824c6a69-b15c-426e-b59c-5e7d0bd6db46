<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddEcommercePartOfMcd extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('taxes', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->string('code', 255);
            $table->decimal('value', 10, 4)->default(0);
            $table->string('country', 100);
            $table->string('province', 100)->nullable()->default('qc');
            $table->boolean('add_existance_tax')->default(false);
            $table->integer('parent_id')->nullable();
            $table->integer('lft')->nullable();
            $table->integer('rgt')->nullable();
            $table->integer('depth')->nullable();
            $table->timestamps();
        });
        Schema::create('shippings', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->string('title_fr', 255);
            $table->string('title_en', 255);
            $table->double('price', 255)->default(0);
            $table->string('type', 25)->nullable(); // (%,$,0)
            $table->string('country', 100);
            $table->string('province', 100)->nullable();
            $table->text('postal_codes')->nullable();
            $table->string('delivery_type');

            $table->integer('parent_id')->nullable();
            $table->integer('lft')->nullable();
            $table->integer('rgt')->nullable();
            $table->integer('depth')->nullable();
            $table->timestamps();
        });
        Schema::create('orders', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedBigInteger('fk_user_id');
            $table->string('moneris_id')->nullable();
            $table->string('status', 60)->nullable(); // shipping, shipped, received, in progress...
            $table->dateTime('date_in_progress')->nullable();
            $table->dateTime('date_shipped')->nullable();
            $table->dateTime('date_delivered')->nullable();
            $table->dateTime('date_cancelled')->nullable();
            $table->longText('order_content')->nullable();
            $table->double('total')->nullable();
            $table->text('additional_content')->nullable();
            $table->timestamps();
            $table->foreign('fk_user_id')->references('id')->on('users')->onDelete('CASCADE');
        });

        Schema::create('carts', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedBigInteger('fk_user_id');
            $table->date('shipping_date')->nullable();
            $table->string('shipping_time')->nullable();
            $table->timestamps();
            $table->foreign('fk_user_id')->references('id')->on('users')->onDelete('CASCADE');
        });
        Schema::create('cart_products', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedInteger('fk_product_id');
            $table->unsignedInteger('fk_cart_id');
            $table->integer('qty')->default(0);
            $table->timestamps();
            $table->foreign('fk_product_id')->references('id')->on('product_details')->onDelete('CASCADE');
            $table->foreign('fk_cart_id')->references('id')->on('carts')->onDelete('CASCADE');
        });

        Schema::create('promo_codes', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();

            $table->string('code', 25);
            $table->decimal('amount')->default(0);
            $table->string('type', 25); // ('amount', 'percent', 'free_shipping')

            $table->double('minimum_price')->nullable();
            $table->double('maximum_price')->nullable();
            $table->integer('minimum_items')->nullable();
            $table->integer('maximum_items')->nullable();
            $table->integer('nb_per_user')->nullable();
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();

            $table->timestamps();
        });
        Schema::create('discounts', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();

            $table->string('title_fr', 60);
            $table->string('title_en', 60)->nullable();
            $table->decimal('amount');
            $table->string('type', 25); // ('amount', 'percent', 'free_shipping')

            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();

            $table->timestamps();
        });
        Schema::create('discountables', function (Blueprint $table) {
            $table->unsignedInteger('discount_id');

            $table->unsignedInteger('discountable_id');
            $table->string('discountable_type');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('shippings');
        Schema::dropIfExists('taxes');
        Schema::dropIfExists('orders');
        Schema::dropIfExists('cart_products');
        Schema::dropIfExists('carts');
        Schema::dropIfExists('promo_codes');
        Schema::dropIfExists('discounts');
        Schema::dropIfExists('discountables');
    }
}
