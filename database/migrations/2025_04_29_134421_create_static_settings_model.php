<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('static_settings', function (Blueprint $table) {
            $table->id();
            $table->string('identifiant')->unique();
            $table->text('banner_image')->nullable();
            $table->text('banner_title_fr')->nullable();
            $table->text('banner_title_en')->nullable();
            $table->text('banner_btn_en')->nullable();
            $table->text('banner_btn_fr')->nullable();
            $table->text('banner_link_en')->nullable();
            $table->text('banner_link_fr')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('static_settings');
    }
};
