<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //setup those fields:         'parent_id lft rgt depth' 
        Schema::table('product_categories', function (Blueprint $table) {
            $table->integer('parent_id')->default(0)->nullable()->after('id');
            $table->integer('lft')->default(0)->after('parent_id');
            $table->integer('rgt')->default(0)->after('lft');
            $table->integer('depth')->default(0)->after('rgt');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('product_categories', function (Blueprint $table) {
            $table->dropColumn('parent_id');
            $table->dropColumn('lft');
            $table->dropColumn('rgt');
            $table->dropColumn('depth');
        });
    }
};
