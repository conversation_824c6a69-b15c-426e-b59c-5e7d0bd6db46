<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('user_details', function (Blueprint $table) {
            $table->renameColumn('anniversary_date', 'veteran_quota_date');
            $table->renameColumn('symptome', 'symptoms');
            $table->renameColumn('symptome_other', 'symptoms_other');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('user_details', function (Blueprint $table) {
            $table->renameColumn('veteran_quota_date', 'anniversary_date');
            $table->renameColumn('symptoms', 'symptome');
            $table->renameColumn('symptoms_other', 'symptome_other');
        });
    }
};
