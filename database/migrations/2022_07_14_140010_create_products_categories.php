<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateProductsCategories extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('product_categories', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->string('title_fr');
            $table->string('title_en');
            $table->string('slug_fr')->unique();
            $table->string('slug_en')->unique();

            $table->integer('parent_id')->nullable();
            $table->integer('lft')->nullable();
            $table->integer('rgt')->nullable();
            $table->integer('depth')->nullable();
            $table->timestamps();
        });

        // DEPRECATED - Use related_products instead
        // Schema::create('similar_products', function (Blueprint $table) {
        //     $table->unsignedInteger('id')->autoIncrement();
        //     $table->integer('fk_product_id');
        //     $table->integer('parent_id')->nullable();
        //     $table->integer('lft')->nullable();
        //     $table->integer('rgt')->nullable();
        //     $table->integer('depth')->nullable();
        //     $table->timestamps();
        // });

        Schema::create('evaluations', function (Blueprint $table) {
            $table->unsignedInteger('id')->autoIncrement();
            $table->unsignedInteger('fk_user_id');
            $table->unsignedInteger('fk_product_detail_id');
            $table->double('value');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();
        Schema::dropIfExists('product_categories');
        Schema::dropIfExists('similar_products');
        Schema::dropIfExists('evaluations');
    }
}
