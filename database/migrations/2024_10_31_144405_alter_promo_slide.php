<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('promo_slides', function (Blueprint $table) {

            // Not used anymore with home hero redesign
            $table->dropColumn('title');
            $table->dropColumn('image_m_fr');
            $table->dropColumn('image_m_en');
            $table->dropColumn('link_fr');
            $table->dropColumn('link_en');
            $table->dropColumn('image_en');

            // Rename columns for clarity
            $table->renameColumn('label_fr', 'title_fr');
            $table->renameColumn('label_en', 'title_en');
            $table->renameColumn('image_fr', 'image');
            
            // Add columns
            $table->string('text_fr')->nullable()->after('title_fr');
            $table->string('text_en')->nullable()->after('title_en');
            $table->string('link_fr')->nullable()->after('text_fr');
            $table->string('link_en')->nullable()->after('text_en');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('promo_slides', function (Blueprint $table) {

            //bring back dropped columns
            $table->string('title');
            $table->text('image_en')->nullable();
            $table->text('image_m_en')->nullable();
            $table->text('image_m_fr')->nullable();
            
            // Rename columns for clarity
            $table->renameColumn('title_fr', 'label_fr');
            $table->renameColumn('title_en', 'label_en');
            $table->renameColumn('image', 'image_fr');
            
            // Drop columns
            $table->dropColumn('text_fr');
            $table->dropColumn('text_en');

        });
    }
};
