<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('promo_banners', function (Blueprint $table) {
            $table->text('position')->after('content_en');
            $table->text('icon')->after('position');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('promo_banners', function (Blueprint $table) {
            $table->dropColumn('position');
            $table->dropColumn('icon');
        });
    }
};
