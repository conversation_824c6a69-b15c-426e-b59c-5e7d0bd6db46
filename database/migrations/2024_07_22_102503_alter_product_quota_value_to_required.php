<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('product_regulars', function (Blueprint $table) {
            $table->double('quota_value')->nullable(false)->change();
        });

        Schema::table('product_clones', function (Blueprint $table) {
            $table->double('quota_value')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('product_regulars', function (Blueprint $table) {
            $table->double('quota_value')->nullable()->change();
        });

        Schema::table('product_clones', function (Blueprint $table) {
            $table->double('quota_value')->nullable()->change();
        });
    }
};
