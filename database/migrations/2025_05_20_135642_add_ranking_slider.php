<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        //change promo banner table
        Schema::table('promo_slides', function (Blueprint $table) {
            $table->unsignedBigInteger('parent_id')->nullable(); // Or choose a different position
            $table->integer('lft')->default(0);
            $table->integer('rgt')->default(0);
            $table->integer('depth')->default(0);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('promo_slides', function (Blueprint $table) {
            $table->dropColumn(['parent_id', 'lft', 'rgt', 'depth']);
        });
    }
};
