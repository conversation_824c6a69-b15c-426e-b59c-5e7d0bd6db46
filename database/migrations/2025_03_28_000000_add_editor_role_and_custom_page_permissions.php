<?php

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Migrations\Migration;
use Backpack\PermissionManager\app\Models\Role;
use Backpack\PermissionManager\app\Models\Permission;

class AddEditorRoleAndCustomPagePermissions extends Migration
{
    public function up(): void
    {
        // Étape 1 : Ajouter le rôle "editor" s'il n'existe pas
        // Step 1: Add the "editor" role if it doesn't exist
        $editorRole = Role::where('name', 'editor')->first();
        if (!$editorRole) {
            $editorRole = Role::create([
                'name' => 'editor',
                'guard_name' => 'backpack',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now()
            ]);
        }

        // Étape 2 : Ajouter les permissions custom-page.* si elles n'existent pas
        $permissions = [
            'custom-page.list',
            'custom-page.create',
            'custom-page.update',
            'custom-page.delete'
        ];

        foreach ($permissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if (!$permission) {
                Permission::create([
                    'name' => $permissionName,
                    'guard_name' => 'backpack',
                    'created_at' => Carbon::now(),
                    'updated_at' => Carbon::now()
                ]);
            }
        }

        // Étape 3 : Lier les permissions au rôle editor si ce n'est pas déjà fait
        $editorRole = Role::where('name', 'editor')->first();
        foreach ($permissions as $permissionName) {
            $permission = Permission::where('name', $permissionName)->first();
            if ($permission && !$editorRole->hasPermissionTo($permission)) {
                $editorRole->givePermissionTo($permission);
            }
        }
    }

    public function down(): void
    {
        $editorRole = Role::where('name', 'editor')->first();
        if ($editorRole) {
            $permissions = [
                'custom-page.list',
                'custom-page.create',
                'custom-page.update',
                'custom-page.delete'
            ];
            
            foreach ($permissions as $permissionName) {
                $permission = Permission::where('name', $permissionName)->first();
                if ($permission) {
                    $editorRole->revokePermissionTo($permission);
                    $permission->delete();
                }
            }
            $editorRole->delete();
        }
    }
}
