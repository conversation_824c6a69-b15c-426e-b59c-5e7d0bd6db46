<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->boolean('is_recurring')->default(false);
            $table->boolean('is_active')->default(false);
            $table->text('recurring_days')->nullable(); // Store as JSON array of days
            $table->time('recurring_start_time')->nullable();
            $table->time('recurring_end_time')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('discounts', function (Blueprint $table) {
            $table->dropColumn(['is_recurring','is_active','recurring_days', 'recurring_start_time', 'recurring_end_time']);

        });
    }
};
