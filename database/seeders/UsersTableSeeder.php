<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UsersTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        //
        // $admin = User::create([
        //     'firstname' => "Admin",
        //     'lastname'  => "Kryzalid",
        //     'email'     => "<EMAIL>",
        //     'password'  => Hash::make('E@DELi6fPchU')
        // ]);
        // $admin->assignRole('superadmin');

        // $user = User::create([
        //     'firstname' => "Rubens",
        //     'lastname'  => "Sitruk",
        //     'email'     => "<EMAIL>",
        //     'password'  => Hash::make('BD8zbtAdf_2_2_2')
        // ]);
        // $user->assignRole('admin');

        // $client = User::create([
        //     'firstname' => "<PERSON>",
        //     'lastname'  => "<PERSON>",
        //     'email'     => "bob<PERSON><EMAIL>",
        //     'password'  => Hash::make('EFZLc!hrwoFNP6'),
        //     'status'    => 'active',
        // ]);
        // $client->userdetail()->create([
        //     'teedy_client_id'         => 'RNM' . $client->id,
        //     'phone'                   => '1112223344',
        //     'birth_date'              => '1981-05-11',
        //     'language'                => 'en',
        //     'veteran'                 => 1,
        //     'quota_veteran_remaining' => '50',
        //     'quota_veteran_allowed'   => '100',
        //     'licence_acmpr'           => 1,
        //     'gender'                  => 'm',
        //     'symptome'                => ['chronic-pain'],
        //     'symptome_other'          => 'Cancer',
        //     'consumption'             => ['inhale-smoking'],
        // ]);
        // $client->addresses()->create([
        //     'address'  => 'porte 1, Mosolé au font du terrain',
        //     'city'     => 'Rhoden Hall',
        //     'pc'       => 'H1H2K2',
        //     'province' => 'qc',
        //     'delivery' => '1',
        //     'billing'  => '1',
        //     'last_use' => '1',
        // ]);
        // $client->assignRole('client');

        // $client = User::create([
        //     'firstname' => 'Calvin Cordozar',
        //     'lastname'  => 'Broadus',
        //     'email'     => '<EMAIL>',
        //     'password'  => Hash::make('%59vO6d^aj3f'),
        //     'status'    => 'pending_no_prescription',
        // ]);
        // $client->userdetail()->create([
        //     'teedy_client_id' => 'CVB' . $client->id,
        //     'phone'           => '5556667788',
        //     'birth_date'      => '1971-10-20',
        //     'language'        => 'en',
        //     // 'quota_user'      => 2988,
        //     'veteran'         => 0,
        //     'licence_acmpr'   => 0,
        //     'gender'          => 'm',
        // ]);
        // $client->addresses()->create([
        //     'address'  => '123 Avenue de la rue Long Beach',
        //     'city'     => 'Los Angeles',
        //     'pc'       => 'H9K1K1',
        //     'province' => 'qc',
        //     'delivery' => '1',
        //     'billing'  => '1',
        //     'last_use' => '1',
        // ]);
        // $client->assignRole('client');
    }
}
