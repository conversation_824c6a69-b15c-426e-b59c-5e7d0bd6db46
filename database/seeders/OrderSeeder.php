<?php

namespace Database\Seeders;

use App\Models\Shipping;
use App\Models\Taxe;
use App\Models\PromoCode;
use App\Models\Discount;
use App\Models\Producer;
use App\Models\ProductDetail;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        Taxe::create([
            'code' => 'TPS',
            'value' => 5,
            'country' => 'ca',
            'add_existance_tax' => false
        ]);
        Taxe::create([
            'code'              => 'TVQ',
            'value'             => 9.975,
            'country'           => 'ca',
            'province'          => 'qc',
            'add_existance_tax' => true
        ]);

        Shipping::create([
            'title_fr'     => 'Livraison en 1h',
            'title_en'     => 'Shipping in 1h',
            'price'        => 0,
            'type'         => 'free',
            'country'      => 'ca',
            'province'     => 'qc',
            'postal_codes' => null,
            'delivery_type' => '1h'
        ]);
        Shipping::create([
            'title_fr'      => 'Livraison en 2h',
            'title_en'      => 'Shipping in 2h',
            'price'         => 7,
            'type'          => 'fixed',
            'country'       => 'ca',
            'province'      => 'qc',
            'postal_codes'  => 'H4A, H9G, H9A, H3H, H9H, H9B, H4J, H9J, H9C, H4K, H9K, H3L, H4L, H3R, H4R, H2M, H3M, H9R, H4M, H3S, H2N, H4S, H4N, H8S, H9S, H3T, H3P, H4T, H4P, H8T, H9P, H2V, H3V, H4V, H3W, H4W, H9W, H9X, H3Y, H4Y, H8Y, H3Z, H8Z, H7W, H2J, H2K, H4B, H3X, J6Z, J7W, J7V, H4Z, H2Z, H4C, H2Y, H2X, H2T, H4E, H3E, H3K, H3C, H2C, H3J, H5B, H4H, H3B, H2B, H5A, H4G, H3A, H3G, H1A, H1G, H2A, H2G, H7G, H1H, H1B, H2H, H1J, H7T, H7C, H1K, H2E, H7K, H7E, H1L, H9E, H1R, H2R, H7L, H1M, H7R, H8R, H1S, H7M, H2S, H1N, H7S, H7N, H8N, H1T, H1P, H2P, H7P, H8P, H1V, H7V, H1W, H2W, H1X, H7X, H1Y, H7Y, H1Z, J4G, J7G, J4H, J7H, J4J, J4K, J4L, J4M, J4N, J4V, J4W, J4X, H3N, H1E, J3Y, H1C, J4Y, J4T, J5B, J5A, J5R, J4Z, J6J, J6K, J5C, H7J, J4R',
            'delivery_type' => '2h'
        ]);
        Shipping::create([
            'title_fr'      => 'Livraison en 24h',
            'title_en'      => 'Shipping in 24h',
            'price'         => 15,
            'type'          => 'percent',
            'country'       => 'ca',
            'postal_codes'  => 'G8H, J8Z, J7Z, J3Z, J6Y, J7X, J6X, J5X, J6W, J7T, J6S, J6R, J4P, J7N, J2M, J7P, J3L, J7K, J0L, J7J, J7E, J3E, J7C, J7B, J4B, J7A, H7B, H7H, H7A, J0A, J1A, J2A, J6A, J8A, J9A, J0B, J2B, H2L, J3B, J8B, J7R, J9B, J0C, J1C, J2C, J8C, J0E, J1E, J2E, J6E, J8E, J9E, J0G, J1G, J2G, J3G, J8G, J0H, J1H, J2H, J3H, J8H, J9H, J0J, J1J, J2J, J5J, J9J, J0K, J1K, J2K, J5K, J1L, J2L, J5L, J7L, J8L, J9L, J1M, J3M, J5M, J7M, J8M, J0N, J1N, J2N, J3N, J6N, J8N, J0P, J3P, J8P, J9P, J0R, J1R, J2R, J3R, J8R, J0S, J1S, J2S, J4S, J0T, J1T, J2T, J3T, J5T, J6T, J8T, J9T, J0V, J3V, J5V, J6V, J8V, J9V, J0W, J2W, J5W, J0X, J1X, J2X, J3X, J8X, J9X, J0Y, J2Y, J5Y, J7Y, J8Y, J9Y, J0Z, J1Z, J5Z, J9Z, G0A, G1A, G2A, G3A, G4A, G5A, G6A, G7A, G8A, G9A, G1B, G2B, G3B, G5B, G6B, G7B, G8B, G9B, G0C, G1C, G2C, G3C, G5C, G6C, G8C, G9C, G0E, G1E, G2E, G3E, G6E, G8E, G0G, G1G, G2G, G3G, G6G, G7G, G8G, G0H, G1H, G0J, G1J, G2J, G3J, G5J, G6J, G7J, G8J, G0K, G1K, G2K, G3K, G6K, G7K, G8K, G0L, G1L, G2L, G3L, G5L, G6L, G8L, G0M, G1M, G2M, G3M, G5M, G8M, G0N, G1N, G2N, G3N, G5N, G7N, G8N, G9N, G0P, G1P, G6P, G7P, G8P, G9P, G0R, G1R, G4R, G5R, G6R, G9R, G0S, G1S, G3S, G4S, G6S, G7S, G0T, G1T, G4T, G5T, G6T, G7T, G8T, G9T, G0V, G1V, G4V, G5V, G6V, G8V, G0W, G1W, G4W, G6W, G8W, G0X, G1X, G4X, G5X, G6X, G7X, G9X, G0Y, G1Y, G5Y, G7Y, G8Y, G0Z, G3Z, G4Z, G5Z, G6Z, G7Z, G8Z, G9H, G3H, G5H, G6H, G7H, G8G, H4X, J3A, J0M, G6Y, L0A, L1A, L2A, L4A, L5A, L6A, L7A, L9A, L0B, L1B, L3B, L4B, L5B, L6B, L7B, L9B, L0C, L1C, L3C, L4C, L5C, L6C, L7C, L9C, L0E, L1E, L2E, L4E, L5E, L6E, L7E, L8E, L0G, L1G, L4G, L5G, L6G, L7G, L8G, L9G, L9H, L1H, L2H, L4H, L5H, L6H, L8H, L0J, L1J, L2J, L4J, L5J, L6J, L7J, L8J, L0K, L1K, L3K, L4K, L5K, L6K, L7K, L8K, L9K, L0L, L1L, L4L, L5L, L6L, L7L, L8L, L9L, L0M, L1M, L2M, L3M, L4M, L5M, L6M, L7M, L8M, L9M, L0N, L1N, L2N, L4N, L5N, L7N, L8N, L9N, L0P, L1P, L2P, L3P, L4P, L5P, L6P, L7P, L8P, L9P, L0R, L1R, L2R, L3R, L4R, L5R, L6R, L7R, L8R, L9R, L0S, L1S, L2S, L3S, L4S, L5S, L6S, L7S, L8S, L9S, L1T, L2T, L3T, L4T, L5T, L6T, L7T, L8T, L9T, L1V, L2V, L3V, L4V, L5V, L6V, L9V, L1W, L2W, L4W, L5W, L6W, L8W, L9W, L1X, L3X, L4X, L6X, L1Y, L3Y, L4Y, L6Y, L9Y, L1Z, L3Z, L4Z, L6Z, L9Z, K0A, K1A, K2A, K4A, K6A, K7A, K8A, K9A, K0B, K1B, K2B, K4B, K8B, K4C, K7C, K0E, K1E, K2E, K0G, K1G, K2G, K0C, K1C, K2C, M1P, M2P, M4P, M5P, M6P, M9P, M1R, M2R, M4R, M5R, M6R, M9R, N0H, N1H, N2H, N3H, N5H, N6H, N8H, N9H, N1R, N2R, N3R, N5R, N8R, N8X, N3Y, N5Y, N8Y, N9Y, P0A, P1A, P2A, P3A, P5A, P6A, P7A, P9A, K7G, K0H, K1H, K2H, K6H, K7H, K8H, K9H, K0J, K1J, K2J, K6J, K9J, K0K, K1K, K2K, K4K, K6K, K7K, K9K, K0L, K1L, K2L, K7L, K9L, M1S, M4S, M5S, M6S, M1T, M4T, M5T, M1V, M4V, M5V, M8V, M9V, N0J, N2J, N6J, N9J, N0K, N1K, N2K, N1S, N3S, N4S, N7S, N8S, N2Z, N4Z, N5Z, P0B, P1B, P2B, P3B, P6B, P7B, P0C, P1C, P3C, P6C, K0M, K1M, K2M, K4M, K7M, K1N, K7N, K8N, K1P, K2P, K4P, K7P, K8P, K1R, K2R, K4R, K7R, K8R, K1S, K2S, K7S, K1T, K2T, K6T, K1V, M1W, M4W, M5W, M8W, M9W, M1X, M4X, M5X, M8X, M4Y, M7Y, M8Y, N4K, N6K, N9K, N0L, N1L, N2L, N3L, N1T, N2T, N3T, N4T, N7T, P7C, P0E, P3E, P5E, P7E, P0G, P3G, P7G, P0H, P1H, P0J, P7J, P0K, K2V, K6V, K7V, K8V, K9V, K1W, K2W, K1X, K1Y, K1Z, M3A, M4A, M5A, M6A, M7A, M9A, M1B, M3B, M4B, M5B, M6B, M9B, M1C, M3C, M4C, M8Z, N0A, N1A, N2A, N5A, N7A, N8A, N9A, N0B, N2B, N3B, N4L, N5L, N6L, N7L, N0M, N1M, N2M, N8T, N2V, N3V, N4V, N5V, P7K, P0L, P1L, P3L, P7L, P0M, P0N, P2N, P3N, P4N, P5N, P8N, P9N, M5C, M6C, M9C, M1E, M4E, M5E, M6E, M1G, M4G, M5G, M6G, M1H, M2H, M3H, M4H, M5H, M6H, M1J, M2J, M3J, M4J, M5J, M6J, M1K, M2K, N4B, N6B, N9B, N0C, N1C, N2C, N3C, N5C, N6C, N9C, N0E, N1E, N6M, N7M, N8N, N2N, N4N, N6N, N7V, N8V, N9V, N3W, N4W, P0P, P1P, P3P, P4P, P0R, P4R, P0S, P0T, P8T, P0V, P0W, P0X, P0Y, M3K, M4K, M5K, M1L, M2L, M3L, M4L, M5L, M6L, M9L, M1M, M2M, M3M, M4M, M5M, M6M, M9M, M1N, M2N, M3N, M4N, M5N, M6N, M9N, N2E, N3E, N6E, N9E, N0G, N1G, N2G, N4G, N6G, N7G, N9G, N0P, N1P, N2P, N3P, N5P, N6P, N8P, N0R, N5W, N7W, N8W, N4X, N5X, N7X, P3Y',
            'delivery_type' => '24h'
        ]);

        PromoCode::create([
            'code'          => 'FREE-SHIPPING-UNDER-250',
            'amount'        => 10,
            'type'          => 'free_shipping',
            'nb_per_user'   => 1,
            'maximum_price' => 250,
        ]);
        PromoCode::create([
            'code'          => 'NO-TAX-THIS-YEAR',
            'amount'        => 15,
            'type'          => 'percent',
            'end_date'      => '2023-01-01',
        ]);
        PromoCode::create([
            'code'          => 'WEEKEND-SPECIAL-100+',
            'amount'        => 25,
            'type'          => 'amount',
            'minimum_price' => 100,
            'start_date'    => '2022-10-28',
            'end_date'      => '2022-10-30',
        ]);

        $discount1 = Discount::create([
            'title_fr'      => '10% sur Bud LaFleur jusqu\'en février',
            'amount'        => '10',
            'type'          => 'percent',
            'end_date'      => '2023-10-01'
        ]);
        $discount1->producers()->attach(Producer::first());

        $discount2 = Discount::create([
            'title_fr'      => '5$ sur Black Sunset',
            'amount'        => '5',
            'type'          => 'amount',
        ]);
        $discount2->products()->attach(ProductDetail::first());
    }
}
