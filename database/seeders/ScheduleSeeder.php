<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Schedule;
use App\Models\Holiday;
use Carbon\Carbon;

class ScheduleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create a delivery schedule from 10am to 7pm every day, except on weekends
        for ($d = 0; $d < 7; $d++) {
            for ($h = 6; $h < 24; $h++) {
                Schedule::create([
                    'day' => $d, 
                    'hour' => $h, 
                    'enabled' => ($d !== 0 && $d !== 6 && $h >= 10 and $h < 19)
                ]);
            }
        }

        // Holidays are skipped in the schedule API response
        Holiday::create([
            'title_fr' => 'Special day',
            'date' => Carbon::now()->addDay(),
            'active' => true
        ]);
    }
}
