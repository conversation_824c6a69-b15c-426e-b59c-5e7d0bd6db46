<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class TermsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::insert('insert into terms (type, name_fr, name_en) values (?, ?, ?)', ['terpene', 'Carène', 'Carene']); 
        DB::insert('insert into terms (type, name_fr, name_en) values (?, ?, ?)', ['terpene', 'Linalol', 'Linalol']); 

        DB::insert('insert into terms (type, name_fr, name_en) values (?, ?, ?)', ['aroma', 'Agrumes', 'Citrus']); 
        DB::insert('insert into terms (type, name_fr, name_en) values (?, ?, ?)', ['aroma', 'Boisé', 'Woody']); 
    }
}
