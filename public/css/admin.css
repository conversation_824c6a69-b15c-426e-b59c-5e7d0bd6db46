.app-header {
  background-color: #176935 !important;
}
.app-header a.nav-link.dropdown-toggle {
  color: #fff !important;
}
.app-header a.nav-link.dropdown-toggle:hover {
  color: #fff;
  opacity: 0.8;
}

.app-header.bg-light .navbar-brand {
  background-color: #fff;
  opacity: 1;
  transition: all 0.2s;
}
.app-header.bg-light .navbar-brand img {
  margin: 0 auto;
}
.app-header.bg-light .navbar-brand:hover {
  opacity: 0.9;
}

a, .btn-link {
  color: #1c7f40;
}
a:hover, .btn-link:hover {
  color: #176935;
}

.btn-primary {
  background-color: #176935;
  border-color: #176935;
  border-radius: 30px;
}
.btn-primary:hover {
  background-color: #1c7f40;
  border-color: #1c7f40;
}

.btn-primary, .page-item.active .page-link {
  background-color: #176935;
  border-color: #176935;
}
.btn-primary:hover, .page-item.active .page-link:hover {
  background-color: #1c7f40;
  border-color: #1c7f40;
}

.sidebar.sidebar-pills a.nav-link, .sidebar.sidebar-pills .nav-icon {
  color: #1c7f40 !important;
}
.sidebar.sidebar-pills a.nav-link:hover, .sidebar.sidebar-pills .nav-icon:hover {
  color: #176935 !important;
}
.sidebar.sidebar-pills .nav-item.open a.nav-link {
  color: #176935 !important;
}
.sidebar.sidebar-pills .nav-item.open a.nav-link:hover {
  color: #1c7f40 !important;
}
.sidebar.sidebar-pills .nav-item.open a.nav-link i.nav-icon {
  color: #176935 !important;
}
.sidebar.sidebar-pills .nav-item.open a.nav-link i.nav-icon:hover {
  color: #1c7f40 !important;
}
.sidebar.sidebar-pills hr {
  border-color: rgba(28, 127, 64, 0.2);
  width: 100%;
}

.bg-success {
  background-color: #176935 !important;
}
.bg-success a.nav-link, .bg-success .nav-icon {
  color: #fff !important;
}
.bg-success a.nav-link:hover, .bg-success .nav-icon:hover {
  color: #1c7f40 !important;
}
.bg-success a.nav-link i.nav-icon, .bg-success .nav-icon i.nav-icon {
  color: #fff !important;
}
.bg-success a.nav-link i.nav-icon:hover, .bg-success .nav-icon i.nav-icon:hover {
  color: #1c7f40 !important;
}

.btn-outline-primary {
  color: #176935 !important;
  border-color: #176935 !important;
}
.btn-outline-primary:hover {
  background-color: #176935 !important;
}
.btn-outline-primary a.nav-link, .btn-outline-primary .nav-icon {
  color: #fff !important;
  background-color: #176935 !important;
  border-color: #176935 !important;
}
.btn-outline-primary a.nav-link:hover, .btn-outline-primary .nav-icon:hover {
  color: #1c7f40 !important;
}
.btn-outline-primary a.nav-link i.nav-icon, .btn-outline-primary .nav-icon i.nav-icon {
  color: #fff !important;
}
.btn-outline-primary a.nav-link i.nav-icon:hover, .btn-outline-primary .nav-icon i.nav-icon:hover {
  color: #1c7f40 !important;
}

.pace .pace-progress {
  background-color: #1c7f40 !important;
}

.form-control:focus {
  border-color: #176935;
  box-shadow: 0 0 0 1px #176935;
}

.dropdown-item.active, .dropdown-item:active {
  background-color: transparent;
  color: #176935;
}
.dropdown-item.active i.nav-icon, .dropdown-item:active i.nav-icon {
  color: #176935 !important;
}
.dropdown-item.active i.nav-icon:hover, .dropdown-item:active i.nav-icon:hover {
  color: #1c7f40 !important;
}

input[readonly=readonly] {
  background-color: #f8f9fa;
  color: #495057;
  cursor: not-allowed;
}
