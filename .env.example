APP_NAME=Teedy
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost:8000

SEND_MAIL=true

CAPTCHA_SECRET=6Le1ThsbAAAAAMUaYxh6pI5SDgpPh3Pr-wbjDy3_

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=teedy
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# kryzalid mailtrap
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME=Teedy

# mailchimp
NEWSLETTER_DRIVER=api
NEWSLETTER_API_KEY=
NEWSLETTER_LIST_ID=
NEWSLETTER_LIST_INTEREST_ID_1=
NEWSLETTER_LIST_INTEREST_ID_2=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

FRONT_URL_FR=http://localhost:4200
FRONT_URL_EN=http://localhost:4200
FRONT_URL_RESET_PASSWORD_FR=https://teedy.kryzastage2.com/fr/retrouver-mot-de-passe
FRONT_URL_RESET_PASSWORD_EN=https://teedy.kryzastage2.com/en/recover-password

MEDICAL_OCEAN_URL="https://ocean.cognisantmd.com/intake/IntakePortal.html?eReqRef=a06fb2a0-2a5e-46dc-b2d5-2d9f18e99d2e"

# Prod
MONERIS_STORE_ID=
MONERIS_API_TOKEN=
MONERIS_ECR_NUMBER=

# test account
MONERIS_STORE_ID_TEST=
MONERIS_API_TOKEN_TEST=
MONERIS_ECR_NUMBER_TEST=

# this will prevent to finalize an order
MONERIS_SYNC=false

# IMPORTANT: This needs to be true when testing Moneris
MONERIS_TEST_MODE=true

# For activate sync in your environment
SYNC_BLUELINK=false
# PROD
# BL_HOST=
# BL_API_KEY=
# DEV
BL_HOST=
BL_API_KEY=

SENTRY_LARAVEL_DSN=
SENTRY_TRACES_SAMPLE_RATE=1.0
VITE_SENTRY_DSN_PUBLIC="${SENTRY_LARAVEL_DSN}"
