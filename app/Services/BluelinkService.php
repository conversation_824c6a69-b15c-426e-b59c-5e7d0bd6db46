<?php

namespace App\Services;

use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class BluelinkService
{
    private $api_key = '';
    private $url_api = '';

    public function __construct()
    {
        $this->url_api = config('bluelinkerp.host');
        $this->api_key = config('bluelinkerp.api_key');
        $this->client = new Client([
            'base_uri' => $this->url_api . '/',
            'headers' => [
                'Authorization' => 'apikey ' . $this->api_key,
                'Content-Type' => 'application/json; charset=UTF-8',
                'Accept' => 'application/json; charset=UTF-8',
                'Accept-Charset' => 'UTF-8',
            ],
            'debug' => env('APP_DEBUG_GUZZLE', false)
        ]);
    }

    /**
     * Create url request and execute the client call
     *
     * @param  string $methode      GET, POST, PUT, DELETE, PATCH
     * @param  string $api_point    the end point API
     * @param  array  $query_string Param api in query sting
     * @param  array  $body         Param api in body of request
     * @return void
     */
    public function createUrl(string $methode, string $api_point, array $query_string = [], array $body = [])
    {
        $url = $api_point;
        $cpt = 0;
        // Create query_string param for request
        foreach (array_filter($query_string) as $key => $value) {
            // Add ? or & for request AND $ because param start with this for bluelink
            if ($cpt == 0) {
                $url .= '?$' . $key . '=' . $value;
            } else {
                $url .= '&$' . $key . '=' . $value;
            }
            $cpt++;
        }
        $request_body = [];
        // Generate json body for post and put request
        if (!empty($body)) {
            $request_body = [
                'json' => array_filter($body),
                'headers' => [
                    'Content-Type' => 'application/json; charset=UTF-8'
                ],
                'options' => [
                    'json_encode_options' => JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES
                ]
            ];
        }
        // execute the query
        try {
            $response = $this->client->request($methode, $url, $request_body);
            
            $result = $response->getBody();
            return json_decode($result);
        } catch (\Throwable $th) {
            Log::channel('daily')->error($th);
            abort('500', $th->getMessage());
        }
    }
}
