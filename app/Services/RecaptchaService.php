<?php
namespace App\Services;

use Illuminate\Support\Facades\Http;

class RecaptchaService
{
    public function verifyRecaptcha($token)
    {
        $secret = env('CAPTCHA_SECRET');
        if (empty($secret)) {
            return false;
        }

        $response = Http::asForm()->post('https://www.google.com/recaptcha/api/siteverify', [
            'secret' => $secret,
            'response' => $token,
        ]);

        
        $body = json_decode(str_replace('error-codes', 'error_codes', $response->getBody()));
        $googleMessage = isset($body->error_codes) ? $body->error_codes[0] : 'N/A';
        return [
            'code'    => (!$body->success || ($body->success && $body->score < 0.5)) ? 500 : 200,
            "message" => __('api.user.recaptcha-failed', ['google-message' => $googleMessage]),
            'data'    => $body,
        ];
    }
}