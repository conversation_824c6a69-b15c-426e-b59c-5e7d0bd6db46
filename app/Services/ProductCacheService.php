<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Jobs\WarmProductCache;

class ProductCacheService
{
    /**
     * Refresh product cache for a specific region and language
     * This method clears cache and marks it for warming
     * 
     * @param string $region The region code (e.g., 'QC')
     * @param string|array $languages Single language code or array of language codes
     * @return void
     */
    public static function refreshCache($region = null, $languages = ['fr', 'en'])
    {
        if ($region) {
            // Clear and mark cache for warming for specific region
            if (!is_array($languages)) {
                $languages = [$languages];
            }
            
            $region = strtoupper($region);
            
            foreach ($languages as $lang) {
                $lang = strtoupper($lang);
                $cacheKey = "products_{$region}_{$lang}";
                
                // Clear the existing cache
                Cache::forget($cacheKey);
                Log::channel('cache')->info('Product cache invalidated', ['key' => $cacheKey]);
                
                // Warm cache AFTER response is sent to browser (so CRUD doesn't get stuck)
                dispatch(function () use ($region, $lang) {
                    self::warmCacheNow($region, $lang);
                })->afterResponse();
            }
        } else {
            // Clear and warm cache for all regions
            self::refreshAllCache();
        }
    }
    
    /**
     * Clear and warm product cache for all regions and languages
     * 
     * @return void
     */
    public static function refreshAllCache()
    {
        $regions = ['QC', 'ON'];
        $languages = ['fr', 'en'];
        
        foreach ($regions as $region) {
            foreach ($languages as $lang) {
                $lang = strtoupper($lang);
                $region = strtoupper($region);
                $cacheKey = "products_{$region}_{$lang}";
                
                // Clear the existing cache
                Cache::forget($cacheKey);
                Log::channel('cache')->info('Product cache invalidated', ['key' => $cacheKey]);
                
                // Warm cache AFTER response is sent to browser (so CRUD doesn't get stuck)
                dispatch(function () use ($region, $lang) {
                    self::warmCacheNow($region, $lang);
                })->afterResponse();
            }
        }
        
        Log::channel('cache')->info('All product caches refreshed');
    }

    /**
     * Warm cache if needed based on TTL check
     * Run this method via cron every minute
     * 
     * @param string $region
     * @param string $lang
     * @param bool $force Force warming regardless of TTL
     * @return void
     */
    public static function warmCacheIfNeeded($region = null, $lang = null, $force = false)
    {
        if ($region && $lang) {
            // Warm specific cache
            self::checkAndWarmCache($region, $lang, $force);
        } else {
            // Warm all caches
            $regions = ['QC', 'ON'];
            $languages = ['FR', 'EN'];
            
            foreach ($regions as $region) {
                foreach ($languages as $lang) {
                    self::checkAndWarmCache($region, $lang, $force);
                }
            }
        }
    }

    /**
     * Check cache TTL and warm if needed
     * 
     * @param string $region
     * @param string $lang
     * @param bool $force
     * @return void
     */
    private static function checkAndWarmCache($region, $lang, $force = false)
    {
        $cacheKey = "products_{$region}_{$lang}";
        $metaKey = "products_{$region}_{$lang}_meta";
        
        // Get cache metadata
        $meta = Cache::get($metaKey);
        
        if (!$force && $meta) {
            $cacheTime = \Carbon\Carbon::parse($meta['cached_at']);
            $minutesOld = $cacheTime->diffInMinutes(now());
            
            // Only warm if cache is older than 9 minutes (1 minute before 10min expiry)
            if ($minutesOld < 9) {
                Log::channel('cache')->info('Cache still fresh, skipping warm', [
                    'key' => $cacheKey,
                    'minutes_old' => $minutesOld
                ]);
                return;
            }
        }
        
        // Check if cache exists, if not or if it's old, warm it
        if ($force || !Cache::has($cacheKey) || ($meta && \Carbon\Carbon::parse($meta['cached_at'])->diffInMinutes(now()) >= 9)) {
            Log::channel('cache')->info('Warming cache', ['key' => $cacheKey, 'force' => $force]);
            
            // Use the WarmProductCache job logic directly
            $job = new WarmProductCache($region, $lang);
            $job->handle();
        }
    }
    
    /**
     * Warm cache immediately without TTL check
     * Used when data changes and cache must be refreshed
     * 
     * @param string $region
     * @param string $lang
     * @return void
     */
    private static function warmCacheNow($region, $lang)
    {
        Log::channel('cache')->info('Warming cache immediately', ['region' => $region, 'lang' => $lang]);
        
        // Use the WarmProductCache job logic directly
        $job = new WarmProductCache($region, $lang);
        $job->handle();
    }
}