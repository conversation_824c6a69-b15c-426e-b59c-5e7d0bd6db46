<?php

use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManagerStatic as Image;
use Illuminate\Http\UploadedFile;

function saveFile($img_data, $file_name, $category)
{
    $disk = 'public';
    $path = null;
    $file_name = Str::slug($file_name) . '_' . uniqid();

    $image_sizes = [
        'products' => 992,
        'producers-logo' => 150,
        'producers-banner' => 2400,
        'identities' => 150,
        'prescriptions' => 150,
        'promo-slides' => 2400,
        'pages' => 2400,
        'certificat' => 2400,
    ];

    // If file comes from an Upload type field, simply store it
    if ($img_data instanceof UploadedFile) {
        $path = $img_data->storeAs($disk . '/' . $category, $file_name . '.' . $img_data->extension());
        $path = str_replace('public/', '', $path);
    }

    // If file comes from an Image type field (base64)
    else if (is_string($img_data)) {
        if (Str::startsWith($img_data, 'data:image')) {
            // Create image file
            $image_file = Image::make($img_data)
                ->encode('png', 100)
                ->resize($image_sizes[$category], null, fn ($i) => $i->aspectRatio())
                ->stream();

            // Save on disk
            $path = $category . '/' . Str::slug($file_name) . '_' . uniqid() . '.png';
            Storage::disk('public')->put($path, $image_file);
        }

        // If not a base64 data, we assume it's a path to storage, making sure the path is relative
        else if (str_contains($img_data, '/storage/')) [$url, $path] = explode('/storage/', $img_data);
    }

    return $path;
}
