<?php

namespace App\Console\Commands;

use App\Models\ProductDetail;
use Illuminate\Console\Command;

class ResetFeaturedProductList extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reset-featured-product-list';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //get all product details and set featured to 0
        $products = ProductDetail::all();
        foreach ($products as $product) {
            $product->update(['featured' => 0]);
        }
    }
}
