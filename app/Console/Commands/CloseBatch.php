<?php

namespace App\Console\Commands;

use Exception;

use App\Models\Order;
use Illuminate\Console\Command;
use illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class CloseBatch extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'teedy:close-batch';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Close batch process';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Scheduled task started.');
        try {
            if (env('APP_ENV') === 'local') {
                $store_id = env('MONERIS_STORE_ID_TEST');
                $api_token = env('MONERIS_API_TOKEN_TEST');
                $ecr_number = env('MONERIS_ECR_NUMBER_TEST');
            } else {
                $store_id = env('MONERIS_STORE_ID');
                $api_token = env('MONERIS_API_TOKEN');
                $ecr_number = env('MONERIS_ECR_NUMBER');
            }

            ## step 1) create transaction array ###
            $txnArray = array(
                'type' => 'batchclose',
                'ecr_number' => $ecr_number
            );
            $mpgTxn = new \mpgTransaction($txnArray);

            ## step 2) create mpgRequest object ###
            $mpgReq = new \mpgRequest($mpgTxn);
            $mpgReq->setProcCountryCode("CA"); //"US" for sending transaction to US environment
            $mpgReq->setTestMode(env('MONERIS_TEST_MODE'));

            ## step 3) create mpgHttpsPost object which does an https post ##
            $mpgHttpPost = new \mpgHttpsPost($store_id,$api_token,$mpgReq);

            ## step 4) get an mpgResponse object ##
            $mpgResponse = $mpgHttpPost->getMpgResponse();

            Order::where('batch_closed', false)
            ->update(['batch_closed' => true]);

            Log::info('success');
        } catch (Exception $e) {
            Log::info('Error closing transactions: ' . $e->getMessage());
        }

        $this->info('Scheduled task finished.');
        return Command::SUCCESS;
    }
}
