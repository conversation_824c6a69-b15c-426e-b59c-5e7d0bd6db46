<?php

namespace App\Console\Commands;

use App\Models\User;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class ImportUsers extends Command
{
    // --reset: Run migration refresh and seed before import
    // --api: Use production API endpoints instead of local json files
    protected $signature = 'import:users {--reset} {--api}';
    protected $description = 'Import all users from V1 admin';

    //
    private $users_file = 'export/users_all.json';
    private $users_url;

    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');

        // Set files sources
        $api = $this->option('api');
        $this->users_url = $api
            ? 'https://api.teedy.com/fr/api/v1-export/users'
            : __DIR__ . '/' . $this->users_file;

        // ------------------ MIGRATIONS ------------------

        // Reset DB and seed producers if specified in command
        if ($this->option('reset')) {
            Artisan::call('migrate:refresh --seed');
            (new Filesystem)->cleanDirectory('storage/app/public/identities');
            (new Filesystem)->cleanDirectory('storage/app/public/prescriptions');
            $this->info('DB reset complete');
        }

        // ------------------ USERS IMPORT ------------------

        // Import regular products
        $jsonData = file_get_contents($this->users_url);
        $json = json_decode($jsonData);

        foreach ($json->results as $u) {
            // $this->info('-----');

            // ------------------ BASE USER ------------------

            // Base user model
            $attrs = ['email', 'archived', 'status', 'created_at', 'updated_at'];
            $new_user_data = array_intersect_key((array) $u, array_flip($attrs));

            // Fix names capitalization
            $new_user_data['firstname'] = $this->capitalize($u->firstname);
            $new_user_data['lastname'] = $this->capitalize($u->lastname);

            // Add password hash
            $new_user_data['password'] = $u->password_str;

            // Download identity proof
            $proof_path = $this->downloadFile($u->proof_identity, 'identities', Str::slug($new_user_data['firstname'] . ' ' . $new_user_data['lastname']));

            // Remove the proof_identity from attributes that will go through normal creation
            if (isset($new_user_data['proof_identity'])) {
                unset($new_user_data['proof_identity']);
            }
            
            // Create client user first without the proof_identity
            $new_user = User::create($new_user_data);
            
            // Then update it directly in the database to bypass model mutators
            if ($proof_path && isset($proof_path['_special_import_path'])) {
                $this->info('Identity proof downloaded: ' . $proof_path['_special_import_path']);
                $new_user->getConnection()
                    ->table($new_user->getTable())
                    ->where('id', $new_user->id)
                    ->update(['proof_identity' => $proof_path['_special_import_path']]);
            }
            
            foreach ($u->roles as $role) {
                $new_user->assignRole($role->name);
            }
            // Create default address
            $new_user->addresses()->create([
                'address'       => $u->delivery_address,
                'city'          => $u->delivery_city,
                'pc'            => $u->delivery_pc,
                'province'      => $u->delivery_province,
                'delivery'      => true,
                'billing'       => true,
                'last_use'      => true, // Set as default address
            ]);

            // ------------------ USER DETAILS ------------------

            // Import attributes with same v1 names
            $attrs = [
                'teedy_client_id', 'phone', 'birth_date', 'quota_user', 'language', 'veteran', 'quota_veteran_remaining',
                'quota_veteran_allowed', 'licence_acmpr', 'inscription_choice', 'symptoms', 'symptoms_other',
                'consumption', 'consumption_other', 'created_at', 'updated_at'
            ];
            $new_user_detail_data = array_intersect_key((array) $u, array_flip($attrs));

            // Import attributes with changed names
            $new_user_detail_data['gender'] = $u->sex;
            $new_user_detail_data['veteran_quota_date'] = $u->anniversary_date;
            $new_user_detail_data['symptoms'] = $u->symptome;
            $new_user_detail_data['symptoms_other'] = $u->symptome_other;

            $new_user->userdetail()->create($new_user_detail_data);

            // ------------------ PRESCRIPTION ------------------
            // NOTE: GONNA DUMB THIS SHIT CUZ PRESCRIPTIONS ARE NOT ACCURATE IN THE V1 DATABASE, IMPORTING FROM BLUELINK INSTEAD
            // If user has a prescription number or file, and has the correct status, create a new prescription
            // $user_should_have_prescription = in_array($u->status, ['pending_no_prescription', 'pending_for_prescription', 'disabled', 'active']);
            // if ($user_should_have_prescription and (!!$u->prescription_number or !!$u->prescription_photo)) {
            //     // Download file
            //     $file = $this->downloadFile($u->prescription_photo, 'prescriptions', uniqid());
            //     // Set prescription status
            //     $status = $u->status == 'pending_prescription_invalid' ? 'invalid' : 'valid';
            //     // Create prescription
            //     $new_user->prescriptions()->create([
            //         'status'    => $status,
            //         'prescription_number'    => $u->prescription_number,
            //         'end_date'  => $u->prescription_validity_date,
            //         'prescription_photo'      => $file,
            //         'clinic_name'    => $u->clinic_choice,
            //         'doc_name'    => $u->doc_name,

            //         // TODO: Check if we should use $u->quota_user instead
            //         // quota_user n'est pas utilisé dans la v1.. valider avec le client sa façon de gérer les quotas par rapport aux prescriptions
            //         'daily_dosage'    => null,
            //     ]);
            //     $this->info('Prescription created');
            // }




            // $this->info('User ['. $u->id .'] created: ' . $new_user->fullname);
        }

        $this->info('--- IMPORT COMPLETE ---');
        return Command::SUCCESS;
    }

    // Format names with all words capitalized
    private function capitalize ($word)
    {
        return ucwords(
            join('-',
                array_map(
                    fn ($p) => ucfirst($p),
                    explode('-', strtolower($word))
                )
            )
        );
    }

    // Download distant image and return local path
    private function downloadFile ($file_path, $type, $custom_file_name = null)
    {
        // Ignore if no image
        if (!$file_path) return null;

        // Extract file name
        $file_path_parts = explode('/', $file_path);
        $image_file = array_pop($file_path_parts);
        $file_parts = explode('.', $image_file);
        $file_ext = array_pop($file_parts);

        // Attempt to download image, return null if failed
        try {
            $file = file_get_contents($file_path);
        }
        catch (Exception $e) {
            // $this->warn('Image download failed: ' . $file_path);
            return null;
        }

        // Store image to disk - USING PRIVATE DISK
        try {
            $file_slug = Str::slug(join('', $file_parts));
            $folder_token = (string) Str::uuid();
            $destination_path = "users/{$folder_token}/ProofIdentity"; // Match model path structure
            $filename = ($custom_file_name ?? $file_slug) . '_' . uniqid() . '.' . $file_ext;
            $path = $destination_path . '/' . $filename;
            
            // Store the file
            $store = Storage::disk('private')->put($path, $file);
            
            // Return success and path, wrapped in a specially marked object
            // This will let us bypass the attribute mutator
            return $store ? ['_special_import_path' => $path] : null;
        }
        catch (Exception $e) {
            $this->warn('Failed to save image to disk: ' . $e->getMessage());
            return null;
        }
    }
}
