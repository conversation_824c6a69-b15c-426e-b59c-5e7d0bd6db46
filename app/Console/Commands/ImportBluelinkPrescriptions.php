<?php

namespace App\Console\Commands;

use DateTime;
use App\Models\User;
use App\Models\Prescription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportBluelinkPrescriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:bluelink-prescriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'import prescriptions from bluelink';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');
        // Read the JSON file
        // $jsonData = file_get_contents('storage\app\json\5_users_teedy.json');
        $jsonData = file_get_contents(storage_path('app/json/prescriptions.json'));
        $jsonDataUser = file_get_contents(storage_path('app/json/bluelink_users.json'));

        // Decode the JSON data
        $data = json_decode($jsonData, true);
        $userData= json_decode($jsonDataUser, true);

        try {
            // Start a new database transaction
            DB::beginTransaction();
    
            if (isset($data['value'])) {
                // Iterate through each item in the 'value' array
                foreach ($data['value'] as $item) {
                    //find the existing user by CustomerCode
                    $user = User::whereHas('userdetail', function ($query) use ($item) {
                        $query->where('bluelink_id', $item['CustomerCode']);
                    })->first();
                    if($user) {

                        // Find user status from userData
                        $userStatus = 'Invalid'; // default status
                        if (isset($userData['value'])) {
                            foreach ($userData['value'] as $userData2) {
                                if ($userData2['ID'] === $item['CustomerCode']) {
                                    $userStatus = !empty($userData2['PatientStatus']) ? $userData2['PatientStatus'] : 'Invalid';
                                    break;
                                }
                            }
                        }
                        //set quota for user based on status and veteran
                        if($user->userdetail->veteran == 0){
                            $user->userdetail->quota_user_remaining = $item['DailyDosage'] * 30;
                            $user->userdetail->save();
                        }

                        // Convert date strings to MySQL compatible format
                        $startDate = (new DateTime($item['StartDate']))->format('Y-m-d H:i:s');
                        $endDate = (new DateTime($item['ExpiryDate']))->format('Y-m-d H:i:s');


                        
                        //create the prescription
                        $user->prescriptions()->create([
                            'status'    => $userStatus,
                            'prescription_number'    => $item['PrescriptionName'],
                            'start_date' => $startDate,
                            'end_date' => $endDate,
                            // 'prescription_photo'      => $file, //note: disabled for now, will manage if needed but seems like theres pretty much no photos in v1
                            // 'clinic_name'    => $u->clinic_choice, //note: not available in bluelink, probably useless
                            'doc_name'    => $item['PrescribedBy'],
                        
                            // TODO: Check if we should use $u->quota_user instead
                            // check if we should use quota veteran and user, ask client later
                            'daily_dosage'    => $item['DailyDosage'] * 30,
                            'validity'  => $item['PrescriptionVerified'],
                        ]);
                    }
                }
            }
    
            // Commit the transaction
            DB::commit();
        } catch (\Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollback();
    
            // and rethrow the exception
            throw $e;
        }

        // Process the data
        // ...

        $this->info('--- ENDING IMPORT ---');
        
        return Command::SUCCESS;
    }
}
