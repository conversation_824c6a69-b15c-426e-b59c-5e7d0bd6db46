<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\ProductDetail;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailNotification;

class SendStockZeroReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:send-stock-zero-report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Envoie un rapport quotidien des produits avec stock à zéro';


    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = now()->toDateString();

        $products = ProductDetail::where('stock_zero_date', $today)->get();

        if ($products->isNotEmpty()) {
            
            $subject = "Rapport quotidien des produits à zéro stock - " . now()->toFormattedDateString();

            $data = ['products' => $products];

            // Send an email
            Mail::send(new EmailNotification("admin", $subject, 'admin.stock_zero_report', $data));


            // Reset stock_zero_date
            ProductDetail::where('stock_zero_date', $today)->update(['stock_zero_date' => null]);
        
            return 0;
        }

       
    }
}
