<?php

namespace App\Console\Commands;

use App\Models\Physician;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportBluelinkPhysicians extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:bluelink-physicians';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import physicians from Bluelink API to local database from a json file.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');
        // Read the JSON file
        $jsonData = file_get_contents(storage_path('app/json/physicians.json'));

        
        // Decode the JSON data
        $data = json_decode($jsonData, true);
        // dd($data);

        try {
            // Start a new database transaction
            DB::beginTransaction();
    
            if (isset($data['value'])) {
                // Iterate through each item in the 'value' array
                foreach ($data['value'] as $item) {
                    // Create entry in physicians table
                    $physician = Physician::create([
                        'name' => $item['CUSTNAME'],
                        'bluelink_id' => $item['proscode'],
                        'email' => $item['EMail'],
                        'phone' => $item['phone'],
                        'address' => $item['Address'],
                        'city' => $item['City, St Zip'],
                        'province' => $item['state'],
                        'zip' => $item['zip'],
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
    
            // Commit the transaction
            DB::commit();
        } catch (\Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollback();
    
            // and rethrow the exception
            throw $e;
        }

        // Process the data
        // ...

        $this->info('--- ENDING IMPORT ---');


        return Command::SUCCESS;
    }
}
