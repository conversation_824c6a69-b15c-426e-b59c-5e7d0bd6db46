<?php

namespace App\Console\Commands;

use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Console\Command;
use App\Exports\UsersExpandedExport;

class CustomExport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:custom-export';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {

                
                // Export user
                error_log('creating excel sheet');
        
                #create excel sheet of all users and store in public folder, add column names to first row
                // dd(new UsersExpandedExport);
                Excel::store(new UsersExpandedExport, 'users_expanded.xlsx', 'public');
                error_log('excel sheet created');
            
    }
}
