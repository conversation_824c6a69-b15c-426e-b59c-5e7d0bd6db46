<?php

namespace App\Console\Commands;

use App\Models\Evaluation;
use App\Models\Producer;
use App\Models\ProductAccessory;
use App\Models\ProductCategory;
use App\Models\ProductClone;
use App\Models\ProductDetail;
use App\Models\ProductRegular;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;
use Illuminate\Filesystem\Filesystem;

class ImportProducts extends Command
{
    // --reset: Run migration refresh and seed before import
    // --images: Download producers and products images
    // --api: Use production API endpoints instead of local json files
    protected $signature = 'import:products {--reset} {--images} {--api}';
    protected $description = 'Import all products and accessories from V1 admin';

    //
    private $producers_file = 'export/producers.json';
    private $producers_url;
    private $products_file = 'export/products.json';
    private $products_url;

    //
    private $categories = [];

    // New values for V2
    private $new_units = ['g', 'ml', 'oz', 'cm'];
    private $new_cannabinoids = ['thc', 'cbd', 'cbg', 'cbn', 'cbga'];
    private $new_formats = ['%' => 'pct', 'mg' => 'mg', 'mg/g' => 'mg_g', 'mg/ml' => 'mg_ml', 'mg/unité' => 'mg_un'];
    private $new_labels = [
        'cultive_qc'  => 'quebec_grown',
        'produit_qc'  => 'quebec_product',
        'bio'         => 'organic',
        'vegan'       => 'vegan',
        'new_offer'   => 'new',
        'lower_price' => 'new_price',
    ];

    // Assign producers v1 ids to v2
    private $producers_ids = [];

    // Related products slugs, to assign them once import is complete
    private $related_products = [];

    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');

        // Set files sources
        $api = $this->option('api');
        $url = $api ? 'https://api.teedy.com/fr/api/v1-export/' : __DIR__ . '/';
        $this->producers_url = $url . ($api ? 'producers' : $this->producers_file);
        $this->products_url = $url . ($api ? 'products' : $this->products_file);
        $this->info('Source set to: ' . ($api ? 'API' : 'LOCAL'));

        // Reset DB and seed producers if specified in command
        if ($this->option('reset')) {
            // Artisan::call('migrate:refresh --seed');
            // $this->info('DB reset complete');

            // ------------------ PRODUCERS ------------------

            // Add default empty producer
            $this->producers_ids[''] = Producer::where('slug_fr', '-')->first()->id;

            // Import Producers
            $jsonData = file_get_contents($this->producers_url);
            $json = json_decode($jsonData);

            // Remove all producers images
            (new Filesystem)->cleanDirectory('storage/app/public/producers');

            foreach ($json->results as $p) {
                if($p->title_fr){
                    // Save the original image URL before overwriting it
                    $original_image_url = $p->image;
                    
                    // Download logo if images download is enabled by command
                    $p->image = ($this->option('images') and $p->image) ? $this->downloadImage($p->image, 'producers-logo') : null;
                    if ($p->image) $this->info('Added image ' . $p->image);

                    $logo_path = $p->image;

            
                    // Create product model
                    $new_producer_data = $this->buildProducer($p);
                    $new_producer = Producer::create($new_producer_data);

                    // Update logo directly in database to bypass model mutator
                    if ($logo_path) {
                        DB::table('producers')->where('id', $new_producer->id)->update(['logo' => $logo_path]);
                    }
            
                    // Add reference to v1 id to map products
                    $this->producers_ids[$p->id] = $new_producer->id;
            
                    $this->info($p->title_fr . ' producer imported');
                }else{
                    $this->info('Producer without title_fr skipped');
                }
            }

            $this->info(count($json->results) . ' producers imported');
        }

        // Build categories
        $this->setupCategories();

        // ------------------ PRODUCTS ------------------

        // Import regular products
        $jsonData = file_get_contents($this->products_url);
        $json = json_decode($jsonData);

        // Remove all products images
        (new Filesystem)->cleanDirectory('storage/app/public/products');

        foreach ($json->results as $p) {
            $this->info('-----');

            // ------------------ ACCESSORY ------------------

            $type = $p->element_type;
            if ($type == 'accessory') {
                $new_product_data = $this->buildAccessory($p);
                $new_product = ProductAccessory::create($new_product_data);
            }

            // ------------------ REGULAR / CLONE ------------------

            else {
                $type = $p->category_slug == 'boutures' ? 'clone' : 'regular';

                $new_product_data = $this->buildProduct($p, $type);
                $new_product = $type == 'clone'
                    ? ProductClone::create($new_product_data)
                    : ProductRegular::create($new_product_data);
            }

            // ------------------ PRODUCT DETAIL ------------------

            // Create productdetail model
            $new_productdetail_data = $this->buildProductDetail($p, $new_product->id, $type);
            if (isset($p->labels)) $new_productdetail_data['labels'] = array_map(fn ($l) => $this->new_labels[$l->value], $p->labels);
            $new_productdetail = ProductDetail::create($new_productdetail_data);

            $this->info(Str::title($type) . ' [' . $new_product->id . '] created: ' . $new_productdetail->sku);

            // ------------------ EVALUATIONS ------------------

            // Create evaluations
            foreach ($p->evaluations as $e) {
                Evaluation::create([
                    'fk_user_id'            => $e->fk_user_id,
                    'fk_product_detail_id'  => $new_productdetail->id,
                    'value'                 => $e->value,
                    'created_at'            => (string) $e->created_at,
                    'updated_at'            => (string) $e->updated_at,
                ]);
            }
            $this->info('Evaluations created: ' . count($p->evaluations));

            // ------------------ RELATED PRODUCTS ------------------

            // Add related products and accessories slugs to add them once all products are imported
            $related = array_merge($p->rel_accessories ?? [], $p->rel_products ?? []);
            if (count($related)) {
                $this->related_products[$new_productdetail->id] = $related;
            }

            // ------------------ IMAGES ------------------

            if ($this->option('images')) {
                // Build array with $p->image and $p->images to simplify download and assignation
                $images = $p->images ?? [];
                $images = array_map(fn ($i) => ['path' => $i->path, 'alt_text' => $i->title], $images);
                if ($p->image) array_unshift($images, ['path' => $p->image, 'alt_text' => $new_productdetail->title_fr, 'primary' => true, 'og' => true]);

                // Download and assign all images
                foreach ($images as $k => $i) {
                    $i['path'] = $this->downloadImage($i['path'], 'products');
                    $new_productdetail->images()->create($i);
                    $this->info('Added image ' . $images[$k]['path']);
                }
            }
        }

        // Assign related products/accessories once everything is imported
        foreach ($this->related_products as $pd_id => $slugs) {
            foreach ($slugs as $slug) {
                $related_product = ProductDetail::where('slug_fr', $slug)->first();
                if ($related_product)
                    DB::table('related_products')->insert([
                        'product_detail_id' => $pd_id,
                        'related_product_detail_id' => $related_product->id
                    ]);
            }
        }

        $this->info('--- IMPORT COMPLETE ---');
        return Command::SUCCESS;
    }

    // Fix category names for constancy with v1
    private function setupCategories ()
    {
        $replace = [
            'huiles' => 'huile-de-cannabis',
            'oils' => 'cannabis-oils',
            'comestibles' => 'comestible',
            'comestibles' => 'comestible',
            'concentres' => 'concentre',
            'cartouches' => 'cartouche',
            'gelules' => 'gelule',
            'topiques' => 'topique',
        ];

        foreach(ProductCategory::all()->toArray() as $c) {
            if (array_key_exists($c['slug'], $replace)) $c['slug'] = $replace[$c['slug']];
            $this->categories[$c['slug']] =  $c['id'];
        }
    }

    // Download distant image and return local path
    private function downloadImage ($image_path, $type)
    {
        // Ignore if no image
        if (!$image_path) return null;

        $image_path = str_replace(' ', '%20', $image_path);
        // Extract file name
        $image_path_parts = explode('/', $image_path);
        $image_file = array_pop($image_path_parts);

        // Validate file extension
        $file_parts = explode('.', $image_file);
        $file_ext = array_pop($file_parts);
        if (!in_array($file_ext, ['png', 'jpg'])) {
            $this->warn('Invalid file format: ' . $image_path);
            return null;
        }

        // Attempt to download image, return null if failed
        try {
            $file = file_get_contents($image_path);
        }
        catch (Exception $e) {
            $this->warn('Image download failed: ' . $image_path);
            $this->warn($e->getMessage());
            return null;
        }

        // Store to local disk and return saved path
        $file_slug = Str::slug(join('', $file_parts));
        $path = $type . '/' . $file_slug . '_' . uniqid() . '.' . $file_ext;
        $store = Storage::disk('public')->put($path, $file);
        return $store ? $path : null;
    }

    // Format v1 producer object to fit v2
    private function buildProducer ($p)
    {
        return [
            'title_fr' => $p->title_fr,
            'title_en' => $p->title_en,
            'slug_fr' => $p->slug_fr,
            'slug_en' => $p->slug_en,
            'description_fr' => str_replace(['<p>&nbsp;</p>', '<p>&nbsp;&nbsp;</p>'], '', $p->description_fr),
            'description_en' => str_replace(['<p>&nbsp;</p>', '<p>&nbsp;&nbsp;</p>'], '', $p->description_en),
            'meta_title_fr' => $p->meta_title_fr,
            'meta_title_en' => $p->meta_title_en,
            'meta_description_fr' => $p->meta_description_fr,
            'meta_description_en' => $p->meta_description_en,

            'logo' => $p->image,

            'publish' => $p->publish,
            'publish_on_website' => $p->publish_on_website,
            'link_to_products' => $p->link_to_products,

            // Preserve old timestamps
            'created_at'        => $p->created_at,
            'updated_at'        => $p->updated_at,
        ];
    }

    private function buildProduct ($p, $type)
    {
        // Create cannabinoids object from root attributes
        $cannabinoids = array_filter(
            array_map(fn ($c) => ['type' => $c, 'amount' => $p->{$c}, 'format' => $this->new_formats[$p->{$c . '_format'}]], $this->new_cannabinoids),
            fn ($c) => !!$c['amount']
        );

        // Create aromas with new keys
        $aromas = ($p->aromas_all and count($p->aromas_all))
            ? array_map(fn ($i) => ['fr' => $i->name_fr ?? null, 'en' => $i->name_en ?? null], $p->aromas_all)
            : null;

        // Create terpenes with new keys
        $terpenes = ($p->terpenes_all and count($p->terpenes_all))
            ? array_filter(array_map(fn ($i) => ['type' => $i->terpene, 'percentage' => floatval($i->percentage)], $p->terpenes_all), fn ($i) => !!$i['type'])
            : null;

        // Attributes shared by regular products and clones
        $product = [
            'type'              => $p->type_slug,
            'dominant'          => $p->dominant == 'Équilibré' ? null : strtolower($p->dominant),
            'intensity'         => $p->intensity_slug == 'hard' ? 'high' : $p->intensity_slug,
            'terpene_total'     => $p->terpene_total,

            'format'                => $p->format,
            'unity'                 => $p->unity == 'gr' ? 'g' : (in_array($p->unity, $this->new_units) ? $p->unity : 'unit'),
            'quota_value'           => $p->quota_value,
            'price_per_unit'        => $p->price_per_unit,
            'sub_format_quantity'   => $p->sub_format_quantity,
            'sub_format'            => $p->sub_format,
            'sub_format_unity'      => $p->sub_format_unity,

            'cannabinoids'      => array_values($cannabinoids),
            'aromas'            => $aromas,
            'terpenes'          => $terpenes,

            // Preserve old timestamps
            'created_at'        => $p->created_at,
            'updated_at'        => $p->updated_at,
        ];

        // Additional attributes if regular product
        if ($type != 'clone')
            $product = array_merge($product, [
                'fk_category_id'    => $this->categories[$p->category_slug],
                'effect_relaxed'    => $p->effect_relaxed ?? 0,
                'effect_sleepy'     => $p->effect_sleepy ?? 0,
                'effect_euphoric'   => $p->effect_euphoric ?? 0,
                'effect_energic'    => $p->effect_energic ?? 0,
                'effect_happy'      => $p->effect_happy ?? 0,
                'benefits_stress'   => $p->benefits_stress ?? 0,
                'benefits_pain'     => $p->benefits_pain ?? 0,
                'benefits_depress'  => $p->benefits_depress ?? 0,
                'benefits_appetite' => $p->benefits_appetite ?? 0,
                'benefits_sleep'    => $p->benefits_sleep ?? 0,
            ]);

        return $product;
    }

    private function buildAccessory ($p)
    {
        return [
            'format'    => $p->size,
            'unity'     => $p->unity,

            // Preserve old timestamps
            'created_at'        => $p->created_at,
            'updated_at'        => $p->updated_at,
        ];
    }

    private function buildProductDetail ($p, $product_id, $type)
    {
        return [
            'productable_id'        => $product_id,
            'productable_type'      => 'App\Models\Product' . Str::title($type),
            'status'                => 'available', // TODO: Doublecheck

            'fk_producer_id'        => $this->producers_ids[$p->producer_id],
            'title_fr'              => $p->title_fr,
            'slug_fr'               => $p->slug_fr,
            'description_fr'        => str_replace('&nbsp;', ' ', strip_tags($p->description_fr)),
            'description_en'        => str_replace('&nbsp;', ' ', strip_tags($p->description_en)),
            'meta_title_fr'         => $p->meta_title_fr,
            'meta_title_en'         => $p->meta_title_en,
            'meta_description_fr'   => $p->meta_description_fr,
            'meta_description_en'   => $p->meta_description_en,
            'sku'                   => $p->sku,

            'price'                 => $p->price,
            'stock'                 => $p->stock,
            'is_applicable_veteran' => $p->is_applicable_to_veteran ?? false,
            'nb_max_veteran'        => $p->nb_max_veteran ?? null,
            'publish'               => !!$p->publish,
            'featured'              => $p->featured ?? null,
            'region'                => $p->region ?? 'qc',

            // Preserve old timestamps
            'created_at'        => $p->created_at,
            'updated_at'        => $p->updated_at,
        ];
    }
}
