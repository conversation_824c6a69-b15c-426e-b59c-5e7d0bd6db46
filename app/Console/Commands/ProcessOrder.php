<?php

namespace App\Console\Commands;

use App\Models\Order;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'order:process';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process the order to the BLWeb_Createorder API and save the transaction id to the order.';

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('--- STARTING PROCESSING ---');

        try {
            $orders = Order::where(function($query) {
                $query->where('bluelink_status', '!=', 'OK')
                      ->orWhereNull('bluelink_status');
            })->get();

            $this->info('Processing ' . count($orders) . ' orders.');
            Log::info('Processing ' . count($orders) . ' orders.');
            
            foreach ($orders as $order) {
                try {
                    if(env('SYNC_BLUELINK')){ 
                        if($order->bluelink_status == null){
                            $this->info('Order ' . $order->id . ' new, creating...');
                            Log::info('Order ' . $order->id . ' new, creating...');
                            $success = $order->BLWeb_Createorder();
                        }elseif($order->bluelink_status == "New"){
                            $this->info('Order ' . $order->id . ' existing, checked for status.');
                            Log::info('Order ' . $order->id . ' existing, checked for status.');
                            $success = $order->BLWeb_GetOrderStatus();
                        }elseif($order->bluelink_status == "Error"){
                            //skip for now
                        }
                    }
                    if($success){
                        $order->web_service_transaction_id = $success;
                        $order->save();
                        $this->info('Order ' . $order->id . ' processed successfully.');
                        Log::info('Order ' . $order->id . ' processed successfully.');
                    }else{
                        $this->error('Error processing order: ' . $order->additional_content);
                        Log::error('Error processing order: ' . $order->additional_content);
                    }
                } catch (Exception $e) {
                    $this->error('Error saving order: ' . $e);
                    Log::error('Error saving order: ' . $e->getMessage());
                    $order->additional_content = $e->getMessage();
                    $order->save();
                }
            }
        } catch (Exception $e) {
            $this->error('Error processing order: ' . $e->getMessage());
            Log::error('Error processing order: ' . $e->getMessage());
        }

        $this->info('--- ENDING PROCESSING ---');


        return Command::SUCCESS;
    }
}
