<?php

namespace App\Console\Commands;

use Exception;
use App\Models\ProductRegular;
use Illuminate\Console\Command;

class ConvertUOM extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-u-o-m';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Convert legacy UOM to new UOM';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- STARTING UOM CONVERSION ---');

        // Get all products
        $products = ProductRegular::all();
        $this->info('Converting ' . count($products) . ' products.');

        // Loop through each product
        foreach ($products as $product) {
            try {
                // Convert the UOM
                $product->convertUOM();
                $product->save();
                $this->info('Product ' . $product->id . ' converted successfully.');
            } catch (Exception $e) {
                $this->error('Error converting product: ' . $e->getMessage());
                $product->additional_content = $e->getMessage();
                $product->save();
            }
        }

        $this->info('--- UOM CONVERSION COMPLETE ---');
    }
}
