<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;

class ResetUserQuota extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:reset-user-quota {--dry-run : Preview changes without applying them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('Running in DRY RUN mode - no changes will be saved to database');
            $this->newLine();
        }
        
        $users = User::whereHas('userdetail', function($query) {
            $query->where('veteran', 0);
        })->get();
        
        $this->info("Found {$users->count()} regular users to process");
        $this->newLine();

        $updatedCount = 0;
        $skippedCount = 0;
        $noChangeCount = 0;
        
        foreach($users as $user) {
            $prescriptions = $user->prescriptions->where('status', 'Approved');
            
            if ($prescriptions->isEmpty()) {
                $this->warn("User #{$user->id} ({$user->fullname}) - No valid prescriptions found - SKIPPED");
                $skippedCount++;
                continue;
            }
            
            $highestQuotaPrescription = $prescriptions->sortByDesc('daily_dosage')->first();
            $newQuota = $highestQuotaPrescription->daily_dosage;
            $currentQuota = $user->userdetail->quota_user_remaining;
            
            if ($currentQuota == $newQuota) {
                $this->line("User #{$user->id} ({$user->fullname}) - Quota already correct ({$currentQuota}g) - NO CHANGE");
                $noChangeCount++;
                continue;
            }
            
            $this->info("User #{$user->id} ({$user->fullname}) - Quota will change: {$currentQuota}g → {$newQuota}g");
            
            if (!$isDryRun) {
                $user->userdetail->quota_user_remaining = $newQuota;
                $user->userdetail->save();
            }
            
            $updatedCount++;
        }
        
        $this->newLine();
        $this->info("=== Summary ===");
        $this->info("Total users processed: {$users->count()}");
        $this->info("Users to be updated: {$updatedCount}");
        $this->info("Users already at correct quota: {$noChangeCount}");
        $this->info("Users skipped (no valid prescriptions): {$skippedCount}");
        
        if ($isDryRun) {
            $this->newLine();
            $this->info("This was a dry run. Run command without --dry-run to apply changes.");
        }
        
        return Command::SUCCESS;
    }
}
