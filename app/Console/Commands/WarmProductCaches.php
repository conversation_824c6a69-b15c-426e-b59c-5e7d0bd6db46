<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ProductCacheService;

class WarmProductCaches extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'cache:warm-products {--force : Force warming regardless of TTL}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Warm product caches based on TTL check';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $force = $this->option('force');
        
        $this->info('Checking product caches for warming...');
        
        ProductCacheService::warmCacheIfNeeded(null, null, $force);
        
        $this->info('Cache warming check completed.');
        
        return 0;
    }
}
