<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Revolution\Google\Sheets\Facades\Sheets;

class SyncSheetInventory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-sheet-inventory {--dry-run : Preview changes without applying them}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Updates the google sheet data with database stock values';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            $spreadsheetId = env('GOOGLE_SHEET_ID');
            $sheetName = env('GOOGLE_SHEET_NAME');
            $dryRun = $this->option('dry-run');

            if ($dryRun) {
                $this->warn('🔍 DRY RUN MODE - No changes will be made');
            }

            $this->info("📊 Reading from Google Sheet: {$sheetName}");

            // Read existing data
            $existingData = Sheets::spreadsheet($spreadsheetId)
                ->sheet($sheetName)
                ->get();
            
            $this->info("📋 Found " . count($existingData) . " rows in the sheet");
            
            // Get database stock values
            $products = \App\Models\ProductDetail::whereNotNull('sku')
                ->where('sku', '!=', '')
                ->pluck('stock', 'sku')
                ->toArray();
            
            $this->info("🏪 Found " . count($products) . " products in database");
            
            $skuColumn = 0; // Column A
            $stockColumn = 2; // Column C
            $updatesCount = 0;
            $skippedCount = 0;
            $notFoundCount = 0;
            $changes = [];
            $batchUpdates = [];
            
            // Progress bar for better UX
            $progressBar = $this->output->createProgressBar(count($existingData));
            $progressBar->start();
            
            // Prepare batch updates
            foreach ($existingData as $rowIndex => $row) {
                $progressBar->advance();
                
                if (empty($row[$skuColumn])) {
                    $skippedCount++;
                    continue;
                }
                
                $sku = trim($row[$skuColumn]);
                
                if (!isset($products[$sku])) {
                    $notFoundCount++;
                    continue;
                }
                
                $currentStock = isset($row[$stockColumn]) ? $row[$stockColumn] : '';
                $currentStock = trim($currentStock) === '' ? '0' : $currentStock;
                $newStock = $products[$sku];
                
                if ($currentStock != $newStock) {
                    $sheetRow = $rowIndex + 1;
                    $columnLetter = chr(65 + $stockColumn); // Convert to letter (C)
                    $cellRange = $columnLetter . $sheetRow; // e.g., "C3"
                    
                    $changes[] = [
                        'sku' => $sku,
                        'cell' => $cellRange,
                        'old_stock' => $currentStock,
                        'new_stock' => $newStock
                    ];
                    
                    $batchUpdates[] = [
                        'range' => $cellRange,
                        'values' => [[$newStock]]
                    ];
                    
                    $updatesCount++;
                }
            }
            
            $progressBar->finish();
            $this->newLine(2);
            
            // Execute batch update if not dry run
            if (!$dryRun && !empty($batchUpdates)) {
                try {
                    $this->info("📤 Executing batch update for {$updatesCount} cells...");
                    
                    // Prepare batch data for the Google Sheets API
                    $batchData = [];
                    foreach ($batchUpdates as $update) {
                        $batchData[] = [
                            'range' => $sheetName . '!' . $update['range'],
                            'values' => $update['values']
                        ];
                    }
                    
                    // Use the Google Sheets service to perform batch update
                    $sheets = Sheets::spreadsheet($spreadsheetId);
                    $service = $sheets->getService();
                    
                    $body = new \Google\Service\Sheets\BatchUpdateValuesRequest();
                    $body->setValueInputOption('RAW');
                    $body->setData($batchData);
                    
                    $service->spreadsheets_values->batchUpdate($spreadsheetId, $body);
                    
                    $this->info("✅ Batch update completed successfully!");
                    
                } catch (\Exception $e) {
                    $this->error("❌ Failed to execute batch update: " . $e->getMessage());
                    Log::error("Failed to execute batch update: " . $e->getMessage());
                    return Command::FAILURE;
                }
            }
            
            // Display results
            if ($dryRun && !empty($changes)) {
                $this->info("📋 Changes that would be made:");
                $this->table(
                    ['SKU', 'Cell', 'Current Stock', 'New Stock'],
                    array_map(function($change) {
                        return [
                            $change['sku'],
                            $change['cell'],
                            $change['old_stock'],
                            $change['new_stock']
                        ];
                    }, array_slice($changes, 0, 10)) // Show first 10 changes
                );
                
                if (count($changes) > 10) {
                    $this->info("... and " . (count($changes) - 10) . " more changes");
                }
            }
            
            // Summary
            $this->info("✅ Sync completed successfully!");
            $this->info("📊 Summary:");
            $this->line("   • Total rows processed: " . count($existingData));
            $this->line("   • Products in database: " . count($products));
            $this->line("   • Updates " . ($dryRun ? "needed" : "completed") . ": {$updatesCount}");
            $this->line("   • SKUs not found in DB: {$notFoundCount}");
            $this->line("   • Rows skipped: {$skippedCount}");
            
            if (!$dryRun && $updatesCount > 0) {
                Log::info("Google Sheet inventory sync completed", [
                    'updates_count' => $updatesCount,
                    'changes' => $changes
                ]);
            }
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("❌ Sync failed: " . $e->getMessage());
            Log::error('Google Sheet sync failed: ' . $e->getMessage());
            
            if ($this->option('verbose')) {
                $this->error($e->getTraceAsString());
            }
            
            return Command::FAILURE;
        }
    }
}
