<?php

namespace App\Console\Commands;

use Exception;

use Illuminate\Console\Command;
use illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;
use App\Models\Cart;
use Carbon\Carbon;

class ClearCarts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'teedy:clear-carts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Clear carts of the previous day';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Scheduled task started.');
        try {
            Log::info('Clearing carts');

            $carts = Cart::where('created_at', '<', Carbon::now()->subHours(2))->get();
            foreach ($carts as $cart) {
                $cart->delete();
                Log::info('Cart cleared: ' . $cart->user->email);
            }
        } catch (Exception $e) {
            Log::info('Error clearing carts: ' . $e->getMessage());
        }
    }
}
