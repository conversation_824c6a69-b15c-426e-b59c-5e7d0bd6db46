<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportVeteranID extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-veteran-i-d';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');
        // Read the JSON file
        // $jsonData = file_get_contents('storage\app\json\bluelink_users.json');

        //linux test
        $jsonData = file_get_contents(storage_path('app/json/bluelink_users.json'));

        // Decode the JSON data
        $data = json_decode($jsonData, true);

        $updatedUsersCount = 0;


        try {
            // Start a new database transaction
            DB::beginTransaction();
    
            if (isset($data['value'])) {
                // Iterate through each item in the 'value' array
                foreach ($data['value'] as $item) {
                    //find the existing user by email
                    $user = User::where('email', $item['EMail'])->first();
    
                    //if user exist, update the bluelink_id from userdetail
                    if ($user) {
                        $this->info('user Bluelink ID found for: ' . $item['EMail']);
                        $user->userdetail->veteran_id = $item['PatientExternalID'];
                        $user->userdetail->save();
                        $this->info('user externalID changed with ' . $item['PatientExternalID']);

                        $updatedUsersCount++;
                    }else{
                        $this->info('No user found for: ' . $item['EMail']);
                    }
                    
                }
            }
    
            // Commit the transaction
            DB::commit();
        } catch (\Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollback();
    
            // and rethrow the exception
            throw $e;
            
        }
        $this->info('Total users updated: ' . $updatedUsersCount);

        // Process the data
        // ...

        $this->info('--- ENDING IMPORT ---');


        return Command::SUCCESS;
    }
}
