<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use App\Models\CartPrice;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class DeleteOldFrozenCarts extends Command
{
    protected $signature = 'carts:delete-old-frozen';
    protected $description = 'Delete frozen carts that have been inactive for more than 15 minutes (TTL of CartPrice model is set to 15 minutes)';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $threshold = Carbon::now();
        CartPrice::where('TTL', '<', $threshold)->delete();
        Log::info('Old frozen carts deleted successfully.');
    }
}
