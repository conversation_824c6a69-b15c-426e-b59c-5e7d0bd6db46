<?php

namespace App\Console\Commands;

use App\Models\Prescription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ValidateBluelinkPrescriptions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'validate:prescriptions';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Validate the prescriptions and warn if the prescription is not found in the bluelink system.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');
        // Read the JSON file
        // $jsonData = file_get_contents('storage\app\json\5_users_teedy.json');
        $jsonData = file_get_contents(storage_path('app/json/prescriptions.json'));

        // Decode the JSON data
        $data = json_decode($jsonData, true);

        try {
            // Start a new database transaction
            DB::beginTransaction();
    
            if (isset($data['value'])) {
                // Iterate through each item in the 'value' array
                foreach ($data['value'] as $item) {
                    //find the existing user by email
                    $prescription = Prescription::where('prescription_number', $item['PrescriptionName'])->first();
    
                    //if user exist, update the bluelink_id from userdetail
                    if ($prescription) {
                        $this->info('Prescription found for: ' . $item['PrescriptionName']);
                        $prescription->bluelink_synced = true;
                        $prescription->save();
                    }
                    
                }
            }
    
            // Commit the transaction
            DB::commit();
        } catch (\Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollback();
    
            // and rethrow the exception
            throw $e;
        }

        // Process the data
        // ...

        $this->info('--- ENDING IMPORT ---');
        
        return Command::SUCCESS;
    }
}
