<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;

class ImportAll extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-all';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run all import and validation commands in sequence';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting the complete import process...');
        
        $this->call('import:users', ['--reset' => true]);
        $this->call('import:bluelink-users');
        $this->call('import:products', ['--reset' => true, '--images' => true]);
        $this->call('import:bluelink-physicians');
        $this->call('import:bluelink-prescriptions');
        $this->call('validate:products');
        $this->call('validate:prescriptions');
        $this->call('app:convert-u-o-m');
        $this->call('app:import-display-uom');
        $this->call('app:clean-region');
        $this->call('app:reset-featured-product-list');
        $this->call('app:sync-inventory-daily');
        
        $this->info('All imports completed successfully!');
        
        return 0;
    }
}
