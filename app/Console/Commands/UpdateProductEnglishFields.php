<?php

namespace App\Console\Commands;

use App\Models\ProductDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;

class UpdateProductEnglishFields extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'products:update-english-fields {--api} {--dry-run} {--descriptions}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update missing English fields and/or restore HTML descriptions in ProductDetail models';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('Starting update of product fields...');
        
        // Source definition
        $api = $this->option('api');
        $url = $api ? 'https://api.teedy.com/fr/api/v1-export/' : __DIR__ . '/';
        $productsUrl = $url . ($api ? 'products' : 'export/products.json');
        
        $this->info('Source set to: ' . ($api ? 'API' : 'LOCAL'));
        $this->info('Update descriptions: ' . ($this->option('descriptions') ? 'Yes' : 'No'));
        
        try {
            // Read the JSON file
            $jsonData = file_get_contents($productsUrl);
            $json = json_decode($jsonData);
            
            if (!$json || !isset($json->results)) {
                $this->error('Invalid JSON data or no results found.');
                return Command::FAILURE;
            }
            
            $this->info(count($json->results) . ' products found in import file.');
            
            $updatedCount = 0;
            $skippedCount = 0;
            $notFoundCount = 0;
            $alreadyCompleteCount = 0;
            $descriptionsUpdated = 0;
            
            // Process each product
            foreach ($json->results as $p) {
                // Find the corresponding product by SKU
                $product = ProductDetail::where('sku', $p->sku)->first();
                
                if (!$product) {
                    $this->warn("Product with SKU {$p->sku} not found in database.");
                    $notFoundCount++;
                    continue;
                }
                
                // Check if both fields are already filled and no description update needed
                $needsUpdate = false;
                if (empty($product->title_en) || empty($product->slug_en)) {
                    $needsUpdate = true;
                } elseif ($this->option('descriptions')) {
                    $needsUpdate = true;
                } else {
                    $alreadyCompleteCount++;
                    continue;
                }
                
                // Collect the update data
                $updateData = [];
                
                // Check and update title_en if missing
                if (empty($product->title_en) && !empty($p->title_en)) {
                    $updateData['title_en'] = $p->title_en;
                }
                
                // Check and update slug_en if missing
                if (empty($product->slug_en)) {
                    // Use the one from import file if available
                    if (!empty($p->slug_en)) {
                        $updateData['slug_en'] = $p->slug_en;
                    } 
                    // Or generate from title if available
                    elseif (!empty($p->title_en)) {
                        $updateData['slug_en'] = Str::slug($p->title_en);
                    }
                }
                
                // Update descriptions with original HTML if requested
                if ($this->option('descriptions')) {
                    // Only update if there's something to update
                    if (!empty($p->description_fr)) {
                        // Remove only special HTML entities but keep actual HTML tags
                        $description_fr = str_replace('&nbsp;', ' ', $p->description_fr);
                        $updateData['description_fr'] = $description_fr;
                    }
                    
                    if (!empty($p->description_en)) {
                        // Remove only special HTML entities but keep actual HTML tags
                        $description_en = str_replace('&nbsp;', ' ', $p->description_en);
                        $updateData['description_en'] = $description_en;
                    }
                    
                    if (isset($updateData['description_fr']) || isset($updateData['description_en'])) {
                        $descriptionsUpdated++;
                    }
                }
                
                if (empty($updateData)) {
                    $skippedCount++;
                    continue;
                }
                
                // Output the changes
                $this->info("Updating product: {$product->sku}");
                foreach ($updateData as $key => $value) {
                    // Don't print full descriptions in console
                    if ($key == 'description_fr' || $key == 'description_en') {
                        $this->line("  - {$key}: [HTML content updated]");
                    } else {
                        $this->line("  - {$key}: " . $value);
                    }
                }
                
                // Update the product if not in dry-run mode
                if (!$this->option('dry-run')) {
                    $product->update($updateData);
                }
                
                $updatedCount++;
            }
            
            // Output summary
            $this->info('');
            $this->info('Update summary:');
            $this->info("- Products processed: " . count($json->results));
            $this->info("- Products already complete: {$alreadyCompleteCount}");
            $this->info("- Products updated: {$updatedCount}" . ($this->option('dry-run') ? ' (dry run - no changes made)' : ''));
            if ($this->option('descriptions')) {
                $this->info("- Descriptions restored: {$descriptionsUpdated}");
            }
            $this->info("- Products skipped: {$skippedCount}");
            $this->info("- Products not found: {$notFoundCount}");
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error('Error processing file: ' . $e->getMessage());
            Log::error('Error updating product fields: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString()
            ]);
            return Command::FAILURE;
        }
    }
}
