<?php

namespace App\Console\Commands;

use App\Models\ProductDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ValidateBluelinkProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'validate:products';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');
        // Read the JSON file
        // $jsonData = file_get_contents('storage\app\json\5_users_teedy.json');
        $jsonData = file_get_contents(storage_path('app/json/products_bluelink.json'));

        // Decode the JSON data
        $data = json_decode($jsonData, true);

        try {
            // Start a new database transaction
            DB::beginTransaction();
    
            if (isset($data['value'])) {
                // Iterate through each item in the 'value' array
                foreach ($data['value'] as $item) {
                    //find the existing user by email
                    $product = ProductDetail::where('sku', $item['ProdCode'])->first();
    
                    //if user exist, update the bluelink_id from userdetail
                    if ($product) {
                        $this->info('Product found for: ' . $item['ProdCode']);
                        $product->bluelink_synced = true;
                        $product->save();
                    }
                    
                }
            }
    
            // Commit the transaction
            DB::commit();
        } catch (\Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollback();
    
            // and rethrow the exception
            throw $e;
        }

        // Process the data
        // ...

        $this->info('--- ENDING IMPORT ---');
        
        return Command::SUCCESS;
    }
}
