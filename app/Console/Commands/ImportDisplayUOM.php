<?php

namespace App\Console\Commands;

use App\Models\ProductDetail;
use App\Models\ProductRegular;
use Illuminate\Console\Command;
use App\Services\BluelinkService;

class ImportDisplayUOM extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-display-uom';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import Display UOM from BL products';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $client = new BluelinkService();
        $inventory = $client->createUrl('GET', '/Inventory', [], []);

        // dd($inventory->value[0]->DisplayUOM);

        foreach ($inventory->value as $product) {
            // dd($product);
            $product_detail = ProductDetail::with('productable')
            ->where('sku', $product->ProdCode)
            ->first();
            
        if ($product_detail && ($product_detail->productable_type === 'App\Models\ProductRegular' || $product_detail->productable_type === 'App\Models\ProductAccessory')) {
            $product_regular = $product_detail->productable;
            if ($product_regular) {
                $product_regular->bl_displayuom = $product->DisplayUOM;
                $product_regular->save();
            }
        }
        }
    }
}
