<?php

namespace App\Console\Commands;

use App\Models\ProductDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class FixBalancedProducts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:fix-balanced-products
                            {--dry-run : Run without making any actual changes}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Update products with dominant type "Équilibré" based on JSON data';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        if ($isDryRun) {
            $this->info('Running in dry-run mode - no changes will be saved');
        }

        $jsonPath = __DIR__ . '/export/products.json';
        
        // Check if file exists
        if (!File::exists($jsonPath)) {
            $this->error("JSON file not found at: $jsonPath");
            return 1;
        }

        $this->info("Loading products from: $jsonPath");
        
        try {
            $json = file_get_contents($jsonPath);
            $data = json_decode($json, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                $this->error('Failed to parse JSON: ' . json_last_error_msg());
                return 1;
            }
            
            if (!isset($data['results']) || !is_array($data['results'])) {
                $this->error('Invalid JSON structure: "results" array not found');
                return 1;
            }
            
            $collection = collect($data['results']);
            $filtered = $collection->where('dominant', 'Équilibré')->all();
            $total = count($filtered);
            
            $this->info("Found $total products with dominant type 'Équilibré'");
            
            $updated = 0;
            $notFound = 0;
            $skipped = [];
            $notSupported = [];
            
            $this->output->progressStart($total);
            
            foreach ($filtered as $item) {
                $sku = $item['sku'] ?? 'Unknown';
                $product = ProductDetail::where('sku', $sku)->with('productable')->first();
                
                if ($product) {
                    $productable = $product->productable;
                    
                    if ($productable->dominant !== 'balanced') {
                        $oldValue = $productable->dominant;
                        if (!$isDryRun) {
                            $productable->dominant = 'balanced';
                            $productable->save();
                        }
                        $updated++;
                        $this->line("  <fg=green>✓</> Product '$sku': Updated dominant from '$oldValue' to 'balanced'");
                    } else {
                        $skipped[] = $sku;
                    }
                } else {
                    $notFound++;
                    $this->line("  <fg=yellow>!</> Product '$sku': Not found in database");
                }
                
                $this->output->progressAdvance();
            }
            
            $this->output->progressFinish();
            
            // Summary
            $this->newLine();
            $this->info('Summary:');
            $this->line("- Total products with 'Équilibré' in JSON: $total");
            $this->line("- Products updated: $updated");
            $this->line("- Products already set correctly: " . count($skipped));
            $this->line("- Products with unsupported type: " . count($notSupported));
            $this->line("- Products not found in database: $notFound");
            
            if ($isDryRun) {
                $this->warn('This was a dry run. No changes were saved.');
            } else {
                $this->info('All changes have been saved to the database.');
            }
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error('Error: ' . $e->getMessage());
            return 1;
        }
    }
}
