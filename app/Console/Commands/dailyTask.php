<?php

namespace App\Console\Commands;

use test;
use Carbon\Carbon;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\Prescription;
use App\Mail\EmailNotification;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class DailyTask extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'teedy:daily-task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Run daily check about prescription and quota';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */

    public function handle()
    {

        $this->info('Scheduled task started.');




        Log::channel('daily')->info('*******************');
        Log::channel('daily')->info('Scheduled task started.');

        $users = User::get();
        foreach ($users as $user) {
            Log::channel('daily')->info('Checking user: ' . $user->firstname . ' - ' . $user->lastname . ' - ' . $user->id);

            // Skip admin
            if ($user->hasRole(['superadmin', 'admin'])) {
                Log::channel('daily')->info('Skipped admin.');
                continue;
            } 

            $allPrescriptionsExpired = true; // Start with true and set to false if any valid prescription is found

            // Check if user has prescription
            if (count($user->prescriptions) > 0) {
                foreach ($user->prescriptions->where('status', 'Approved') as $prescription) {
                    // Check if prescription is expired
                    $nb_jour = Carbon::now()->diffInDays(Carbon::create($prescription->end_date), false);

                    $this->info('Checking prescription ' . $prescription->id . ' for user ' . $user->firstname . ' ' . $user->lastname . ' - ' . $nb_jour . ' days left');
                
                    // Update prescription status to expired if expired
                    if ($nb_jour <= 0 && $nb_jour > -1) {
                        // Update prescription status to expired
                        $prescription->status = 'Expired';
                        $prescription->save();
                        Log::channel('daily')->info('Updated prescription ' . $prescription->id . ' status to expired.');
                
                        // Send mail to user and admin notifying them that prescription has expired (DAY 0)
                        $this->info('Prescription for user ' . $user->firstname . ' ' . $user->lastname . ' has expired.');
                        
                        App::setLocale($user->userdetail->language);
                        $subject = __('mail.prescription_expired.subject');
                        if($this->shouldSendMail()){
                            Mail::send(new EmailNotification($user->email, $subject, 'customer.medical.prescription_expired', ['firstname' => $user->firstname, 'lastname' => $user->lastname, 'user' => $user]));
                            Mail::send(new EmailNotification(env('MAIL_FROM_ADDRESS_SUPPORT'), $subject, 'admin.medical.prescription_expired', ['firstname' => $user->firstname, 'lastname' => $user->lastname, 'id' => $user->id, 'veteran' => $user->userdetail->veteran]));
                            Log::channel('daily')->info('Mail sent to user and admin about expired prescription.');
                        }else{
                            Log::channel('daily')->info('Mail sending is disabled, skipping mail for expired prescription.');
                        }

                    } elseif ($nb_jour < 0) {
                        $prescription->status = 'Expired';
                        $prescription->save();
                    } else {
                        $allPrescriptionsExpired = false; // Set to false if any valid prescription is found
                        Log::channel('daily')->info('Prescription for user is still valid.');
                    }
                    
                    // Send mail to user notifying them that their prescription will expire in 15/30 days
                    if (($nb_jour == 30 || $nb_jour == 15) && $user->status == 'active') {
                        App::setLocale($user->userdetail->language);
                        $subject = ($user->userdetail->veteran) ? __('mail.prescription_will_expired.subject_veteran') : __('mail.prescription_will_expired.subject');
                        if($this->shouldSendMail()){
                            Mail::send(new EmailNotification($user->email, $subject, 'customer.medical.prescription_will_expired', ['firstname' => $user->firstname, 'lastname' => $user->lastname ,'nb_jour' => $nb_jour]));
                            App::setLocale('fr');
                            Mail::send(new EmailNotification(env('MAIL_FROM_ADDRESS_SUPPORT'), $subject, 'customer.medical.prescription_will_expired', ['firstname' => $user->firstname, 'lastname' => $user->lastname ,'nb_jour' => $nb_jour]));
                            Log::channel('daily')->info('Prescription will expire mail sent for user, ' . $nb_jour . ' days left');
                        }else{
                            Log::channel('daily')->info('Mail sending is disabled, skipping mail for prescription expiration.');
                        }

                    }
                }

                // Handle expired prescriptions follow-up emails
                $this->handleExpiredPrescriptionFollowUp($user);

            // Check if user has never submitted prescription
            } elseif ($user->status == 'pending_no_prescription') {
                    Log::channel('daily')->info('User has never submitted prescription');
                    $nb_jour_from = Carbon::now()->diffInDays(Carbon::create($user->created_at->format('Y-m-d')), true);

                    // Send mail to user notifying them that they have no prescriptions (1 day and 7 days after registration)
                    if ($nb_jour_from == 1 || $nb_jour_from == 7) {
                        App::setLocale($user->userdetail->language);
                        $subject = __('mail.no_prescriptions.subject');

                        if($this->shouldSendMail()){
                            Mail::send(new EmailNotification($user->email, $subject, 'customer.medical.no_prescription', ['firstname' => $user->firstname, 'lastname' => $user->lastname]));
                            Log::channel('daily')->info('Mail sent to user about having no prescriptions.');
                        }else{
                            Log::channel('daily')->info('Mail sending is disabled, skipping mail for no prescriptions.');
                        }
                    }
            }
            
            // Update user status to expired if all prescriptions are expired
            if ($allPrescriptionsExpired && $user->status == 'active') {
                $user->status = 'pending_no_prescription';
                $user->save();
                Log::channel('daily')->info('Updated user status to expired.');
            }

            // // // // /// // 
            // Quota reset
            // // // // /// // 

            // User quota reset
            if (!$user->userdetail->veteran && Carbon::now()->day == 1) {

                // get all valid prescriptions for user 
                $prescriptions = $user->prescriptions->where('status', 'Approved');
                // get the prescription with the highest quota
                $highestQuotaPrescription = $prescriptions->sortByDesc('daily_dosage')->first();

                if ($highestQuotaPrescription) {
                    $user->userdetail->quota_user_remaining = $highestQuotaPrescription->daily_dosage;
                    $user->userdetail->save();
                }
            }

            // Veteran quota reset
            if ($user->userdetail->veteran) {
                $now = Carbon::now();
                $resetDate = Carbon::create($user->userdetail->veteran_quota_date);
                $lastDayOfMonth = $now->copy()->endOfMonth()->day;

                $shouldResetToday = false;

                // Check if today is the reset date or the last day of the month (prevent issue with february and 30 days months)
                if ($now->day == $resetDate->day) {
                    // Reset on the specified day if the month has enough days
                    $shouldResetToday = true;
                } elseif ($now->day == $lastDayOfMonth && $now->day < $resetDate->day) {
                    // Reset on the last day of the month, but only if we haven't already reset this month
                    $shouldResetToday = true;
                }

                if ($shouldResetToday) {
                    Log::channel('daily')->info('Veteran quota reset for user ' . $user->email);
                    // Reset quota
                    $user = User::withoutEvents(function () use ($user) {
                        $user->userdetail->quota_veteran_remaining = $user->userdetail->quota_veteran_allowed;
                        $user->userdetail->save();
                    });
                }
            }
        }

        $this->info('Scheduled task finished.');
        Log::channel('daily')->info('*******************');
    }

    /**
     * Handle follow-up emails for expired prescriptions
     */
    private function handleExpiredPrescriptionFollowUp(User $user)
    {
        // Get the most recently expired prescription
        $expiredPrescriptions = $user->prescriptions->where('status', 'Expired');
        
        if ($expiredPrescriptions->isEmpty()) {
            return;
        }

        // Get the most recently expired prescription and its end date
        $mostRecentExpired = $expiredPrescriptions->sortByDesc('end_date')->first();
        $lastExpiredDate = $mostRecentExpired->end_date;

        // Check if user has renewed their prescription since the last expiry
        $hasRenewed = $user->prescriptions()
            ->where('status', 'Approved')
            ->where('created_at', '>', Carbon::parse($lastExpiredDate))
            ->exists();

        // If user has renewed, don't send follow-up emails
        if ($hasRenewed) {
            Log::channel('daily')->info('User has renewed prescription since last expiry, skipping follow-up emails.');
            return;
        }

        $daysSinceExpiry = Carbon::now()->diffInDays(Carbon::parse($mostRecentExpired->end_date), false);

        Log::channel('daily')->info("Days since prescription expiry: {$daysSinceExpiry}");

        // 15 days after expiration: email to admin only
        if ($daysSinceExpiry <= -15 && $daysSinceExpiry > -16) {
            $this->sendAdminFollowUpEmail($user, $daysSinceExpiry);
        }

        // 30 days after expiration, then every 30 days for 6 months: email to user
        if ($daysSinceExpiry <= -30 && $daysSinceExpiry > -31) {
            $this->sendUserRenewalReminder($user, $daysSinceExpiry);
        }

        // Monthly reminders for 30, 60, 90, 120, 150 days after expiration
        if (($daysSinceExpiry <= -60 && $daysSinceExpiry > -61) ||
            ($daysSinceExpiry <= -90 && $daysSinceExpiry > -91) ||
            ($daysSinceExpiry <= -120 && $daysSinceExpiry > -121) ||
            ($daysSinceExpiry <= -150 && $daysSinceExpiry > -151)) {
            $this->sendUserRenewalReminder($user, $daysSinceExpiry);
        }

        // 6 months after expiration (180 days): reminder email
        if ($daysSinceExpiry <= -180 && $daysSinceExpiry > -181) {
            $this->sendUser6MonthReminder($user, $daysSinceExpiry);
        }

        // 1 year after expiration (365 days): final reminder email
        if ($daysSinceExpiry <= -365 && $daysSinceExpiry > -366) {
            $this->sendUserFinalReminder($user, $daysSinceExpiry);
        }
    }

    /**
     * Send admin follow-up email
     */
    private function sendAdminFollowUpEmail(User $user, int $daysSinceExpiry)
    {
        if ($this->shouldSendMail() == false) {
            Log::channel('daily')->info('Mail sending disabled - would have sent admin follow-up email.');
            return;
        }

        App::setLocale('fr');
        $subject = __('mail.prescription_expired.subject_admin_followup_15', [
            'firstname' => $user->firstname,
            'lastname' => $user->lastname
        ]);
        Mail::send(new EmailNotification(
            env('MAIL_FROM_ADDRESS_SUPPORT'), 
            $subject, 
            'admin.medical.prescription_expired', 
            ['firstname' => $user->firstname, 'lastname' => $user->lastname, 'user' => $user, 'id' => $user->id,'veteran' => $user->userdetail->veteran ?? false]
        ));
        Log::channel('daily')->info('15-day follow-up email sent to admin for expired prescription.');
    }

    /**
     * Send user renewal reminder
     */
    private function sendUserRenewalReminder(User $user, int $daysSinceExpiry)
    {
        if ($this->shouldSendMail() == false) {
            Log::channel('daily')->info('Mail sending disabled - would have sent user renewal reminder.');
            return;
        }

        // Check if user wants to receive expired prescription emails
        if (!($user->userdetail->mail_notifications ?? true)) {
            Log::channel('daily')->info('User has opted out of expiry emails, skipping renewal reminder.');
            return;
        }

        App::setLocale($user->userdetail->language);
        $subject = __('mail.prescription_expired.subject_renewal_reminder');
        Mail::send(new EmailNotification(
            $user->email, 
            $subject, 
            'customer.medical.prescription_expired', 
            ['firstname' => $user->firstname, 'lastname' => $user->lastname, 'user' => $user, 'id' => $user->id,'veteran' => $user->userdetail->veteran ?? false]
        ));
        Log::channel('daily')->info("Monthly renewal reminder sent to user ({$daysSinceExpiry} days expired).");
    }

    /**
     * Send user 6-month reminder
     */
    private function sendUser6MonthReminder(User $user, int $daysSinceExpiry)
    {
        if ($this->shouldSendMail() == false) {
            Log::channel('daily')->info('Mail sending disabled - would have sent 6-month reminder.');
            return;
        }

        // Check if user wants to receive expired prescription emails
        if (!($user->userdetail->mail_notifications ?? true)) {
            Log::channel('daily')->info('User has opted out of expiry emails, skipping 6-month reminder.');
            return;
        }

        App::setLocale($user->userdetail->language);
        $subject = __('mail.prescription_expired.subject_6month_reminder');
        Mail::send(new EmailNotification(
            $user->email, 
            $subject, 
            'customer.medical.prescription_expired', 
            ['firstname' => $user->firstname, 'lastname' => $user->lastname, 'user' => $user, 'id' => $user->id,'veteran' => $user->userdetail->veteran ?? false]
        ));
        Log::channel('daily')->info('6-month reminder email sent to user for expired prescription.');
    }

    /**
     * Send user final reminder
     */
    private function sendUserFinalReminder(User $user, int $daysSinceExpiry)
    {
        if ($this->shouldSendMail() == false) {
            Log::channel('daily')->info('Mail sending disabled - would have sent final reminder.');
            return;
        }

        // Check if user wants to receive expired prescription emails
        if (!($user->userdetail->mail_notifications ?? true)) {
            Log::channel('daily')->info('User has opted out of expiry emails, skipping final reminder.');
            return;
        }

        App::setLocale($user->userdetail->language);
        $subject = __('mail.prescription_expired.subject_1year_reminder');
        Mail::send(new EmailNotification(
            $user->email, 
            $subject, 
            'customer.medical.prescription_expired', 
            ['firstname' => $user->firstname, 'lastname' => $user->lastname, 'user' => $user, 'id' => $user->id,'veteran' => $user->userdetail->veteran ?? false]
        ));
        Log::channel('daily')->info('1-year final reminder email sent to user for expired prescription.');
    }

    /**
     * Check if mail should be sent (for testing purposes)
     */
    private function shouldSendMail(): bool
    {
        return env('SEND_MAIL', false);
    }
}
