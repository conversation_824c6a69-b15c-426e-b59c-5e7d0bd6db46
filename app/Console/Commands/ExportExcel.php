<?php

namespace App\Console\Commands;

use Exception;

use Illuminate\Console\Command;
use App\Mail\ReportMail;
use App\Exports\UsersExport;
use App\Exports\ProductsExport;
use App\Mail\ProductsReportMail;
use Illuminate\Support\Facades\Mail;
use Maatwebsite\Excel\Facades\Excel;

class ExportExcel extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'teedy:export-excel';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Export excel sheet of all users and products';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Set Emails
        if (env('APP_ENV') === 'local') {
            $emails = ['<EMAIL>'];
        } else {
            $emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
        }
        
        // Export user
        error_log('creating excel sheet');

        #create excel sheet of all users and store in public folder, add column names to first row
        Excel::store(new UsersExport, 'users.xlsx', 'public');
        error_log('excel sheet created');

        #send email with excel sheet attached
        error_log('sending email');
        $subject = 'Teedy - Users Export';

        Mail::to($emails)
            ->send(new ReportMail('mails.users_report', $subject, ['firstname' => 'Dev', 'lastname' => 'Lopper' ] ));
        
        error_log('email sent successfully');



        // Export product
        error_log('creating excel sheet');
        
        // return Excel::store(new UsersExport, 'users.xlsx', 'public');

        #create excel sheet of all users and store in public folder, add column names to first row
        Excel::store(new ProductsExport, 'Products.xlsx', 'public');
        error_log('Product excel sheet created');

        #send email with excel sheet attached
        error_log('sending email');
        $subject = 'Teedy - Products Export';
        
        Mail::to($emails)
            ->send(new ProductsReportMail('mails.products_report', $subject, ['firstname' => 'Dev', 'lastname' => 'Lopper' ] ));

        error_log('email sent successfully');
    }
}