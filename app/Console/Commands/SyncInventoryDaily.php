<?php

namespace App\Console\Commands;

use Exception;
use App\Models\ProductDetail;
use Google\Service\AdExchangeBuyerII\Product;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncInventoryDaily extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-inventory-daily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronise bluelink linked products stock levels daily';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- STARTING PROCESSING ---');

        try {
            $products = ProductDetail::where('bluelink_synced', 1)->get();
            $this->info('Processing ' . count($products) . ' products.');

            $count = 0;
            foreach ($products as $product) {
                try {
                    if(env('SYNC_BLUELINK')){ 

                        $count++;
                        $data = $product->BLWeb_SyncStock();
                        $this->info('updating product ' . $product->id . ' - ' . $product->sku . ' - product updated count: ' . $count);
                        if(empty($data->value)) return false;
                        $uoh = $data->value[0]->{'UOH in DisplayUOM'};
                        $description = $data->value[0]->Description;
                        $product->update(['stock' => $uoh, 'description_bl' => $description]);

                        
                    }
                } catch (Exception $e) {
                    $this->error('Error updating product: ' . $e->getMessage());
                }
            }

            //check every product after stock ajustment and delist if necessary

            $products = ProductDetail::where('bluelink_synced', 1)->where('stock', '<=', 0)->where('publish', 1)->get();
            foreach ($products as $product) {
                $this->delistProductIfOutOfStock($product);
            }
        } catch (Exception $e) {
            $this->error('Error updating product: ' . $e->getMessage());
        }

        $this->info('--- ENDING PROCESSING ---');


        return Command::SUCCESS;
    }

    private function delistProductIfOutOfStock(ProductDetail $product)
    {
        // Check if stock is zero or negative
        if ($product->stock <= 0) {
            // Only delist if the product is currently published and check if there are related products. Only delist if all related products are at 0 as well
            $relatedProducts = $product->related_products()->get();

            $allRelatedOutOfStock = $relatedProducts->isEmpty() || $relatedProducts->every(function ($relatedProduct) {
                return $relatedProduct->stock <= 0;
            });
            if ($allRelatedOutOfStock) {
                //delist all products

                if ($product->publish) {
                    $product->update(['publish' => 0]);
                    
                    Log::info('Product delisted due to zero stock', [
                        'product_id' => $product->id,
                        'sku' => $product->sku,
                        'title' => $product->title_fr,
                        'stock' => $product->stock,
                    ]);

                    $this->info('delisting product ' . $product->id . ' - ' . $product->sku);

                    foreach ($relatedProducts as $relatedProduct) {
                        $relatedProduct->update(['publish' => 0]);
                        $this->info('delisting product ' . $relatedProduct->id . ' - ' . $relatedProduct->sku);
                    }

                    return true;
                }
            }
                
        }

        return false;
    }
}
