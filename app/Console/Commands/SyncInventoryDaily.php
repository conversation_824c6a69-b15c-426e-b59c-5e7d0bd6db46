<?php

namespace App\Console\Commands;

use Exception;
use App\Models\ProductDetail;
use Illuminate\Console\Command;

class SyncInventoryDaily extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-inventory-daily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Synchronise bluelink linked products stock levels daily';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- STARTING PROCESSING ---');

        try {
            $products = ProductDetail::where('bluelink_synced', 1)->get();
            $this->info('Processing ' . count($products) . ' products.');

            foreach ($products as $product) {
                try {
                    if(env('SYNC_BLUELINK')){ 

                        $data = $product->BLWeb_SyncStock();
                        if(empty($data->value)) return false;
                        $uoh = $data->value[0]->{'UOH in DisplayUOM'};
                        $description = $data->value[0]->Description;
                        $product->update(['stock' => $uoh, 'description_bl' => $description]);
                    }
                } catch (Exception $e) {
                    $this->error('Error updating product: ' . $e->getMessage());
                }
            }
        } catch (Exception $e) {
            $this->error('Error updating product: ' . $e->getMessage());
        }

        $this->info('--- ENDING PROCESSING ---');


        return Command::SUCCESS;
    }
}
