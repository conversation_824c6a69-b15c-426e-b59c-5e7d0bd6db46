<?php

namespace App\Console\Commands;

use App\Models\Address;
use Illuminate\Console\Command;

class CleanRegion extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:clean-region';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'batch change addresses to code instead of full name';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        //change all provinces to abbreviation, example: Québec to qc, Ontario to on
        $this->info('--- STARTING REGION CONVERSION ---');

        $provinceMap = [
            'Québec' => 'QC',
            'Quebec' => 'QC',
            'Ontario' => 'ON',
            'British Columbia' => 'BC',
            'Alberta' => 'AB',
            'Manitoba' => 'MB',
            'Saskatchewan' => 'SK',
            'Nova Scotia' => 'NS',
            'New Brunswick' => 'NB',
            'Newfoundland and Labrador' => 'NL',
            'Prince Edward Island' => 'PE',
            'Northwest Territories' => 'NT',
            'Nunavut' => 'NU',
            'Yukon' => 'YT'
        ];
        
        // Use a DB query to get and update directly
        $addresses = Address::all();
        $this->info("Found " . $addresses->count() . " addresses");
        
        foreach ($addresses as $address) {
            // Get the raw database value using getRawOriginal
            $originalProvince = $address->getRawOriginal('province');
            $this->info("Checking province: {$originalProvince}");
            
            if (isset($provinceMap[$originalProvince])) {
                $this->info("Converting '{$originalProvince}' to '{$provinceMap[$originalProvince]}'");
                
                // Update directly in database to bypass accessor
                Address::where('id', $address->id)
                       ->update(['province' => $provinceMap[$originalProvince]]);
            }
        }

        $this->info('--- REGION CONVERSION COMPLETE ---');
    }
}
