<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class ImportBluelinkUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'import:bluelink-users';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Import users customerID from Bluelink API to local database using email as the unique identifier from a json file.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('--- STARTING IMPORT ---');
        // Read the JSON file
        // $jsonData = file_get_contents('storage\app\json\bluelink_users.json');

        //linux test
        $jsonData = file_get_contents(storage_path('app/json/bluelink_users.json'));

        // Decode the JSON data
        $data = json_decode($jsonData, true);

        $updatedUsersCount = 0;


        try {
            // Start a new database transaction
            DB::beginTransaction();
    
            if (isset($data['value'])) {
                // Iterate through each item in the 'value' array
                foreach ($data['value'] as $item) {
                    //find the existing user by email
                    $user = User::where('email', $item['EMail'])->first();
    
                    //if user exist, update the bluelink_id from userdetail
                    if ($user) {
                        $this->info('user Bluelink ID found for: ' . $item['EMail']);
                        $user->userdetail->bluelink_id = $item['ID'];
                        $user->userdetail->teedy_client_id = $item['ID'];
                        $user->userdetail->bluelink_synced = true;
                        $user->userdetail->save();
                        $this->info('user Bluelink ID changed with ' . $item['ID']);

                        $updatedUsersCount++;

                        //update the user's address's shipcode and shipname with the new data
                        $address = $user->addresses()->first();
                        if ($address) {
                            $address->shipcode = $item['ID'];
                            $address->shipname = $item['Company'];
                            $address->save();
                        }else{
                            $this->info('No address found for user: ' . $item['EMail']);
                        }
                    }else{
                        $this->info('No user found for: ' . $item['EMail']);
                    }
                    
                }
            }
    
            // Commit the transaction
            DB::commit();
        } catch (\Exception $e) {
            // An error occurred; cancel the transaction...
            DB::rollback();
    
            // and rethrow the exception
            throw $e;
            
        }
        $this->info('Total users updated: ' . $updatedUsersCount);

        // Process the data
        // ...

        $this->info('--- ENDING IMPORT ---');


        return Command::SUCCESS;
    }
}
