<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\PackagingDateImport;
use Illuminate\Support\Facades\Log;

class SyncPackagingDate extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:sync-packaging-date {--url= : Direct download URL for the Excel file}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync packaging dates from Excel file to product details';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Syncing packaging date...');

        try {
            // Get URL from options or use default
            // This should be a direct download URL from Google Drive
            $url = $this->option('url') ?? $this->getGoogleDriveDownloadUrl('16QTpN2oP7wcB7wqI-gI_sOFYYuVwtunjvsk3UVQqT8U');
            
            if (!$url) {
                $this->error('Invalid or missing download URL');
                return;
            }
            else {
                $this->info('Using URL: ' . $url);
            }
            
            // Download the file
            $response = Http::timeout(30)->get($url);
            
            if (!$response->successful()) {
                $this->error('Failed to download file: ' . $response->status());

                Log::error('Échec du téléchargement du fichier.', [
                    'url' => $url,
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                return;
            }
            
            // Save to temporary file
            $tempPath = storage_path('app/temp_import.xlsx');
            Storage::disk('local')->put('temp_import.xlsx', $response->body());
            
            // Import using Laravel Excel
            Excel::import(new PackagingDateImport($this), $tempPath);
            
            // Remove temporary file
            // Storage::disk('local')->delete('temp_import.xlsx');
            
            $this->info('Packaging date synced successfully.');
        } catch (\Exception $e) {
            $this->error('Error syncing packaging date: ' . $e->getMessage());
            Log::error('Error syncing packaging date: ' . $e->getMessage());
        }
    }
    
    /**
     * Convert Google Drive file ID to direct download URL
     */
    private function getGoogleDriveDownloadUrl($fileId)
    {
        // Format for direct download from Google Drive
        return "https://docs.google.com/spreadsheets/d/{$fileId}/export?format=xlsx&id={$fileId}&gid=548364225";
    }
}
