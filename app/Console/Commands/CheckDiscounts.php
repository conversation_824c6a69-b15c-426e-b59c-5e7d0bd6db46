<?php

namespace App\Console\Commands;

use App\Models\Discount;
use Illuminate\Console\Command;

class CheckDiscounts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'teedy:check-discounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Checks discounts for recurring discounts starts and stops';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = now()->format('l'); // Gets current day name
        
        // Activate recurring discounts
        Discount::where('is_recurring', true)
        ->where('recurring_days', 'LIKE', "%$today%")
            ->where('recurring_start_time', '<=', now()->format('H:i:s'))
            ->where('recurring_end_time', '>', now()->format('H:i:s'))
            ->update(['is_active' => true]);
            
        // Deactivate expired recurring discounts
        Discount::where('is_recurring', true)
            ->where(function ($query) use ($today) {
                $query->where('recurring_days', 'NOT LIKE', "%$today%")
                    ->orWhere('recurring_end_time', '<=', now()->format('H:i:s'));
            })
            ->update(['is_active' => false]);
    }
}
