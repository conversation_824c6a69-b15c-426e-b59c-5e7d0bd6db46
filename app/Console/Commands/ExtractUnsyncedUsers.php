<?php

namespace App\Console\Commands;

use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ExtractUnsyncedUsers extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:extract-unsynced-users 
                            {--new-users=new_users.txt : Path to output file for completely new users}
                            {--email-mismatch=email_mismatch.txt : Path to output file for users found by name with different email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Extract users from BlueLink that do not exist in our database';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- STARTING SCRIPT ---');

        // Setup output files
        $newUsersFile = $this->option('new-users');
        $emailMismatchFile = $this->option('email-mismatch');
        
        if (!str_contains($newUsersFile, '/')) {
            $newUsersFile = storage_path('app/' . $newUsersFile);
        }
        
        if (!str_contains($emailMismatchFile, '/')) {
            $emailMismatchFile = storage_path('app/' . $emailMismatchFile);
        }

        $jsonData = file_get_contents(storage_path('app/json/bluelink_users2.json'));
        $data = json_decode($jsonData, true);

        $newUsersCount = 0;
        $emailMismatchCount = 0;
        
        // Prepare content for "new users" file
        $newUsersContent = "COMPLETELY NEW USERS (NOT FOUND IN DATABASE)\n";
        $newUsersContent .= "==========================================\n\n";
        $newUsersContent .= "Generated on: " . now()->format('Y-m-d H:i:s') . "\n\n";
        $newUsersContent .= "Email, First Name, Last Name, BlueLink Customer Number, Phone\n";
        $newUsersContent .= "--------------------------------------------------------------\n";
        
        // Prepare content for "email mismatch" users
        $emailMismatchContent = "USERS FOUND BY NAME BUT WITH DIFFERENT EMAIL\n";
        $emailMismatchContent .= "==========================================\n\n";
        $emailMismatchContent .= "Generated on: " . now()->format('Y-m-d H:i:s') . "\n\n";
        $emailMismatchContent .= "BlueLink Email, BlueLink First Name, BlueLink Last Name, DB Email, DB First Name, DB Last Name, BlueLink Customer Number, Phone\n";
        $emailMismatchContent .= "--------------------------------------------------------------\n";

        try {
            if (isset($data['value'])) {
                // Progress bar for better visibility
                $bar = $this->output->createProgressBar(count($data['value']));
                $bar->start();
                
                foreach ($data['value'] as $item) {
                    // First check: Find user by email
                    $user = User::where('email', $item['EMail'])->first();
                    
                    if ($user) {
                        // User found by email
                        $bar->advance();
                        continue;
                    }
                    
                    // Second check: Try to find by name if we have sufficient info
                    $nameMatch = false;
                    if (!empty($item['PatientFirstName']) && !empty($item['PatientLastName'])) {
                        $userByName = User::where('firstname', $item['PatientFirstName'])
                                         ->where('lastname', $item['PatientLastName'])
                                         ->first();
                                    
                        if ($userByName) {
                            // Found by name but with different email
                            $emailMismatchCount++;
                            $nameMatch = true;
                            
                            $emailMismatchContent .= sprintf(
                                "%s, %s, %s, %s, %s, %s, %s, %s\n",
                                $item['EMail'] ?? 'N/A',
                                $item['PatientFirstName'] ?? 'N/A',
                                $item['PatientLastName'] ?? 'N/A',
                                $userByName->email,
                                $userByName->firstname,
                                $userByName->lastname,
                                $item['ID'] ?? 'N/A',
                                $item['Phone'] ?? 'N/A'
                            );
                            
                            $this->line(
                                "<fg=yellow>Email mismatch:</> BlueLink: {$item['EMail']} → DB: {$userByName->email} " .
                                "({$item['PatientFirstName']} {$item['PatientLastName']})"
                            );
                        }
                    }
                    
                    // If not found by email or name, it's a completely new user
                    if (!$nameMatch) {
                        $newUsersCount++;
                        
                        // Write to the new users file
                        $newUsersContent .= sprintf(
                            "%s, %s, %s, %s, %s\n",
                            $item['EMail'] ?? 'N/A',
                            $item['PatientFirstName'] ?? 'N/A',
                            $item['PatientLastName'] ?? 'N/A', 
                            $item['ID'] ?? 'N/A',
                            $item['Phone'] ?? 'N/A'
                        );
                    }
                    
                    $bar->advance();
                }
                
                $bar->finish();
                $this->newLine(2);
            }

            $newUsersContent .= "\n--------------------------------------------------------------\n";
            $newUsersContent .= "Total completely new users: {$newUsersCount}\n";

            $emailMismatchContent .= "\n--------------------------------------------------------------\n";
            $emailMismatchContent .= "Total users with email mismatch: {$emailMismatchCount}\n";

            File::put($newUsersFile, $newUsersContent);
            File::put($emailMismatchFile, $emailMismatchContent);
            
            $this->info("Output files created successfully:");
            $this->line("- Completely new users: {$newUsersCount} (saved to {$newUsersFile})");
            $this->line("- Email mismatch users: {$emailMismatchCount} (saved to {$emailMismatchFile})");
    
        } catch (\Exception $e) {
            $this->error("Error: " . $e->getMessage());
            return Command::FAILURE;
        }
        
        $this->info('--- ENDING SCRIPT ---');

        return Command::SUCCESS;
    }
}
