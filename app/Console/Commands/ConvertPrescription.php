<?php

namespace App\Console\Commands;

use Exception;
use App\Models\Prescription;
use Illuminate\Console\Command;

class ConvertPrescription extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:convert-prescription';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('--- STARTING precsription status CONVERSION ---');
        $prescriptions = Prescription::all();
        $this->info('Converting ' . count($prescriptions) . ' prescriptions.');

        // Loop through each prescriptions
        foreach ($prescriptions as $prescription) {
            try {
                // Convert the UOM
                $prescription->convertStatus();
                $prescription->save();
                $this->info('Prescription ' . $prescription->id . ' converted successfully.');
            } catch (Exception $e) {
                $this->error('Error converting prescription: ' . $e->getMessage());
            }
        }

        $this->info('--- UOM CONVERSION COMPLETE ---');
    }
}
