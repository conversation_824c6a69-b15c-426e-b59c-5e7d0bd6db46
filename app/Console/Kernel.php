<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        $schedule->command('teedy:daily-task')->daily()->at('00:01')->timezone('America/New_York');
        $schedule->command('teedy:close-batch')->daily()->at('22:00')->timezone('America/New_York');
        $schedule->command('teedy:clear-carts')->everyTenMinutes()->timezone('America/New_York');
        $schedule->command('teedy:export-excel')->cron('0 0 1,15 * *')->timezone('America/New_York');
        $schedule->command('teedy:check-discounts')->everyMinute()->timezone('America/New_York');
        $schedule->command('carts:delete-old-frozen')->everyMinute()->timezone('America/New_York');
        $schedule->command('app:sync-inventory-daily')->daily()->at('23:00')->timezone('America/New_York');
        $schedule->command('app:sync-packaging-date')->daily()->at('00:01')->timezone('America/New_York');
        $schedule->command('cache:warm-products')->everyMinute()->timezone('America/New_York');
        $schedule->command('app:sync-sheet-inventory')->daily()->at('00:01')->timezone('America/New_York');
        $schedule->command('app:send-stock-zero-report')->daily()->at('00:01')->timezone('America/New_York');


        // Async BL save order
        $schedule->command('order:process')
        ->withoutOverlapping()
        ->everyMinute()
        ->timezone('America/New_York');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
