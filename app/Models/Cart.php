<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Carbon;

class Cart extends Model
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'carts';
    // protected $primaryKey = 'id';
    // public $timestamps = false;
    protected $guarded = ['id'];
    // protected $fillable = [];
    // protected $hidden = [];
    protected $appends = [
        'errors', 
        'total_price', 
        'total_price_discounted', 
        'promo_code_amount',
        'total_taxes',
        'taxes_monetary_value',
        'shipping_cost',
        'final_price',
        'total_weight', 
        'total_items',
        'total_goods_value',
        'taxes',
        'valid', 
        'free_delivery', 
        'promo_code', 
        'final_price_veteran',
        'total_price_discounted_veteran',
    ];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function items()
    {
        return $this->hasMany(CartProduct::class, 'fk_cart_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'fk_user_id');
    }

    public function promoCode()
    {
        return $this->belongsTo(PromoCode::class, 'fk_promo_code_id');
    }

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeAsApiModel()
    {
        return $this->with('items');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getTotalWeightAttribute()
    {
        $total_weight = 0;
        foreach ($this->items as $item) {
            if ($item->productdetail->productable->quota_value)
                $total_weight += ($item->productdetail->productable->quota_value * $item->qty);
        }
        return $total_weight;
    }

    // This is the total price of the cart, minus potential global promo
    public function getTotalPriceDiscountedAttribute()
    {
        $total_price = 0;
        foreach ($this->items as $item) {
            //check if veteran, if true, dont add the price to the total unless the item is an accessory
            if(Auth::user()->userdetail->veteran && $item->productdetail->productable_type != 'App\Models\ProductAccessory'){
                continue;
            }
            $total_price += (($item->productdetail->discounted_price ?? $item->productdetail->price) * $item->qty);
        }

        return round($total_price, 2);
    }

    // This is the total price of the cart, minus potential global promo ingoring the veteran discount verification
    public function getTotalPriceDiscountedVeteranAttribute()
    {
        $total_price = 0;
        foreach ($this->items as $item) {
            //check if veteran, if true, dont add the price to the total unless the item is an accessory
            $total_price += (($item->productdetail->discounted_price ?? $item->productdetail->price) * $item->qty);
        }

        return round($total_price, 2);
    }

    public function getPromoCodeAmountAttribute()
    {
        if ($this->promo_code) {
            if ($this->promo_code->type == 'percent') {
                return round($this->total_price_discounted * ($this->promo_code->amount / 100), 2);
            } elseif ($this->promo_code->type == 'amount') {
                return $this->promo_code->amount;
            }
        }
        return 0;
    }

    public function getTotalPriceAttribute()
    {
        $total_price = 0;
        foreach ($this->items as $item) {
            //check if veteran, if true, dont add the price to the total unless the item is an accessory
            if(Auth::user()->userdetail->veteran && $item->productdetail->productable_type != 'App\Models\ProductAccessory'){
                continue;
            }
            $total_price += ($item->productdetail->price * $item->qty);
        }

        return round($total_price, 2);
    }

    public function getTotalGoodsValueAttribute()
    {
        $total_price = 0;
        foreach ($this->items as $item) {
            $total_price += ($item->productdetail->price * $item->qty);
            $total_price += (($item->productdetail->discounted_price ?? $item->productdetail->price) * $item->qty);
        }

        return round($total_price, 2);
    }

    public function getTotalItemsAttribute()
    {
        return array_reduce($this->items->toArray(), fn ($sum, $i) => $sum += $i['qty']);
    }

    public function getErrorsAttribute()
    {
        return $this->validateItems();
    }

    public function getValidAttribute()
    {
        return !$this->getErrorsAttribute();
    }

    public function getFreeDeliveryAttribute()
    {
        if(Auth::user()->userdetail->veteran){
            return $this->total_goods_value >= config('constants.cart.min_price_for_free_delivery');
        }
        else{
            return $this->total_price_discounted >= config('constants.cart.min_price_for_free_delivery');
        }
        
    }

    public function getPromoCodeAttribute()
    {
        return $this->promoCode()->first();
    }

    public function GetTaxesAttribute() {
        $sum_taxes = 0;
        $taxes = [];

        $filteredTaxes = Taxe::where('province', $this->user->region)->get();

        foreach ($filteredTaxes as $taxe) {
            $taxes[] = [
                'name' => $taxe->code,
                'value' => $taxe->value,
            ];
            $sum_taxes += $taxe->value / 100;
        }

        return ['taxes' => $taxes, 'sum_taxes' => $sum_taxes];
    }

    public function getTotalTaxesAttribute() {
        $totalTaxes = $this->calculTaxes($this->total_price_discounted + $this->shipping_cost - $this->promo_code_amount, $this->taxes);
        return round($totalTaxes, 2);
    }

    public function getShippingCostAttribute() {
        $shipping = Shipping::getShippingType($this->user->shipping_address->pc);

        // In case they change client postal code to a non valid one..
        if (!$shipping) {
            return 0;
        }
        
        // return 0 if free shipping
        if ($this->promo_code?->type == 'free_shipping') return 0;

        // dd($this->free_delivery);
        // return 0 if cart is over free delivery price
        if ($this->free_delivery) return 0;

        // return raw shipping cost in $
        if ($shipping['type'] == 'fixed') return $shipping['price'];

        // dd(round($this->total_price_discounted * ($shipping['price'] / 100), 2));
        // return shipping cost in % translated to $
        
        //check if user is a veteran, if true, use the total goods value instead of the total price
        if(Auth::user()->userdetail->veteran){
            return round($this->total_goods_value * ($shipping['price'] / 100), 2);
        }else{
            if ($shipping['type'] == 'percent') return round($this->total_price_discounted * ($shipping['price'] / 100), 2);
        }

        return 0;
    }

    // This is the final price of the cart, after all discounts and taxes
    public function getFinalPriceAttribute() {
        return round($this->total_price_discounted + $this->total_taxes + $this->shipping_cost - $this->promo_code_amount, 2);
    }

    // This is the final price of the cart that the veteran would pay (without the veteran discount)
    public function getFinalPriceVeteranAttribute() {
        return round($this->total_price_discounted_veteran + $this->total_taxes + $this->shipping_cost - $this->promo_code_amount, 2);
    }

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    // Validate cart items
    public function validateItems() {
        $errors = null;
        $is_veteran = !!Auth::user()->userdetail->veteran;

        // Error if cart total price is below minimum
        $min_price = config('constants.cart.min_price');
        if($is_veteran){
            if ($this->total_goods_value < $min_price)
                $errors['cart'][] = __('api.cart.under-price', ['min' => $min_price . '$']);
        }else{
            if ($this->total_price_discounted < $min_price)
                $errors['cart'][] = __('api.cart.under-price', ['min' => $min_price . '$']);
        }


        // Error if single order exceeds max weight
        $max_weight = config('constants.cart.max_weight');
        if ($this->total_weight > $max_weight)
            $errors['cart'][] = __('api.cart.over-weight', ['max' => $max_weight . 'g']);

        // Check veteran user total cart
        if ($is_veteran) {
            $remaining = Auth::user()->userdetail->quota_veteran_remaining;

            // Error if cart total is above monthly veteran dosage limit
            if ($this->total_weight > $remaining)
                $errors['cart'][] = __('api.cart.veteran-quota', ['max' => $remaining . 'g']);
        }

        // Check regular user total cart
        if (!$is_veteran) {
            $dosage = Auth::user()->userdetail->quota_user_remaining;

            // Error if cart total is above monthly dosage limit for regular user
            if ($this->total_weight > $dosage)
                $errors['cart'][] = __('api.cart.user-quota', ['max' => $dosage . 'g']);
        }

        // Check each item
        foreach ($this->items as $item) {
            $id = $item['fk_product_id'];
            $product = ProductDetail::find($id);
            $qty = $item['qty'];

            // Error if invalid product
            if (!$product) {
                $errors['cart'][] = __('api.product.not-found', ['id' => $item['fk_product_id']]);
                continue;
            }

            // Error if invalid quantity
            if (!isset($qty) or !is_numeric($qty) or $qty < 0)
                $errors['cart'][] = __('api.product.invalid-qty', ['qty' => $qty]);

            // Error if not enough stock
            if ($product->stock < $qty)
                $errors['cart'][] = __('api.cart.over-stock', ['product' => $product->title]);

            // Check for veteran product
            if ($is_veteran and $product->is_applicable_veteran) {

                // If veteran max items per order not set, use default
                $nb_max_veteran = $product->nb_max_veteran ?? config('constants.cart.veteran_base_nb_items');

                // Error if veteran max product per order exceeded
                if ($qty > $nb_max_veteran)
                    $errors['cart'][] = __('api.cart.veteran-limit', ['max' => $nb_max_veteran, 'product' => $product->title]);
            }

            // Check for regular user product
            if (!$is_veteran or !$product->is_applicable_veteran) {

                // Skip max per person check if product has no limit
                if ($product->max_per_person == null) continue;

                // Error if user max product per order exceeded
                if ($qty > $product->max_per_person)
                    $errors['cart'][] = __('api.cart.user-limit', ['max' => $product->max_per_person, 'product' => $product->title]);
            }

            // Error if product requires a licence (clones ACMPR)
            if ($product->product_type == 'clone' && !$this->licence_acmpr)
                $errors['cart'][] = __('api.cart.acmpr', ['product' => $product->title]);
        }

        return $errors;
    }

    // Validate shipping date and time
    public function validateShipping($shipping_date, $shipping_time) {
        $schedule = $this->user->shippingSchedule();
        return array_key_exists($shipping_date, $schedule) and array_search($shipping_time, $schedule[$shipping_date]) !== false;
    }

    // Calculate the total of taxes
    public function calculTaxes($price, $taxes) {
        $totalTaxes = 0;
        foreach ($taxes['taxes'] as $tax) {
            $totalTaxes += $price * ($tax['value'] / 100);
        }
        return $totalTaxes;
    }

    //return taxes value in money format individualy so we can display them at checkout
    public function getTaxesMonetaryValueAttribute() {
        $price = $this->total_price_discounted + $this->shipping_cost - $this->promo_code_amount;
        $taxes = $this->taxes;
        $taxesValue = [];
        foreach ($taxes['taxes'] as $tax) {
            $taxesValue[$tax['name']] = round($price * ($tax['value'] / 100), 2);
        }
        return $taxesValue;
    }

    public function validatePromoCode($promo, $user)
    {
        $now = Carbon::now();
        if ($promo->start_date > $now || $promo->end_date < $now) {
            return ['status' => false, 'message' => __('api.promo.not-in-time-frame')];
        }

        if ($promo->minimum_price && $this->total_price_discounted < $promo->minimum_price) {
            return ['status' => false, 'message' => __('api.promo.under-minimum', ['min' => $promo->minimum_price])];
        }
        if ($promo->maximum_price && $this->total_price_discounted > $promo->maximum_price) {
            return ['status' => false, 'message' => __('api.promo.over-maximum', ['max' => $promo->maximum_price])];
        }

        if ($promo->minimum_items && $this->total_items < $promo->minimum_items) {
            return ['status' => false, 'message' => __('api.promo.under-minimum-items', ['min' => $promo->minimum_items])];
        }
        if ($promo->maximum_items && $this->total_items > $promo->maximum_items) {
            return ['status' => false, 'message' => __('api.promo.over-maximum-items', ['max' => $promo->maximum_items])];
        }

        $user_orders = $user->orders()->where('fk_promo_code_id', $promo->id)->count();
        if ($promo->nb_per_user && $user_orders >= $promo->nb_per_user) {
            return ['status' => false, 'message' => __('api.promo.used-too-many-times', ['max' => $promo->nb_per_user])];
        }

        return ['status' => true];
    }
}
