<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CartProductFrozen extends CartProduct
{
    use HasFactory;

    protected $table = 'cart_products_frozen';

    //add fillable
    protected $fillable = ['price'];

    public function cartPrice()
    {
        return $this->belongsTo(CartPrice::class, 'fk_cart_id');
    }
}
