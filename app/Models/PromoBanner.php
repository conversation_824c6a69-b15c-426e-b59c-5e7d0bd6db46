<?php

namespace App\Models;

use App\Models\BaseModel;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class PromoBanner extends BaseModel
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'promo_banners';
    protected $guarded = ['id'];
    protected $hidden = ['id', 'content_fr', 'content_en', 'published', 'created_at', 'updated_at'];
    protected $appends = ['content'];


    public function getContentAttribute()
    {
        return $this->localizedColumn('content');
    }

}
