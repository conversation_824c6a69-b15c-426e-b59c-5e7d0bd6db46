<?php

namespace App\Models;

use App\Services\BluelinkService;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Physician extends Model
{
    use CrudTrait;
    use HasFactory;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'physicians';
    // protected $primaryKey = 'id';
    public $timestamps = false;
    protected $guarded = ['id'];
    protected $fillable = [
        'name',
        'bluelink_id',
        'address',
        'city',
        'zip',
        'phone',
        'email',
        'province',
        'created_at',
        'updated_at',
    ];
    // protected $hidden = [];
    // protected $dates = [];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */
    public function BLWeb_GetPhysician()
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetPhysicianPROC\')/Default.Run/', [], [
            'params' => [
                'ProsCode' => $this->bluelink_id, // if null will get everything
            ]
        ]);
        return $result; //return a customer json object
    }

    public function BLWeb_CreatePhysician()
    {
        try{
            $client = new BluelinkService();
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_CreatePhysicianPROC\')/Default.Run/', [], [
                'params' => [
                    'ProsCode' => $this->bluelink_id, // this is the physician id
                    'CUSTNAME' => $this->name, // full name
                    'Address' => $this->address,
                    'City' => $this->city,
                    'Zip' => $this->zip,
                    'state' => $this->province,
                    'Phone' => $this->phone,
                    'Email' => $this->email
                ]
            ]);
            return $result;
        }catch(\Exception $e){
            Log::info('Error in BLWeb_CreatePhysician' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_CreatePhysician',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
 //return a customer json object
    }

    public function BLWeb_UpdatePhysician()
    {
        try{
            $client = new BluelinkService();
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_UpdatePhysicianPROC\')/Default.Run/', [], [
                'params' => [
                    'ProsCode' => $this->bluelink_id, // this is the physician id
                    'CUSTNAME' => $this->name, // full name
                    'Address' => $this->address,
                    'City' => $this->city,
                    'Zip' => $this->zip,
                    'state' => $this->province,
                    'Phone' => $this->phone,
                    'Email' => $this->email
                ]
            ]);
            return $result; //return a customer json object
        }catch(\Exception $e){
            Log::info('Error in BLWeb_UpdatePhysician' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_UpdatePhysician',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }

    }

    
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
