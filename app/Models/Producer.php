<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\Discount;

class Producer extends BaseModel
{
    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'producers';
    protected $guarded = ['id'];
    // Translated fields
    protected $appends = ['title', 'slug', 'description', 'meta_title', 'meta_description'];
    // Hide from API
    protected $hidden = ['title_fr', 'title_en', 'slug_fr', 'slug_en', 'description_fr', 'description_en', 'discounts',
        'meta_title_fr', 'meta_description_fr', 'meta_title_en', 'meta_description_en', 'depth', 'created_at', 'lft', 'rgt', 'updated_at', 'parent_id'
    ];

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    // public function setLogoAttribute($value)
    // {
    //     $this->saveImage($value, $this->title_fr, 'producers-logo', 'logo');
    // }

    // public function setBannerAttribute($value)
    // {
    //     $this->saveImage($value, $this->title_fr, 'producers-banner', 'banner');
    // }

    public function setSlugEnAttribute($value)
    {
        $this->attributes['slug_en'] = $value ?? $this->slug_fr . '_en';
    }


    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    // public function getImageAttribute()
    // {
    //     if (Request::is('admin/*')) return json_decode($value);
    // }

    public function getTitleAttribute()
    {
        return $this->localizedColumn('title');
    }

    public function getSlugAttribute()
    {
        return $this->localizedColumn('slug');
    }

    public function getDescriptionAttribute()
    {
        return $this->localizedColumn('description');
    }

    public function getMetaTitleAttribute()
    {
        return $this->localizedColumn('meta_title');
    }

    public function getMetaDescriptionAttribute()
    {
        return $this->localizedColumn('meta_description');
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function products()
    {
        return $this->hasMany(ProductDetail::class, 'fk_producer_id');
    }

    public function discounts()
    {
        return $this->morphToMany(Discount::class, 'discountable');
    }
}
