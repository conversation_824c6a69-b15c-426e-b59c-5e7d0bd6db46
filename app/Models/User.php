<?php

namespace App\Models;

use DateTime;
use Carbon\Carbon;
use App\Models\Evaluation;
use App\Models\UserDetail;
use Illuminate\Support\Str;
use App\Mail\EmailNotification;
use App\Services\BluelinkService;
use Laravel\Sanctum\HasApiTokens;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;
use Intervention\Image\Facades\Image;
use Spatie\Permission\Traits\HasRoles;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Notifications\Notifiable;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Contracts\Translation\HasLocalePreference;

// Number of days in advance where delivery is possible (to limit reponse size)
const DELIVERY_DAYS_LIMIT = 15;
// Interval for delivery schedule (ex: 12:00 - 14:00 -> +2)
const DELIVERY_HOUR_INTERVAL = 2;

// Here are the possible status for a user
// const STATUS = [
//     'pending',
//     'pending_no_prescription', // default on creation
//     'pending_for_prescription',
//     'pending_prescription_invalid',
//     'active', // active user
//     'disabled'
// ];

class User extends Authenticatable implements HasLocalePreference
{
    use CrudTrait;
    use HasRoles;
    use HasApiTokens, HasFactory, Notifiable;

    protected $fillable = [
        'firstname',
        'lastname',
        'archived',
        'status',
        'email',
        'password',
        'proof_identity',
        'created_at',
        'updated_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
        'created_at', 'updated_at',
        'email_verified_at', 'archived',
        'addresses'
    ];

    protected $casts = [
        'email_verified_at' => 'datetime'
    ];

    protected $guard_name = 'backpack';
    protected $appends = ['full_name', 'client_type', 'shipping_address', 'billing_address', 'shipping_type', 'region'];

    /**
     * Get the user's preferred locale.
     */
    public function preferredLocale(): string
    {
        return $this->userdetail->language;
    }
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function userdetail()
    {
        // $this->file('proof_identity', 'users/' . md5($this->id), 'proofidentity_' . uniqid());
        return $this->hasOne(UserDetail::class, 'fk_user_id');
    }

    public function prescriptions()
    {
        return $this->hasMany(Prescription::class, 'fk_user_id')->orderBy('end_date', 'DESC');
    }

    public function addresses()
    {
        return $this->hasMany(Address::class, 'fk_user_id')->orderBy('last_use', 'DESC');
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'fk_user_id')->orderBy('created_at', 'DESC');
    }

    public function evaluations()
    {
        return $this->hasMany(Evaluation::class, 'fk_user_id');
    }

    public function cart()
    {
        return $this->hasOne(Cart::class, 'fk_user_id');
    }

    public function cartPrice()
    {
        return $this->hasOne(CartPrice::class, 'fk_user_id');
    }

    /*
    |--------------------------------------------------------------------------
    | ATTRIBUTES
    |--------------------------------------------------------------------------
    */
    public function getFullNameAttribute()
    {
        return $this->firstname . ' ' . $this->lastname;
    }

    public function getClientTypeAttribute()
    {
        return ($this->userdetail->veteran == 1) ? 'veteran' : 'regular';
    }

    protected static function boot()
    {
        parent::boot();
        
        static::creating(function ($user) {
            $user->folder_token = (string) Str::uuid();

            if (is_null($user->status)) {
                $user->status = 'pending_no_prescription';
            }
        });
    }

    public function setProofIdentityAttribute($value)
    {
        $attribute_name = "proof_identity";
        $disk = "private"; // or use your own disk, defined in config/filesystems.php
        $destination_path = "users/" . $this->folder_token . "/ProofIdentity"; // path relative to the disk above
        $filename = 'proofidentity-' . uniqid();

        if (empty($value)) {
            // delete the image from disk
            if (isset($this->{$attribute_name}) && !empty($this->{$attribute_name})) {
                Storage::disk($disk)->delete($this->{$attribute_name});
            }
            // set null on database column
            $this->attributes[$attribute_name] = null;
        }
        // if a base64 was sent, store it in the db
        if (Str::startsWith($value, 'data:application/pdf')) {
            $filename = 'prescription-' . uniqid();
            $data = substr($value, strpos($value, ',') + 1);
            $data = base64_decode($data);
            Storage::disk($disk)->put($destination_path . '/' . $filename . '.pdf', $data);

            $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
            $this->attributes[$attribute_name] =  $public_destination_path . '/' . $filename . '.pdf';
        } else if (Str::startsWith($value, 'data:image')) {
            $image = Image::make($value)->encode('jpg', 90);
            $filename = md5($value . time()) . '.jpg';
            Storage::disk($disk)->put($destination_path . '/' . $filename, $image->stream());

            if (isset($this->{$attribute_name}) && !empty($this->{$attribute_name})) {
                Storage::disk($disk)->delete($this->{$attribute_name});
            }
            $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
            $this->attributes[$attribute_name] = $public_destination_path . '/' . $filename;
        } elseif (!empty($value)) {
            $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);
        }
    }

    public function getProofIdentityAttribute($value)
    {
        if (Auth::check() && !Request::is('admin/*') && !empty($value)) {
            return Storage::disk('private')->url($value);
        }
        return $value;
    }

    public function getShippingAddressAttribute()
    {
        return $this->addresses()->first();
    }

    public function getBillingAddressAttribute()
    {
        return $this->addresses()->first();
    }

    public function getShippingTypeAttribute ()
    {
        $delivery_type = Shipping::getShippingDelay(substr($this->shipping_address->pc ?? '', 0, 6));
        return $delivery_type ?? false;
    }

    public function getRegionAttribute ()
    {
        return count($this->addresses) ? $this->addresses[0]->attributes['province'] : null;
    }

    // Send email notification for user / admin
    public function notifyAccountCreated()
    {
        // User
        Mail::send(new EmailNotification($this->email, __('mail.register_user.subject'), 'customer.register', ['user' => $this]));
        
        // Admin
        Mail::send(new EmailNotification(env('MAIL_FROM_ADDRESS_SUPPORT'), __('mail.register_admin.subject'), 'admin.register', ['user' => $this]));
    }

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    // Return the next available dates with their respective shipping schedules
    public function shippingSchedule() {
        // Get shipping delay from client zipcode, fail if zipcode not supported
        $delivery_delay = intval($this->shippingType);
        if (!$delivery_delay) return false;

        // Fetch regular shipping schedule
        $schedule = array_reduce(
            Schedule::where('enabled', true)->orderBy('day', 'ASC')->orderBy('hour', 'ASC')->get()->toArray(),
            // Rearrange schedule structure as [day_index => [hours], ...]
            function($all, $e) {
                if (!array_key_exists($e['day'], $all)) $all[$e['day']] = [];
                $all[$e['day']] = array_merge($all[$e['day']], [$e['hour']]);
                return $all;
            },
            []
        );

        // Fetch next holidays
        $holidays = Holiday::where('date', '>=', Carbon::now()->startOfDay())
                          ->where('active', true)
                          ->get()
                          ->keyBy('date')
                          ->toArray();

        $date = Carbon::now();
        $today = Carbon::now();
        $schedule_by_date = [];
        // Testing
        // $today = Carbon::create(2025, 4, 24, 10, 0); // Vendredi 11 avril 2025 à 16h00
        // $date = $today->copy();


        for ($i = 0; $i <= DELIVERY_DAYS_LIMIT; $i++) {
            $current_date = $date->format('Y-m-d');
            
            $isHoliday = isset($holidays[$current_date]);

            // Add corresponding schedule to date if day is not a holiday and not disabled (weekend)
            if (!$isHoliday && array_key_exists($date->dayOfWeek, $schedule)) {
                // Array of hours [7, 8, 9, ...]
                $schedule_for_date = $schedule[$date->dayOfWeek];

                // Filter out hours that are below delivery delay threshold
                $schedule_for_date = array_values(array_filter(
                    $schedule_for_date,
                    function($s) use($today, $date, $delivery_delay) {
                        // Round up the current time to the next hour
                        $rounded_today = $today->copy()->ceilHour();
                        
                        // Create a date-time object with the set time
                        $date_with_set_time = Carbon::createFromDate($date)->setTimeFromTimeString($s);
                        
                        // Handle specific cases for 1-hour and 2-hour delivery delays
                        if ($delivery_delay == 1 || $delivery_delay == 2) {
                            // Calculate the next available slot based on the delivery delay
                            return $date_with_set_time->greaterThanOrEqualTo($rounded_today);
                        }
                        
                        // For other delivery delays, use the default logic
                        $diff_in_hours = $rounded_today->diffInHours($date_with_set_time, false);
                        return $diff_in_hours >= $delivery_delay;
                    }
                ));

                // Set each hour as interval (ex: 12:00 - 14:00)
                $schedule_by_date[$current_date] = array_map(function ($h) {
                    return $h . ':00 - ' . ($h + DELIVERY_HOUR_INTERVAL) . ':00';
                }, $schedule_for_date);
            }
            // Increment date
            $date->addDay();
        }

        // Particular case for 24h delivery
        if ($delivery_delay === 24) {
            return self::adjustScheduleFor24h($schedule_by_date, $today, $holidays);
        }

        return $schedule_by_date;
    }


    /**
     * Add some particular cases for 24h delivery
     * When a command is placed after 4 p.m. or on weekends or public holidays (non-working days in admin), the next day is not available for delivery.
     */
    private static function adjustScheduleFor24h(array $schedule_by_date, Carbon $today, array $holidays): array
    {

        self::registerHolidays($holidays);
    
        // Special Case: After 4 p.m. or on weekends or public holidays (non-working days)
        if ($today->hour >= 16 || $today->isWeekend() || $today->isHoliday()) {
            // Delete the next day, because teedy team cannot see the command until tomorrow
            $tomorrow = $today->copy()->addDay();
            $tomorrow_formatted = $tomorrow->format('Y-m-d');

            if (isset($schedule_by_date[$tomorrow_formatted])) {
                unset($schedule_by_date[$tomorrow_formatted]);
            }
            
            // If tomorrow is not a working day, also delete the next working day (to let Teedy team see the command, when they are back to work)
            if (!$tomorrow->isBusinessDay()) {
                $nextBusinessDay = $tomorrow->copy()->nextBusinessDay();
                $nextBusinessDay_formatted = $nextBusinessDay->format('Y-m-d');
                // remove it
                if (isset($schedule_by_date[$nextBusinessDay_formatted])) {
                    unset($schedule_by_date[$nextBusinessDay_formatted]);
                }
            }
        }

        return $schedule_by_date;
    }

    /**
     * Save holidays in Carbon
     */
    private static function registerHolidays(array $holidays): void
    {
        // Reset previous holidays
        Carbon::resetHolidays();
        // Set custom holidays with the API data
        Carbon::setHolidays('teedy', $holidays);
        // Ensure that weekends are considered non-working days
        Carbon::setWeekendDays([Carbon::SATURDAY, Carbon::SUNDAY]);
        // Use the custom Holydays region
        Carbon::setHolidaysRegion('teedy');
    }

    // Update the user status based on the prescriptions status
    public function updateStatus()
    {
        $prescriptions = $this->prescriptions()->get();
        $status = 'pending_no_prescription';
    
        if ($prescriptions->contains('status', 'Approved')) {
            $status = 'active';
        } elseif ($prescriptions->contains('status', 'Pending')) {
            $status = 'pending';
        } elseif ($prescriptions->contains(function ($prescription) {
            return in_array($prescription->status, ['Expired', 'Invalid']);
        })) {
            $status = 'pending_prescription_invalid';
        }
    
        Log::info('User status updated', ['user' => $this->id, 'status' => $status]);
    
        $this->status = $status;
        $this->save();
    }

    public function updateQuota($old_quota, $new_quota)
    {

        // temp disabling quota calculation
        // if ($this->userdetail->veteran) {
        //     $difference = $old_quota - $this->userdetail->quota_veteran_remaining;
        //     $this->userdetail->quota_veteran_remaining = $new_quota - $difference;
        // } else {
        //     $difference = $old_quota - $this->userdetail->quota_user_remaining;
        //     $this->userdetail->quota_user_remaining = $new_quota - $difference;
        // }

        if ($this->userdetail->veteran) {
            $this->userdetail->quota_veteran_remaining = $new_quota;
        } else {
            $this->userdetail->quota_user_remaining = $new_quota;
        }
        $this->userdetail->save();
    }

    // Get all the customers from the bluelink erp
    public function BLWeb_GetCustomer()
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetCustomerPROC\')/Default.Run/', [], [
            'params' => [
                'CustID' => $this->userdetail->teedy_client_id,
            ]
        ]);
        return $result; //return a customer json object
    }

    public function convertStatus()
    {
        $this->status = $this->status == 'active' ? 'Approved' : $this->status;
        $this->status = $this->status == 'pending' ? 'Pending' : $this->status;
        $this->status = $this->status == 'pending_prescription_invalid' ? 'Invalid' : $this->status;
        $this->status = $this->status == 'disabled' ? 'Expired' : $this->status;
    }

    // const STATUS = [
    //     'pending',
    //     'pending_no_prescription', // default on creation
    //     'pending_for_prescription',
    //     'pending_prescription_invalid',
    //     'active', // active user
    //     'disabled'
    // ];

    // Update the customer bluelink erp
    public function BLWeb_UpdateCustomer()
    {
        try{
            $client = new BluelinkService();

            // Store original status
            $originalStatus = $this->status;
            
            // Convert status to BlueLink format
            $this->convertStatus();
            $bluelinkStatus = $this->status;
            
            // Restore original status (so we don't modify the user object)
            $this->status = $originalStatus;
            
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_UpdateCustomerPROC\')/Default.Run/', [], [
                'params' => [
                    'CustID' => $this->userdetail->bluelink_id,
                    'CompanyName' => $this->firstname . ' ' . $this->lastname,
                    'PFName' => $this->firstname,
                    'PLName' => $this->lastname,
                    'PDOB' => $this->userdetail->birth_date,
                    'PGend' => $this->userdetail->getRawGenderAttribute(),
                    'PStat' => $bluelinkStatus, // possible status : pending, approved, expired, invalid, but the api seems to accept anything
                    'PExtID' => $this->userdetail->veteran ? $this->userdetail->veteran_id : null, // external id
                ]
            ]);
            return $result; //return a column1 json with 1 or 0
        }catch(\Exception $e){
            Log::info('Error in BLWeb_UpdateCustomer' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_UpdateCustomer',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
    }

    // Create a customer bluelink erp
    public function BLWeb_CreateCustomer()
    {
        try{
            // check if user already has a bluelink id and if he already exist in the bluelink system
            if($this->userdetail->bluelink_id){
                $client = $this->BLWeb_GetCustomer();
                if($client){
                    $this->userdetail->bluelink_synced = true;
                    $this->userdetail->bluelink_id = $this->userdetail->teedy_client_id;
                    $this->userdetail->save();
                    return $client;
                }
            }

            // create a unique id for bluelink
            $uuid = substr(str_shuffle(str_repeat($x='0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil(10/strlen($x)) )),1,10);


            // Format date to be accepted by bluelink
            $datetime = $this->userDetail->birth_date->format('Y-m-d H:i:s');

            $address = $this->addresses()->first();

            $client = new BluelinkService();
            $params = [
                'ID' => $uuid,
                'Company' => $this->firstname . ' ' . $this->lastname,
                'PatientBirthday' => $datetime,
                'Phone' => $this->userdetail->phone,
                'Email' => $this->email,
                'TERMS_DESC' => 'Prepaid',
                'TaxAuthority' => 'QC',
                'CurrencyCode' => 'CA',
                'DefaultShipTo' => 'main',
                'Location' => 'Main',
                'CLASS' => 'Patient',
                'ShipVia' => 'BEST WAY',
                'ContractPricingRule' => 1,
                'PatientFirstName' => $this->firstname,
                'PatientLastName' => $this->lastname,
                'PatientStatus' => 'Pending',
                'PatientGender' => $this->userdetail->getRawGenderAttribute(),
                'PatientExternalID' => $this->userdetail->veteran ? $this->userdetail->veteran_id : null,
                'Country' => 'Canada',
            ];
            
            if ($address) {
                $params['Address1'] = $address->address;
                $params['City'] = $address->city;
                $params['State'] = $address->province;
                $params['Zip'] = $address->pc;
            }
            
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_CreateCustomerPROC\')/Default.Run/', [], [
                'params' => $params
            ]);

            $this->userdetail->bluelink_id = $uuid;
            $this->userdetail->bluelink_synced = true;
            $this->userdetail->save();

            return $result;
            }catch(\Exception $e){
                Log::info('Error in BLWeb_CreateCustomer' . $e->getMessage());
                ApiLog::create([
                    'model' => self::class,
                    'method' => 'BLWeb_CreateCustomer',
                    'error_message' => $e->getMessage(),
                ]);
                return false;
            }
    }

    // Add this to your User model (app/Models/User.php)
    public function generateUnsubscribeToken($type = 'expiry_unsubscribe')
    {
        $data = [
            'user_id' => $this->id,
            'type' => $type,
            'expires_at' => now()->addMonths(6)->timestamp, // Token expires in 6 months
        ];
        
        return encrypt($data);
    }

}
