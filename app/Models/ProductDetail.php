<?php

namespace App\Models;

use Carbon\Carbon;
use App\Models\Discount;
use App\Models\BaseModel;
use App\Models\ProductImage;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use App\Services\ProductCacheService;

class ProductDetail extends BaseModel
{

    protected static function boot()
    {
        parent::boot();

        static::saved(function ($product) {
            if ($product->productable_type == 'App\Models\ProductRegular') {
                Log::channel('cache')->info('Product saved', ['product' => $product->id]);
    
                // Use the cache service to refresh cache for the product's region
                $region = strtoupper($product->region);
                ProductCacheService::refreshCache($region);
            }

            if ($product->productable_type == 'App\Models\ProductAccessory') {
                Log::channel('cache')->info('ProductAccessory saved', ['product' => $product->id]);

                // Use the cache service to refresh cache for all regions since accessories affect all regions
                ProductCacheService::refreshCache();
            }

        });
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table   = 'product_details';
    protected $guarded = ['id'];
    protected $appends = ['title', 'description', 'slug', 'meta_title', 'meta_description', 'product_type',
        'og_image', 'evaluation', 'active_discounts', 'discounted_price', 'related', 'variation', 'current_stock',
        'front_url', 'type_string', 'format', 'unity', 'need_license'];
    protected $hidden = [
        'title_fr', 'title_en', 'description_fr', 'description_en', 'slug_fr', 'slug_en', 'productable',
        'meta_title_fr', 'meta_title_en', 'meta_description_fr', 'meta_description_en', 'evaluations',
        'fk_producer_id', 'fk_variation_of_id', 'productable_id', 'productable_type', 'related_products',
        'discounts', 'created_at', 'updated_at', 'depth', 'parent_id', 'rgt', 'variation_of',
    ];

    protected $casts = [
        'is_applicable_veteran' => 'boolean',
        'publish'               => 'boolean',
        'featured'              => 'boolean'
    ];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function productable()
    {
        return $this->morphTo();
    }
    public function images()
    {
        return $this->hasMany(ProductImage::class, 'fk_productdetail_id')->orderBy('primary', 'DESC');
    }
    public function producer()
    {
        return $this->belongsTo(Producer::class, 'fk_producer_id');
    }
    public function variations()
    {
        return $this->hasMany(ProductDetail::class, 'fk_variation_of_id');
    }
    public function variation_of()
    {
        return $this->belongsTo(ProductDetail::class, 'fk_variation_of_id');
    }
    public function discounts()
    {
        return $this->morphToMany(Discount::class, 'discountable');
    }
    public function evaluations()
    {
        return $this->hasMany(Evaluation::class, 'fk_product_detail_id');
    }
    public function related_products()
    {
        return $this->belongsToMany(ProductDetail::class, 'related_products', 'product_detail_id', 'related_product_detail_id');
    }
    // public function cartProducts()
    // {
    //     return $this->hasMany(CartProduct::class, 'fk_product_id');
    // }
    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    public function scopeAsApiModel()
    {
        return $this
            ->with('productable')
            ->with('productable.category')
            ->with('producer')
            ->with('producer.discounts')
            ->with('discounts')
            ->with('images');
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

    public function setLabelsAttribute($value)
    {
        $this->attributes['labels'] = json_encode($value);
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    // Simply translated columns
    public function getTitleAttribute() { return $this->localizedColumn('title'); }
    public function getDescriptionAttribute() { return $this->localizedColumn('description'); }
    public function getSlugAttribute() { return $this->localizedColumn('slug'); }
    public function getMetaTitleAttribute() { return $this->localizedColumn('meta_title'); }
    public function getMetaDescriptionAttribute() { return $this->localizedColumn('meta_description'); }

    // Backpack - Return label slug
    // Frontend - Return [slug, translated_label] array
    public function getLabelsAttribute($value)
    {
        $value = json_decode($value) ?? [];
        if (Request::is('admin/*')) return $value;

        return array_map(fn ($current_label) => [
            'slug' => $current_label,
            'title' => __('teedy/products.labels')[$current_label]
        ], $value);
    }

    // Returns the number of evaluations for the product with its average evaluation score
    public function getEvaluationAttribute()
    {
        $evs = array_map(fn ($e) => $e->value, $this->evaluations->all());
        $nb = count($evs);
        return ['nb' => $nb, 'score' => $nb ? round(array_sum($evs) / $nb, 1) : null];
    }

    // OG image
    public function getOgImageAttribute()
    {
        $og = $this->images->where('og', 1)->first();
        return $og ? $og->url : null;
    }

    public function getActiveDiscountsAttribute()
    {
        //collect all discounts that are valid
        $validDiscounts = [];

        foreach ($this->discounts()->where('is_active', true)->get() as $discount) {
            if ($this->datesAreValid($discount->start_date, $discount->end_date)) {
                $validDiscounts[] = $discount;
            }
        }

        foreach ($this->producer->discounts()->where('is_active', true)->get() as $discount) {
            if ($this->datesAreValid($discount->start_date, $discount->end_date)) {
                $validDiscounts[] = $discount;
            }
        }

        if ($this->productable_type == 'App\Models\ProductRegular') {
            foreach ($this->productable->category->discounts as $discount) {
                if ($this->datesAreValid($discount->start_date, $discount->end_date) && $discount->is_active) {
                    $validDiscounts[] = $discount;
                }
            }
        }

        if (empty($validDiscounts)) {
            return [];
        }

        // Check if this product is in a cart with a promo code
        // Only check for promo codes if the product is in a cart
        // Check if THIS SPECIFIC PRODUCT is in a cart with a promo code
        /** Don't show discounts if product with a promo code **/
        $cartProducts = CartProduct::where('fk_product_id', $this->id)->get();

        foreach ($cartProducts as $cartProduct) {
            $cart = Cart::where('id', $cartProduct->fk_cart_id)->with(['promoCode', 'user.userdetail'])->first();

            if ($cart) {
                if (isset($cart->promoCode) && $cart->promoCode->type === 'percent') {
                    // Hide discounts for carts with a percent promo
                    return [];
                }

                if (isset($cart->user) && $cart->user->userdetail->veteran) {
                    // Hide discounts if user is veteran
                    return [];
                }
            }
        }


        // Find best discount
        $bestDiscount = array_reduce($validDiscounts, function($carry, $discount) {
            if (!$carry) return $discount;
            
            $carryAmount = $carry->type === 'percentage' ? 
                $this->price * ($carry->amount / 100) : 
                $carry->amount;
                
            $discountAmount = $discount->type === 'percentage' ? 
                $this->price * ($discount->amount / 100) : 
                $discount->amount;
                
            return $discountAmount > $carryAmount ? $discount : $carry;
        });

        return [$bestDiscount];
    
    }

    // Return product price with all global active discounts applied
    public function getDiscountedPriceAttribute()
    {
        // Calculate price with discounts
        $discounted_price = $this->price;

        foreach ($this->active_discounts as $discount) {
            // Fixed amount
            if ($discount->type == 'amount') {
                $discounted_price -= $discount->amount;
            }
            // Percentage
            else if ($discount->type == 'percent') {
                $discounted_price -= ($this->price * $discount->amount / 100);
            }
        }

        $discounted_price = round($discounted_price, 2);

        return $discounted_price != $this->price ? $discounted_price : null;
    }

    // Format the model name as product_type, without the prefixes (ex: 'App\\Models\\ProductRegular' => 'regular')
    public function getProductTypeAttribute()
    {
        $prefix = 'Product';
        $parts = explode('\\', $this->productable_type);
        $suffix = strtolower(end($parts));
        return substr($suffix, strlen($prefix));
    }

    // Returns related products IDs (of ProductDetail)
    public function getRelatedAttribute()
    {
        return $this->related_products
                ->where('publish', 1) // Add condition to filter out unpublished products
                ->pluck('pivot.related_product_detail_id');
    }

    // Returns the variation (which is the parent if any)
    public function getVariationAttribute()
    {
        return ProductDetail::whereHas('related_products', function ($query) {
            $query->where('related_product_detail_id', $this->id);
        })->first();
    }

    // Count product stock considering items in carts
    public function getCurrentStockAttribute()
    {
        $total_in_carts = CartProduct::where('fk_product_id', $this->id)->sum('qty');
        return $this->stock - $total_in_carts;
    }

    // Returns translated category and product slugs
    public function getFrontUrlAttribute()
    {
        // dd($this->productable->category);
        return $this->productable->category['slug'] . '/' . $this->slug;
    }

    // Returns a string containing the product type and cannabinoids list separated with •
    public function getTypeStringAttribute()
    {
        if (!$this->productable->cannabinoids) return null;
        if (!$this->productable->type) return null;
        $s = ' • ';
        $category = $this->productable->type['title'];
        $cannabinoids = array_map(fn ($c) => $s . $c->title . ': ' . $c->amount . $c->format, $this->productable->cannabinoids);
        return $category . join('', $cannabinoids);
    }

    // Returns a string containing the product quantity, format and unity (ex: '5 units' or '2x3.5g')
    public function getFormatAttribute()
    {
        if ($this->productable_type == 'App\Models\ProductClone') return $this->productable->format;

        if ($this->productable_type == 'App\Models\ProductAccessory') return $this->productable->size;

        // Use sub-format if available
        $qty = $this->productable->sub_format_quantity ? $this->productable->sub_format_quantity . 'x' : '';
        $format = $this->productable->sub_format ?: $this->productable->format;

        return $qty . $format
            // Add space
            . ' '
            . __('teedy/products.unity.' . $this->productable->unity);
    }

    // Returns the translated product unity (ex: 'g' or 'unit')
    public function getUnityAttribute()
    {
        if (!in_array($this->productable_type, ['App\Models\ProductRegular', 'App\Models\ProductAccessory'])) {
            return '';
        }
        
        return __('teedy/products.unity.' . $this->productable->unity);
    }

    // Returns true if the product is a clone
    public function getNeedLicenseAttribute()
    {
        return $this->productable_type == 'App\Models\ProductClone';
    }

    public function BLWeb_SyncStock()
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetInventoryPROC\')/Default.Run/', [], [
            'params' => [
                'ProdCode' => $this->sku,
            ]
        ]);
        return $result;
    }

    public function reorder($tree)
    {
        $count = 1;
        foreach ($tree as $item) {
            $this->where('id', $item['id'])->update(['featured_order' => $count]);
            $count++;
        }
    }

    public function getRemoveFeaturedButton()
    {
        // Determine which controller we're in based on the current route
        $currentRoute = request()->route()->getName();
        $baseRoute = 'featured-products';
        
        if (str_contains($currentRoute, 'featured-products-qc')) {
            $baseRoute = 'featured-products-qc';
        } elseif (str_contains($currentRoute, 'featured-products-on')) {
            $baseRoute = 'featured-products-on';
        }
        
        return '<a href="' . url(config('backpack.base.route_prefix') . '/' . $baseRoute . '/' . $this->id . '/remove-featured') . '" 
                class="btn btn-sm btn-outline-danger" 
                onclick="return confirm(\'Êtes-vous sûr de vouloir retirer ce produit des produits vedettes?\')"
                title="Retirer des produits vedettes">
                    <i class="la la-times"></i>
                </a>';
    }

    /*
    |--------------------------------------------------------------------------
    | HELPERS
    |--------------------------------------------------------------------------
    */

    // Discounts - Check if current date is between start and end
    private function datesAreValid($start = null, $end = null)
    {
        // Ignore discount if start date is in the future
        $ds = $start ? Carbon::createFromFormat('Y-m-d', $start) : null;
        if ($ds and Carbon::now()->endOfDay()->lt($ds->endOfDay())) return false;

        // Ignore discount if end date is passed
        $de = $end ? Carbon::createFromFormat('Y-m-d', $end) : null;
        if ($de and Carbon::now()->endOfDay()->gt($de->endOfDay())) return false;

        return true;
    }
}
