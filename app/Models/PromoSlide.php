<?php

namespace App\Models;

use App\Models\BaseModel;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class PromoSlide extends BaseModel
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'promo_slides';
    protected $guarded = ['id'];
    protected $hidden = ['id', 'published', 'created_at', 'updated_at',
        'title_fr', 'text_fr', 'link_fr', 
        'title_en', 'text_en', 'link_en', 'image_en'];
    protected $appends = ['title', 'text', 'link'];


    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    // public function setImageAttribute($value)
    // {
    //     if (strpos($value, 'promo-slides/') === 0) {
    //         return;
    //     }
    //     $this->saveImage($value, $this->title, 'promo-slides', 'image');
    // }

    // public function setImageEnAttribute($value)
    // {
    //     if (strpos($value, 'promo-slides/') === 0) {
    //         return;
    //     }
    //     $this->saveImage($value, $this->title, 'promo-slides', 'image');
    // }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    
    public function getTitleAttribute()
    {
        return $this->localizedColumn('title');
    }

    public function getTextAttribute()
    {
        return $this->localizedColumn('text');
    }

    public function getLinkAttribute()
    {
        return $this->localizedColumn('link');
    }


    public function getImageAttribute($value)
    {

        if(!Request::is('admin/*')){
            if(App::getLocale() == 'en') {
                $value = $this->attributes['image_en'];
            } else {
                $value = $this->attributes['image'];
            }
        }
        return (Request::is('admin/*')) ? $value : ($value ? url(Storage::url($value)) : null);

    }

}
