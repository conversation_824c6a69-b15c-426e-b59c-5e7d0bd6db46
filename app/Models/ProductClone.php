<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\ProductDetail;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;

class ProductClone extends BaseModel
{

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'product_clones';
    protected $guarded = ['id'];
    // Dynamically hidden in products api controller
    // protected $hidden = ['id', 'fk_category_id', 'created_at', 'updated_at'];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function productdetail()
    {
        return $this->morphOne(ProductDetail::class, 'productable');
    }

    // Fake relation to simplify API response
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'fake');
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    public function setCannabinoidsAttribute($value)
    {
        $this->attributes['cannabinoids'] = json_encode($value);
    }

    public function setAromasAttribute($value)
    {
        $this->attributes['aromas'] = json_encode($value);
    }

    public function setTerpenesAttribute($value)
    {
        $this->attributes['terpenes'] = json_encode($value);
    }

    public function setAdditionalDetailsAttribute($value)
    {
        $this->attributes['additional_details'] = json_encode($value);
    }

    public function setCertificatAttribute($value)
    {
        $this->saveImage($value, $this->title_fr, 'certificat', 'certificat');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getCertificatAttribute($value)
    {
        if (empty($value)) return null;
        
        $appUrl = config('app.url');
        return $appUrl . Storage::url($value);
    }

    public function getCategoryAttribute($value)
    {
        if (Request::is('admin/*')) return null;
        $word = App::getLocale() == 'fr' ? 'boutures' : 'clones';
        return ['title' => ucfirst($word), 'slug' => $word];
    }

    public function getCannabinoidsAttribute ($value)
    {
        if (Request::is('admin/*')) return $value;
        return array_map(function ($current) {
            $current->title = __('teedy/products.cannabinoids')[$current->type];
            $current->format = __('teedy/products.formats')[$current->format];
            return $current;
        }, json_decode($value) ?: []);
    }

    public function getTypeAttribute ($value)
    {
        if (Request::is('admin/*')) return $value;
        return ['slug' => $value, 'title' => __('teedy/products.types.' . $value)];
    }

    public function getTerpenesAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        return $this->mergeTranslatedTitleToArray($value, 'terpenes');
    }

    public function getAdditionalDetailsAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        return $this->mergeTranslatedTitleToArray($value, 'additional_details');
    }

    public function getIntensityAttribute ($value)
    {
        if (Request::is('admin/*')) return $value;
        return __('teedy/products.intensities.' . $value);
    }

    //
    private function mergeTranslatedTitleToArray($value, $type)
    {
        return array_map(function ($current) use ($type) {
            $current->title = __('teedy/products.' . $type)[$current->type];
            return $current;
        }, json_decode($value) ?: []);
    }

    public function getOpenButton()
    {
        return '<a class="btn btn-sm btn-link" href="' . env('APP_URL') . '/api/clone/' . $this->id . '/sync">' .
               '<i class="la la-eye"></i> Synchroniser avec BlueLink</a>';
    }

    public function BLWeb_SyncInventory()
    {
        $data = $this->BLWeb_GetInventory();
        if(empty($data->value)) return false;
        $uoh = $data->value[0]->{'UOH in DisplayUOM'};
        $this->productdetail->update(['stock' => $uoh]);
        return true;

    }

    public function BLWeb_GetInventory()
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetInventoryPROC\')/Default.Run/', [], [
            'params' => [
                'ProdCode' => $this->productdetail->sku,
            ]
        ]);
        // dd(!empty($result->value));
        if(!empty($result->value)){
            $this->productdetail->update(['bluelink_synced' => 1]);
        }else{
            $this->productdetail->update(['bluelink_synced' => 0]);
        }
        return $result; // return a column1 json with 1 or 0
    }
}
