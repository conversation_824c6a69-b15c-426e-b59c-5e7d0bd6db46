<?php

namespace App\Models;

use App\Models\BaseModel;
// use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class CustomPage extends BaseModel
{
    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'pages';
    protected $guarded = ['id'];
    protected $appends = ['slug', 'title', 'subtitle', 'content', 'meta_title', 'meta_description', 'url'];
    protected $hidden = [
        'slug_fr', 'title_fr', 'subtitle_fr', 'content_fr', 'meta_title_fr', 'meta_description_fr',
        'slug_en', 'title_en', 'subtitle_en', 'content_en', 'meta_title_en', 'meta_description_en',
        'show_on_top', 'created_at', 'updated_at', 'image'
    ];

    /*
    |--------------------------------------------------------------------------
    | SETTERS
    |--------------------------------------------------------------------------
    */

    // public function setImageAttribute ($value) {
    //     $this->saveImage($value, $this->title_fr, 'pages', 'image');
    // }


    /*
    |--------------------------------------------------------------------------
    | GETTERS
    |--------------------------------------------------------------------------
    */

    public function getUrlAttribute()
    {
        if (!$this->image) return null;
        return request()->getSchemeAndHttpHost() . Storage::url($this->image);
    }

    public function getCategoryAttribute ($value) {
        if (Request::is('admin/*')) return $value;
        return __('teedy/pages.category_slugs.' . $value);
    }

    public function getSlugAttribute () {
        return $this->localizedColumn('slug');
    }

    public function getTitleAttribute () {
        return $this->localizedColumn('title');
    }

    public function getSubtitleAttribute () {
        return $this->localizedColumn('subtitle');
    }

    public function getContentAttribute () {
        return $this->localizedColumn('content');
    }

    public function getMetaTitleAttribute () {
        return $this->localizedColumn('meta_title');
    }

    public function getMetaDescriptionAttribute () {
        return $this->localizedColumn('meta_description');
    }

    public function getPublishAttribute ($value) {
        return !!$value;
    }
}
