<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;

class GlobalContent extends Model
{

    use CrudTrait;

    protected $table = 'global_content';

    protected $fillable = ['name', 'type', 'data'];
    protected $hidden = ['id', 'created_at', 'updated_at'];
    protected $appends = ['data_formatted'];
    protected $casts = [
        'data' => 'array',
    ];


    // Bypass the mutator
    public function getDataFormattedAttribute()
    {
        return json_decode($this->attributes['data']);
    }

    // Accessor to decode JSON when retrieving data
    public function getDataAttribute($value)
    {
        $decodedValue = json_decode($value, true);
        $transformedData = [];

        foreach ($decodedValue as $key => $content) {
            $transformedData[] = ['key' => $key, 'content' => $content];
        }

        return $transformedData;
    }

    // Mutator to encode data to JSON when saving
    public function setDataAttribute($value)
    {
        // Transform the array of objects into an associative array
        $transformedData = [];
        foreach ($value as $item) {
            if (isset($item['key']) && isset($item['content'])) {
                $transformedData[$item['key']] = $item['content'];
            }
        }
        $this->attributes['data'] = json_encode($transformedData);
    }
}