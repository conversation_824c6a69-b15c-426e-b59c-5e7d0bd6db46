<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ApiLog extends Model
{
    use CrudTrait;
    use HasFactory;

    protected $table = 'api_logs';
    public $timestamps = true;
    protected $guarded = ['id'];
    protected $fillable = ['model', 'method', 'error_message'];
}
