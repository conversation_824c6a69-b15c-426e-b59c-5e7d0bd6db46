<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class BaseModel extends Model
{
    use CrudTrait;

    // Return the _fr or _en version of the column, based on the current language
    protected function localizedColumn($column_prefix)
    {
        return $this->{$column_prefix . '_' . App::getLocale()} ?: $this->{$column_prefix . '_fr'};
    }

    // Save image field as png image and set path as attribute + delete old image if needed
    protected function saveImage($data, $name, $folder = '', $attr_name = 'image')
    {
        $new_path = saveFile($data, Str::slug($name), $folder);
        $current_path = $this->attributes[$attr_name] ?? null;

        // Delete old image if it's been replaced
        if ($current_path and $current_path != $new_path)
            Storage::disk('public')->delete($current_path);

        $this->attributes[$attr_name] = $new_path;
    }
}
