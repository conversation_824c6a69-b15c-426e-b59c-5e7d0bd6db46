<?php

namespace App\Models;

use App\Models\BaseModel;

// const TYPE = [
//     'fixed' => 'Fixed',
//     'free_shipping' => 'Free',
//     'percent' => 'Percentage',
// ];

class PromoCode extends BaseModel
{
    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'promo_codes';
    protected $guarded = ['id'];

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    public function setMinimumItemsAttribute($value)
    {
        $this->attributes['minimum_items'] = intval($value);
    }

    public function setMaximumItemsAttribute($value)
    {
        $this->attributes['maximum_items'] = intval($value);
    }

    public function setNbPerUserAttribute($value)
    {
        $this->attributes['nb_per_user'] = intval($value);
    }
}
