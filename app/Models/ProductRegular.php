<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\ProductCategory;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Services\ProductCacheService;

class ProductRegular extends BaseModel
{

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($productRegular) {
            Log::channel('cache')->info('ProductRegular deleted', ['product_regular' => $productRegular->id]);
            
            // Use the cache service to refresh cache for all regions since we don't have region info at this level
            ProductCacheService::refreshCache();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'product_regulars';
    protected $guarded = ['id'];
    protected $appends = ['effects', 'benefits', 'product_detail_id'];

    // Dynamically hidden in products api controller
    protected $hidden = ['effect_relaxed', 'effect_sleepy', 'effect_euphoric', 'effect_energic', 'effect_happy', 'benefits_stress', 'benefits_pain', 'benefits_depress', 'benefits_appetite', 'benefits_sleep'];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    public function productdetail()
    {
        return $this->morphOne(ProductDetail::class, 'productable');
    }

    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'fk_category_id');
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */

    // Remove cannabinoids with value 0 before save
    public function setCannabinoidsAttribute($value)
    {
        $filtered_values = array_filter($value ?? [], fn ($e) => $e['amount'] != 0);
        $this->attributes['cannabinoids'] = json_encode($filtered_values);
    }

    // Remove empty aromas before save
    public function setAromasAttribute($value)
    {
        $filtered_values = array_filter($value ?? [], fn ($e) => $e['fr'] || $e['en']);
        $this->attributes['aromas'] = json_encode($filtered_values);
    }

    // Remove terpenes with 0% before save
    public function setTerpenesAttribute($value)
    {
        $filtered_values = $value; // array_filter($value ?? [], fn ($e) => $e['percentage'] != 0);
        $this->attributes['terpenes'] = json_encode($filtered_values);
    }

    public function setCertificatAttribute($value)
    {
        $this->saveImage($value, $this->title_fr, 'certificat', 'certificat');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    public function getCertificatAttribute($value)
    {
        if (empty($value)) return null;
        
        $appUrl = config('app.url');
        return $appUrl . Storage::url($value);
    }

    // Return translated cannabinoid full name and format
    public function getCannabinoidsAttribute($value)
    {
        if (Request::is('admin/*')) return $value;

        return array_map(function ($current) {
            $current->title = __('teedy/products.cannabinoids')[$current->type];
            $current->format = __('teedy/products.formats')[$current->format];
            return $current;
        }, json_decode($value) ?: []);
    }

    // public function getAromasAttribute($value)
    // {
    //     if (Request::is('admin/*')) return $value;
    //     return $this->mergeTranslatedTitleToArray($value, 'aromas');
    // }

    public function getTerpenesAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        return $this->mergeTranslatedTitleToArray($value, 'terpenes');
    }

    public function getTypeAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        if (empty($value)) return null;
        return ['slug' => $value, 'title' => __('teedy/products.types.' . $value)];
    }

    public function getIntensityAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        return __('teedy/products.intensities.' . $value);
    }

    public function getDominantAttribute($value)
    {
        if (!$value) return null;
        if (Request::is('admin/*')) return $value;
        return __('teedy/products.cannabinoids.' . $value);
    }

    public function getEffectsAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        $props = ['relaxed', 'sleeepy', 'euphoric', 'energic', 'happy'];
        $effects = [];
        foreach ($props as $p) {
            $effects[$p] = $this['effect_' . $p];
        }
        return $effects;
    }

    public function getBenefitsAttribute($value)
    {
        if (Request::is('admin/*')) return $value;
        $props = ['stress', 'pain', 'depress', 'appetite', 'sleep'];
        $benefits = [];
        foreach ($props as $p) {
            $benefits[$p] = $this['benefits_' . $p];
        }
        return $benefits;
    }

    public function getProductDetailIdAttribute($value)
    {
        return $this->productdetail->id;
    }

    private function mergeTranslatedTitleToArray($value, $type)
    {
        return array_map(function ($current) use ($type) {
            $current->title = __('teedy/products.' . $type)[$current->type];
            return $current;
        }, json_decode($value) ?: []);
    }

    public function getOpenButton()
    {
        return '<a class="btn btn-sm btn-link" href="' . env('APP_URL') . '/admin/product/' . $this->id . '/sync">' .
               '<i class="la la-eye"></i> Synchroniser avec BlueLink</a>';
    }

    public function BLWeb_SyncInventory()
    {
        $data = $this->BLWeb_GetInventory();
        if(empty($data->value)) return false;
        $uoh = $data->value[0]->{'UOH in DisplayUOM'};
        $this->productdetail->update(['stock' => $uoh]);
        return true;

    }

    public function BLWeb_GetInventory()
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetInventoryPROC\')/Default.Run/', [], [
            'params' => [
                'ProdCode' => $this->productdetail->sku,
            ]
        ]);
        // dd(!empty($result->value));
        if(!empty($result->value)){
            $this->productdetail->update(['bluelink_synced' => 1]);
        }else{
            $this->productdetail->update(['bluelink_synced' => 0]);
        }
        return $result; // return a column1 json with 1 or 0
    }

    public function convertUOM()
    {
        $conversionMap = [
            'g' => 'GR',
            'ml' => 'ML',
            'unit' => 'EA',
            // Add more conversions as needed
        ];

        $this->unity = $conversionMap[$this->unity] ?? $this->unity;
    }
}
