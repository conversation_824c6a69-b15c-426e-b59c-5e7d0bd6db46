<?php

namespace App\Models;

use App\Models\ApiLog;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class Address extends Model
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'addresses';
    protected $guarded = ['id'];
    protected $appends = ['pc_formated'];
    protected $hidden = ['id', 'fk_user_id', 'created_at', 'updated_at'];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function user()
    {
        return $this->belongsTo(User::class, 'fk_user_id');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public function getPcFormatedAttribute()
    {
        if (!empty($this->pc)) {
            $value = $this->pc;
            $result = strtoupper(substr($value, 0, 3) . ' ' . substr($value, 3));
            return $result;
        }
        return null;
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    public function setPcAttribute($value)
    {
        $this->attributes['pc'] = strtoupper(str_replace([' ', '-'], '', $value));
    }

    public function getProvinceAttribute($value)
    {
        if (!Request::is('admin/*') && !empty($value)) {
            return __('common.provinces.' . strtolower($value));
        }
        // dd($value);
        return $value;
    }

    // public function getProvinceAttribute($value)
    // {
    //     if (!empty($value)) {
    //         if (Request::is('admin/*')) {
    //             return $value;
    //         }
    //         return __('common.provinces.' . $value);
    //     }
    //     return null;
    // }

    // public function getRawProvinceAttribute()
    // {
    //     if (!empty($this->getRawOriginal('province'))) {
    //         return $this->getRawOriginal('province');
    //     }
    //     return null;
    // }

    public function getProvinceCodeForBluelink()
    {
        $provinceMapping = [
            'Québec' => 'QC',
            'Ontario' => 'ON',
            'British Columbia' => 'BC',
            'Alberta' => 'AB',
            'Manitoba' => 'MB',
            'Saskatchewan' => 'SK',
            'Nova Scotia' => 'NS',
            'New Brunswick' => 'NB',
            'Newfoundland and Labrador' => 'NL',
            'Prince Edward Island' => 'PE',
            'Northwest Territories' => 'NT',
            'Nunavut' => 'NU',
            'Yukon' => 'YT'
        ];

        $province = $this->user->addresses->first()->getRawOriginal('province');
        return $provinceMapping[$province] ?? $province;
    }

    //bluelink set address
    public function BLWeb_CreateAddress()
    {
        try{
            $client = new BluelinkService();
            $result = $client->createUrl('POST', 'Customers(' . '\'' . $this->user->userdetail->bluelink_id . '\'' . ')/ShipToLocations', [], [
                'ShipCode' => $this->shipcode, // cant be updated afterward, it's use to qry and update the address
                'ShipName' => $this->shipname,
                'ShipAddress1' => substr($this->address, 0, 30), // for wathever reason BL seems to only accept 30 char
                'ShipAddress2' => substr($this->address, 30),
                'ShipCity' => $this->city,
                'ShipZip' => $this->pc,
                'ShipState' => $this->province,
                'ShipCountry' => 'Canada',
            ]);
    
        }catch(\Exception $e){
            Log::info('Error in BLWeb_CreateAddress' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_CreateAddress',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
    }

    public function BLWeb_UpdateAddress()
    {
        try{
            $client = new BluelinkService();
            $result = $client->createUrl('PATCH', 'Customers(' . '\'' . $this->user->userdetail->bluelink_id . '\'' . ')/ShipToLocations(' . '\'' . $this->shipcode . '\'' . ')', [], [
                'ShipName' => $this->shipname,
                'ShipAddress1' => substr($this->address, 0, 30), // for wathever reason BL seems to only accept 30 char
                //add the rest from $this-address that doesnt fit the limit of 30 char into shipaddress2. this field doesnt have a limit
                'ShipAddress2' => substr($this->address, 30),
                'ShipCity' => $this->city,
                'ShipZip' => $this->pc,
                'ShipState' => $this->province,
                'ShipCountry' => 'Canada',
            ]);
            //also update address on the user bluelink table
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_UpdateCustomerPROC\')/Default.Run/', [], [
                'params' => [
                    'CustID' => $this->user->userdetail->bluelink_id,
                    'Address1' => substr($this->address, 0, 30), // for wathever reason BL seems to only accept 30 char
                    'Address2' => substr($this->address, 30),
                    'City' => $this->city,
                    'Zip' => $this->pc,
                    'State' => $this->province,
                ]
            ]);
    
        }catch(\Exception $e){
            Log::info('Error in BLWeb_UpdateAddress' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_UpdateAddress',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }

        
    }
}
