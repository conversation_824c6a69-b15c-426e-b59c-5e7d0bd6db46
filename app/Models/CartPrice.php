<?php

namespace App\Models;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class CartPrice extends Cart
{
    use HasFactory;

    protected $table = 'cartprice';

    protected $guarded = ['id'];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($cartPrice) {
            $cartPrice->TTL = Carbon::now()->addMinutes(15);
        });
    }

    public function getTotalWeightAttribute()
    {
        $total_weight = 0;
        foreach ($this->itemsFrozen as $item) { // Use itemsFrozen instead of items
            if ($item->productdetail->productable->quota_value)
                $total_weight += ($item->productdetail->productable->quota_value * $item->qty);
        }
        return $total_weight;
    }

    // Override the method to use the frozen price
    public function getTotalPriceDiscountedAttribute()
    {
        $total_price = 0;
        foreach ($this->itemsFrozen as $item) {
            //check if veteran, if true, dont add the price to the total unless the item is an accessory
            if(Auth::user()->userdetail->veteran && $item->productdetail->productable_type != 'App\Models\ProductAccessory'){
                continue;
            }
            $total_price += (($item->productdetail->discounted_price ?? $item->productdetail->price) * $item->qty);
        }

        return round($total_price, 2);
    }

    // This is the total price of the cart, minus potential global promo ingoring the veteran discount verification
    public function getTotalPriceDiscountedVeteranAttribute()
    {
        $total_price = 0;
        foreach ($this->itemsFrozen as $item) {
            //check if veteran, if true, dont add the price to the total unless the item is an accessory
            $total_price += (($item->productdetail->discounted_price ?? $item->productdetail->price) * $item->qty);
        }

        return round($total_price, 2);
    }



    public function getTotalItemsAttribute()
    {
        return array_reduce($this->itemsFrozen->toArray(), fn ($sum, $i) => $sum += $i['qty']); // Use itemsFrozen instead of items
    }


    public function getFreeDeliveryAttribute()
    {
        if(Auth::user()->userdetail->veteran){
            return $this->total_goods_value >= config('constants.cart.min_price_for_free_delivery');
        }
        else{
            return $this->total_price_discounted >= config('constants.cart.min_price_for_free_delivery');
        }
    }


    public function itemsFrozen()
    {
        return $this->hasMany(CartProductFrozen::class, 'fk_cart_id');
    }

    public function getTimeLeft()
    {
        return Carbon::now()->diffInSeconds($this->time_to_live);
    }

    public function isExpired()
    {
        return Carbon::now()->gt($this->getExpirationTime());
    }
}
