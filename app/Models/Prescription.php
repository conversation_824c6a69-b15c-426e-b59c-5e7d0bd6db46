<?php

namespace App\Models;

use App\Models\BaseModel;
use Illuminate\Support\Str;
use Illuminate\Support\Carbon;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\Log;
use Intervention\Image\Facades\Image;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Storage;
use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Casts\Attribute;

// Possible status values
// const STATUS = [
//     'valid',
//     'pending',
//     'invalid',
//     'expired'
// ];

class Prescription extends BaseModel
{
    use CrudTrait;

    protected $table = 'prescriptions';
    public $timestamps = false;
    protected $guarded = ['id'];
    protected $fillable = [
        'fk_user_id', 'prescription_number', 'code_admin', 'doc_name', 'daily_dosage', 'validity', 'start_date', 'end_date', 'status', 'prescription_photo',
    ];
    



    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function user()
    {
        return $this->belongsTo(User::class, 'fk_user_id');
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    protected function prescriptionPhoto(): Attribute
    {
        // $patate = "patate";
        // $patate2 strtoupper($patate);
        // return Attribute::make(
        //     // get: fn ($value) => $patate,
        //     set: function ($value) {
        //         $patate = "patate";

        //         if ($value) {
        //             $value = $patate;
        //         }
                
        //         return $value;
        //     }
        // );

        return Attribute::make(
            get: function ($value) {
                return $value;
            },
            set: function ($value) {
                Log::info('Prescription photo value:' . $value);
                $attribute_name = "prescription_photo";
                $disk = "private"; // or use your own disk, defined in config/filesystems.php
                $destination_path = "users" . $this->folder_token . "/prescription"; // path relative to the disk above
                $filename = 'prescription-' . uniqid();

                if (empty($value)) {
                    // delete the image from disk
                    Log::info('No photo value');
                    if (isset($this->{$attribute_name}) && !empty($this->{$attribute_name})) {
                        Storage::disk($disk)->delete($this->{$attribute_name});
                    }
                    // set null on database column
                    $this->attributes[$attribute_name] = null;
                    return null;
                }
                //this bit of code is blasphemy but it works, need refactoring a bit
                if (is_object($value) && method_exists($value, 'getMimeType')){
                    // if a base64 was sent, store it in the db
                    if (Str::startsWith($value->getMimeType(), 'application/pdf')) {
                        Log::info('type pdf');
                        $filename = 'prescription-' . uniqid();
                        $data = substr($value, strpos($value, ',') + 1);
                        $data = base64_decode($data);
                        Storage::disk($disk)->put($destination_path . '/' . $filename . '.pdf', $data);

                        $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
                        $this->attributes[$attribute_name] =  $public_destination_path . '/' . $filename . '.pdf';
                        return $destination_path . '/' . $filename . '.pdf';
                    } else if (Str::startsWith($value->getMimeType(), 'image')) {
                        Log::info('type image');
                        $image = Image::make($value)->encode('jpg', 90);
                        $filename = md5($value . time()) . '.jpg';
                        Storage::disk($disk)->put($destination_path . '/' . $filename, $image->stream());

                        if (isset($this->{$attribute_name}) && !empty($this->{$attribute_name})) {
                            Storage::disk($disk)->delete($this->{$attribute_name});
                        }
                        $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
                        $this->attributes[$attribute_name] = $public_destination_path . '/' . $filename;
                        Log::info('Image path set: ' . $this->attributes[$attribute_name]);
                        return $this->attributes[$attribute_name];
                    } elseif (!empty($value)) {
                        Log::info('just storing it object');
                        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);
                    }else{
                        Log::info('what the sigma');
                    }
                }else{
                    // if a base64 was sent, store it in the db
                    if (Str::startsWith($value, 'data:application/pdf')) {
                        Log::info('type pdf');
                        // dd('pdf');
                        $filename = 'prescription-' . uniqid();
                        $data = substr($value, strpos($value, ',') + 1);
                        $data = base64_decode($data);
                        Storage::disk($disk)->put($destination_path . '/' . $filename . '.pdf', $data);

                        $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
                        $this->attributes[$attribute_name] =  $public_destination_path . '/' . $filename . '.pdf';
                        return $destination_path . '/' . $filename . '.pdf';
                    } else if (Str::startsWith($value, 'data:image')) {
                        Log::info('type image');
                        $image = Image::make($value)->encode('jpg', 90);
                        $filename = md5($value . time()) . '.jpg';
                        Storage::disk($disk)->put($destination_path . '/' . $filename, $image->stream());

                        if (isset($this->{$attribute_name}) && !empty($this->{$attribute_name})) {
                            Storage::disk($disk)->delete($this->{$attribute_name});
                        }
                        $public_destination_path = Str::replaceFirst('public/', '', $destination_path);
                        $this->attributes[$attribute_name] = $public_destination_path . '/' . $filename;
                        Log::info('Image path set: ' . $this->attributes[$attribute_name]);
                        return $this->attributes[$attribute_name];
                    } elseif (!empty($value)) {
                        Log::info('just storing it string');
                        $this->uploadFileToDisk($value, $attribute_name, $disk, $destination_path);
                    }else{
                        Log::info('what the sigma');
                    }
                }

            }
        );
    
    }

    // public static function boot()
    // {
    //     parent::boot();
    //     static::deleted(function ($obj) {
    //         Storage::disk('public')->delete($obj->prescription_photo);
    //     });
    // }

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    //convert old prescription status naming to new name
    public function convertStatus()
    {
        $this->status = $this->status == 'valid' ? 'Approved' : $this->status;
        $this->status = $this->status == 'pending' ? 'Pending' : $this->status;
        $this->status = $this->status == 'invalid' ? 'Invalid' : $this->status;
        $this->status = $this->status == 'expired' ? 'Expired' : $this->status;
    }

    //simple get prescriptions here
    public function BLWeb_GetPrescription($prescription_number)
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetPrescriptionPROC\')/Default.Run/', [], [
            'params' => [
                "PrescriptionName" => $prescription_number // is prescription id inside BL
            ]
        ]);
        return $result; // return a customer json object
    }


    public function BLWeb_CreatePrescription()
    {
        try{
            $client = new BluelinkService();

            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_CreatePrescriptionsPROC\')/Default.Run/', [], [
                'params' => [
                    'CustomerCode' => $this->user->userdetail->bluelink_id,
                    'PrescriptionName' => $this->prescription_number, // this cant be updated, its used to know which prescription to update
                    'PrescribedBy' => $this->doc_name, // physician code
                    'DailyDosage' => $this->daily_dosage / 30, // save monthly in db
                    "LimitDays" =>  Carbon::parse($this->start_date)->diffInDays(Carbon::parse($this->end_date)),
                    'StartDate' => $this->start_date,
                    'ExpiryDate' => $this->end_date,
                    'Active' => $this->status == 'Approved' ? 1 : 0,
                    'PrescriptionVerified' => $this->status == 'Approved' ? 1 : 0,
                    // 'ReferredBy' => $this->clinic_name, // physician code
                    'Condition' => 'misc', // fixed value  
                    'CannabisPrescriptionCategoryCode' => 'Generic', // fixed value
                ]
            ]);

            //update customer with new physician
            $client2 = new BluelinkService();
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_UpdateCustomerPROC\')/Default.Run/', [], [
                'params' => [
                    'CustID' => $this->user->userdetail->bluelink_id,
                    'PPhy' => $this->doc_name,
                    'PStat' => $this->status,
                ]
            ]);


            $this->bluelink_synced = true;
            $this->save();
            
            return $result; // return a column1 json with 1 or 0
        }catch(\Exception $e){
            Log::info('Error in BLWeb_CreatePrescription' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_CreatePrescription',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
    }

    // Update prescription
    public function BLWeb_UpdatePrescription()
    {
        try{
            $client = new BluelinkService();
            
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_UpdatePrescriptionPROC\')/Default.Run/', [], [
                'params' => [
                    'CustomerCode' => $this->user->userdetail->bluelink_id,
                    'PrescriptionName' => $this->prescription_number, // this cant be updated, its used to know which prescription to update
                    'PrescribedBy' => $this->doc_name, // physician code
                    'DailyDosage' => $this->daily_dosage / 30, // save monthly in db
                    "LimitDays" =>  Carbon::parse($this->start_date)->diffInDays(Carbon::parse($this->end_date)),
                    'StartDate' => $this->start_date,
                    'ExpiryDate' => $this->end_date,
                    'Active' => $this->status == 'Approved' ? 1 : 0,
                    'PrescriptionVerified' => $this->status == 'Approved' ? 1 : 0,
                    'ReferredBy' => $this->clinic_name, // physician code
                    'Condition' => 'misc', // fixed value  
                    'CannabisPrescriptionCategoryCode' => 'Generic', // fixed value

                ]
            ]);

            //update customer with new physician
            $client2 = new BluelinkService();
            $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_UpdateCustomerPROC\')/Default.Run/', [], [
                'params' => [
                    'CustID' => $this->user->userdetail->bluelink_id,
                    'PPhy' => $this->doc_name,
                    'PStat' => $this->status
                ]
            ]);

            $this->bluelink_synced = true;
            $this->save();
            return $result; // return a column1 json with 1 or
        }catch(\Exception $e){
            Log::info('Error in BLWeb_UpdatePrescription' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_UpdatePrescription',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }
    }
}   
