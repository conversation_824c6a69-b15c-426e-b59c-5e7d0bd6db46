<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Request;
use App\Services\ProductCacheService;

class ProductAccessory extends BaseModel
{

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($productAccessory) {
            Log::channel('cache')->info('ProductAccessory deleted', ['product_accessory' => $productAccessory->id]);
            
            // Use the cache service to refresh cache for all regions since accessories affect all regions
            ProductCacheService::refreshCache();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'product_accessories';
    protected $guarded = ['id'];
    // Dynamically hidden in products api controller
    // protected $hidden = ['id', 'fk_category_id', 'created_at', 'updated_at'];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function productdetail()
    {
        return $this->morphOne(ProductDetail::class, 'productable');
    }

    // Fake relation to simplify API response
    public function category()
    {
        return $this->belongsTo(ProductCategory::class, 'fake');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public function getCategoryAttribute($value)
    {
        if (Request::is('admin/*')) return null;
        $word = App::getLocale() == 'fr' ? 'accessoires' : 'accessories';
        return ['title' => ucfirst($word), 'slug' => $word];
    }

    public function getOpenButton()
    {
        return '<a class="btn btn-sm btn-link" href="' . env('APP_URL') . '/api/accessories/' . $this->id . '/sync">' .
               '<i class="la la-eye"></i> Synchroniser avec BlueLink</a>';
    }

    public function BLWeb_SyncInventory()
    {
        $data = $this->BLWeb_GetInventory();
        if(empty($data->value)) return false;
        $uoh = $data->value[0]->{'UOH in DisplayUOM'};
        $this->productdetail->update(['stock' => $uoh]);
        return true;

    }

    public function BLWeb_GetInventory()
    {
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'StoredProcedures(\'BLWeb_GetInventoryPROC\')/Default.Run/', [], [
            'params' => [
                'ProdCode' => $this->productdetail->sku,
            ]
        ]);
        // dd(!empty($result->value));
        if(!empty($result->value)){
            $this->productdetail->update(['bluelink_synced' => 1]);
        }else{
            $this->productdetail->update(['bluelink_synced' => 0]);
        }
        return $result; // return a column1 json with 1 or 0
    }
}
