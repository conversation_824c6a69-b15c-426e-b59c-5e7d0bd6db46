<?php

namespace App\Models;

use App\Models\BaseModel;

class Holiday extends BaseModel
{
    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table   = 'holidays';
    protected $guarded = ['id'];
    protected $hidden  = ['created_at', 'updated_at'];



    public function getActiveAttribute()
    {
        return $this->attributes['active'] ? 'Oui' : 'Non';
    }

}
