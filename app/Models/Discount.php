<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\Producer;
use App\Models\ProductCategory;
use App\Models\ProductDetail;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Log;
use App\Services\ProductCacheService;

class Discount extends BaseModel
{
    protected static function boot()
    {
        parent::boot();

        static::saved(function ($discount) {
            Log::channel('cache')->info('Discount saved', ['discount' => $discount->title_fr]);

            // Use the cache service to refresh cache for all regions since discounts affect products globally
            ProductCacheService::refreshCache();
        });

        static::deleted(function ($discount) {
            Log::channel('cache')->info('Discount deleted', ['discount' => $discount->title_fr]);

            // Use the cache service to refresh cache for all regions since discounts affect products globally
            ProductCacheService::refreshCache();
        });
    }

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table   = 'discounts';
    protected $guarded = ['id'];
    protected $appends = ['title'];
    protected $hidden  = ['created_at', 'updated_at', 'pivot', 'title_fr', 'title_en'];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function products()
    {
        return $this->morphedByMany(ProductDetail::class, 'discountable');
    }

    public function producers()
    {
        return $this->morphedByMany(Producer::class, 'discountable');
    }

    public function categories()
    {
        return $this->morphedByMany(ProductCategory::class, 'discountable');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */ 
    public function getTitleAttribute()
    {
        return $this->localizedColumn('title');
    }
}
