<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\ProductDetail;
use Illuminate\Support\Facades\Storage;

class ProductImage extends BaseModel
{
    protected $table   = 'product_images';
    protected $guarded = ['id'];
    protected $appends = ['url'];
    protected $hidden  = ['depth', 'created_at', 'lft', 'rgt', 'updated_at', 'parent_id', 'fk_productdetail_id'];
    protected $fillable = ['path', 'alt_text', 'primary', 'og'];
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function product()
    {
        return $this->belongsTo(ProductDetail::class, 'fk_productdetail_id');
    }

    public function getUrlAttribute()
    {
        return request()->getSchemeAndHttpHost() . Storage::url($this->path);
    }
}
