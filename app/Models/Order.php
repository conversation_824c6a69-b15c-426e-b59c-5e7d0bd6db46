<?php

namespace App\Models;

use App\Models\ApiLog;
use App\Services\BluelinkService;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Log;
use Illuminate\Database\Eloquent\Model;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

// Possible status values
// const STATUS = [
//     'in_progress',
//     'shipped',
//     'delivered',
//     'cancelled'
// ];

class Order extends Model
{
    use CrudTrait;

    protected $table = 'orders';
    protected $guarded = ['id'];
    protected $cats = [
            'order_content' => 'json',
            'additional_content' => 'json',
        ];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */
    public function changeStatus()
    {
        $html = '<button class="btn btn-link dropdown-toggle" type="button" data-toggle="dropdown" aria-expanded="false">' . __('teedy/orders.orders.labels.change_status') . '</button>';
        $html .= '<div class="dropdown-menu" style="">';
        foreach (__('teedy/orders.orders.status_values') as $key => $value) {
            $html .= '<a class="dropdown-item';
            $html .= ($this->status == $key) ? ' disabled' : '';
            $html .= '" href="' . route('order.update_status', ['id' => $this->id, 'new_status' => $key]) . '">' . $value . '</a>';
        }
        $html .= '</div>';
        return $html;
    }

    public function BLWeb_Createorder()
    {
        try{
            $content_order = json_decode($this->order_content);

            $footerItems = [];
            foreach ($content_order->product_list as $product) {
                $footerItems[] = [
                    'ProductCode' => $product->sku,
                    'ProductDescription' => $product->product_description_bl ?: $product->product_title_fr,
                    'Quantity' => $product->quantity,
                    'Price' => $product->base_price,
                    'UOM' => $product->uom,
                    'Discount' => $product->discount,
                ];
            }
            Log::info('Footer Items: ' . json_encode($footerItems));
    
            
        $temp = [
            'webReference' => $this->user->userdetail->teedy_client_id . '_' . time(),
            'order' => [
                'CustomerCode' => $this->user->userdetail->bluelink_id,
                'ShipCode' => null,
                'ShipVia' => 'BEST WAY',
                'Location' => 'Main', // Warehouse location
                'TaxAuthority' => strtoupper($this->user->addresses->first()->getProvinceCodeForBluelink()),
                'Freight' => $content_order->shipping_cost,
                'InvoiceDiscount' => $content_order->cart_discount_percentage,
                'FooterItems' => $footerItems,
            ]
        ];
        Log::info('Temp: ' . json_encode($temp));

            $client = new BluelinkService();
            $result = $client->createUrl('POST', 'Orders/Default.CreateOrder/', [], [
                'webReference' => $this->user->userdetail->teedy_client_id . '_' . time(),
                'order' => [
                    'CustomerCode' => $this->user->userdetail->bluelink_id,
                    'ShipCode' => null,
                    'ShipVia' => 'BEST WAY',
                    'Location' => 'Main', // Warehouse location
                    'TaxAuthority' => strtoupper($this->user->addresses->first()->getProvinceCodeForBluelink()), // yes i know it's weird but QC in db get converted to Quebec by default because of get Attr in model
                    'Freight' => $content_order->shipping_cost,
                    'InvoiceDiscount' => $content_order->cart_discount_percentage,
                    'FooterItems' => $footerItems,
                ]
            ]);
    
            $bluelinkResponse = $result;
    
            //check if we get a web service transaction id, if so, check the WebServiceTransactions() method to confirm the order
            if ($bluelinkResponse->WebServiceTransactionID) {
                // Make the second API call to check the status of the order
                \Illuminate\Support\Facades\Log::info('Bluelink create Result dump: ' . $bluelinkResponse->WebServiceTransactionID);
                //wait 15 seconds (WebServiceTransactions() takes some time to update the status)
                sleep(1);

                $statusResult = $client->createUrl('GET', 'WebServiceTransactions(' . $bluelinkResponse->WebServiceTransactionID . ')', [], []);

                if($statusResult){

                    \Illuminate\Support\Facades\Log::info('Status Response existing: ' . $statusResult->Status);
                    \Illuminate\Support\Facades\Log::info('Status Response body: ' . json_encode($statusResult));

                    // Extract SourceReference from the Headers array
                    $headers = $statusResult->Headers;
                    if (!empty($headers) && isset($headers[0]->SourceReference)) {
                        $sourceReference = $headers[0]->SourceReference;
                        \Illuminate\Support\Facades\Log::info('Source Reference: ' . $sourceReference);

                        // Remove the dot and everything after
                        $sourceReferenceParts = explode('.', $sourceReference);
                        $sourceReference = $sourceReferenceParts[0];
                        \Illuminate\Support\Facades\Log::info('Clean Source Reference: ' . $sourceReference);
                    } else {
                        \Illuminate\Support\Facades\Log::info('Source Reference not found');
                        $sourceReference = ''; // Default value if not found
                    }

                    $statusResponse = $statusResult;
                    if($statusResponse->Status == 'OK')
                    {
                        \Illuminate\Support\Facades\Log::info('Status ok');
                        $this->bluelink_status = $statusResponse->Status;
                        $this->sales_order_number = $sourceReference ?? ''; // still saying its undefined even if its ok, really weird UPDATE: bluelink is not reliable for update messages
                        $this->web_service_transaction_id = $bluelinkResponse->WebServiceTransactionID;
                        $this->save(); // might cause problem if saving before the end, gonna leave it like that for now
                        return $statusResponse->WebServiceTransactionID;
                    }elseif($statusResponse->Status == 'New'){
                        \Illuminate\Support\Facades\Log::info('Status new, will retry in a minute');
                        $this->bluelink_status = $statusResponse->Status;
                        $this->web_service_transaction_id = $bluelinkResponse->WebServiceTransactionID;
                        $this->save();
                        return $statusResponse->WebServiceTransactionID;
                    }
                    else{
                        \Illuminate\Support\Facades\Log::info('Status not ok');
                        return false;
                    }
                }else{

                    return false;
                }
                
            }else{
                return false;
            }
        }catch(\Exception $e){
            Log::info('Error in BLWeb_Createorder' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_Createorder',
                'error_message' => $e->getMessage(),
            ]);
            return false;
        }



    }

    public function BLWeb_GetOrderStatus()
    {
        $client = new BluelinkService();
        $statusResult = $client->createUrl('GET', 'WebServiceTransactions(' . $this->web_service_transaction_id . ')', [], []);

        if($statusResult){

            \Illuminate\Support\Facades\Log::info('Status Response existing: ' . $statusResult->Status);
            \Illuminate\Support\Facades\Log::info('Status Response body: ' . json_encode($statusResult));

            // Extract SourceReference from the Headers array
            $headers = $statusResult->Headers;
            if (!empty($headers) && isset($headers[0]->SourceReference)) {
                $sourceReference = $headers[0]->SourceReference;
                \Illuminate\Support\Facades\Log::info('Source Reference: ' . $sourceReference);

                // Remove the dot and everything after
                $sourceReferenceParts = explode('.', $sourceReference);
                $sourceReference = $sourceReferenceParts[0];
                \Illuminate\Support\Facades\Log::info('Clean Source Reference: ' . $sourceReference);
            } else {
                \Illuminate\Support\Facades\Log::info('Source Reference not found');
                $sourceReference = ''; // Default value if not found
            }

            $statusResponse = $statusResult;
            if($statusResponse->Status == 'OK')
            {
                \Illuminate\Support\Facades\Log::info('Status ok');
                $this->bluelink_status = $statusResponse->Status;
                $this->sales_order_number = $sourceReference ?? '';
                $this->save();
                return $statusResponse->WebServiceTransactionID;
            }elseif($statusResponse->Status == 'New'){
                \Illuminate\Support\Facades\Log::info('Status new, will retry in a minute');
                $this->bluelink_status = $statusResponse->Status;
                $this->save();
                return $statusResponse->WebServiceTransactionID;
            }
            elseif($statusResponse->Status == 'Error'){
                \Illuminate\Support\Facades\Log::info('Status not ok');
                $this->bluelink_status = $statusResponse->Status;
                $this->additional_content = $statusResponse->ErrorMessage;
                $this->save();
                return false;
            }else{
                \Illuminate\Support\Facades\Log::info('Status not ok');
                return false;
            }
        }else{

            return false;
        }
    }

    public function BLWeb_UpdateOrder($new_status)
    {
        // dd($new_status);
        $client = new BluelinkService();
        if($this->sales_order_number){
            $result = $client->createUrl('PATCH', 'Orders('. $this->sales_order_number . ')/', [], [
                'OrderStatus' => $new_status,
            ]);
            $this->status = $new_status;
            $this->save();
            Alert::success(__('teedy/orders.orders.status_changing'))->flash();
        }else{
            Alert::error('No sales order number')->flash();
        }

    }

    public function Refund(){
        if (env('APP_ENV') === 'local') {
            $store_id = env('MONERIS_STORE_ID_TEST');
            $api_token = env('MONERIS_API_TOKEN_TEST');
        } else {
            $store_id = env('MONERIS_STORE_ID');
            $api_token = env('MONERIS_API_TOKEN');
        }

        $orderid = $this->moneris_id;
        $txnnumber = $this->txn_number;
        $dynamic_descriptor = 'Teedy - Refund';

        ## step 1) create transaction array ###
        $txnArray = array(
            'type' => 'refund',
            'txn_number' => $txnnumber,
            'order_id' => $orderid,
            'amount' => '1.00',
            'crypt_type' => '7',
            'cust_id' => $this->user->userdetail->teedy_client_id,
            'dynamic_descriptor' => $dynamic_descriptor
        );

        ## step 2) create a transaction object passing the array created in
        ## step 1.
        $mpgTxn = new \mpgTransaction($txnArray);

        ## step 3) create a mpgRequest object passing the transaction object created
        ## in step 2
        $mpgRequest = new \mpgRequest($mpgTxn);
        $mpgRequest->setProcCountryCode("CA"); //"US" for sending transaction to US environment
        $mpgRequest->setTestMode(env('MONERIS_TEST_MODE'));

        ## step 4) create mpgHttpsPost object which does an https post ##
        $mpgHttpPost = new \mpgHttpsPost($store_id, $api_token,$mpgRequest);

        ## step 5) get an mpgResponse object ##
        $mpgResponse = $mpgHttpPost->getMpgResponse();

        ## step 6) retrieve data using get methods
        print ("\nCardType = " . $mpgResponse->getCardType());
        print("\nTransAmount = " . $mpgResponse->getTransAmount());
        print("\nTxnNumber = " . $mpgResponse->getTxnNumber());
        print("\nReceiptId = " . $mpgResponse->getReceiptId());
        print("\nTransType = " . $mpgResponse->getTransType());
        print("\nReferenceNum = " . $mpgResponse->getReferenceNum());
        print("\nResponseCode = " . $mpgResponse->getResponseCode());
        print("\nISO = " . $mpgResponse->getISO());
        print("\nMessage = " . $mpgResponse->getMessage());
        print("\nIsVisaDebit = " . $mpgResponse->getIsVisaDebit());
        print("\nAuthCode = " . $mpgResponse->getAuthCode());
        print("\nComplete = " . $mpgResponse->getComplete());
        print("\nTransDate = " . $mpgResponse->getTransDate());
        print("\nTransTime = " . $mpgResponse->getTransTime());
        print("\nTicket = " . $mpgResponse->getTicket());
        print("\nTimedOut = " . $mpgResponse->getTimedOut());

        // Sync order delete in bluelink
        if(env('SYNC_BLUELINK')){
            try{
                $this->BLWeb_DeleteOrder();
            }catch(\Exception $e){
                Log::info('Error in BLWeb_DeleteOrder' . $e->getMessage());
                ApiLog::create([
                    'model' => self::class,
                    'method' => 'BLWeb_DeleteOrder',
                    'error_message' => $e->getMessage(),
                ]);
            }
        }

        $this->status = 'cancelled';

        $userdetails = $this->user->userdetail;
        if($userdetails->veteran){
            $userdetails->quota_veteran_remaining += $this->total_quota_weight;
        }else{
            $userdetails->quota_user_remaining += $this->total_quota_weight;
        }
        $userdetails->save();
        $this->save();
    }

    public function PurchaseCorrection(){
        if (env('APP_ENV') === 'local') {
            $store_id = env('MONERIS_STORE_ID_TEST');
            $api_token = env('MONERIS_API_TOKEN_TEST');
        } else {
            $store_id = env('MONERIS_STORE_ID');
            $api_token = env('MONERIS_API_TOKEN');
        }
        $orderid = $this->moneris_id;
        $txnnumber = $this->txn_number;
        // $txnnumber = "none";
        $dynamic_descriptor = 'Teedy - Void';

        ## step 1) create transaction hash ###
        $txnArray = array(
            'type' => 'purchasecorrection',
            'txn_number' => $txnnumber,
            'order_id' => $orderid,
            'crypt_type' => '7',
            'cust_id' => $this->user->userdetail->teedy_client_id,
            'dynamic_descriptor' => $dynamic_descriptor
        );

        ## step 2) create a transaction object passing the array created in
        ## step 1.
        $mpgTxn = new \mpgTransaction($txnArray);

        ## step 3) create a mpgRequest object passing the transaction object created
        ## in step 2
        $mpgRequest = new \mpgRequest($mpgTxn);
        $mpgRequest->setProcCountryCode("CA"); //"US" for sending transaction to US environment
        $mpgRequest->setTestMode(env('MONERIS_TEST_MODE'));

        ## step 4) create mpgHttpsPost object which does an https post ##
        $mpgHttpPost = new \mpgHttpsPost($store_id,$api_token,$mpgRequest);

        ## step 5) get an mpgResponse object ##
        $mpgResponse = $mpgHttpPost->getMpgResponse();

        ## step 6) retrieve data using get methods
        print("\nCardType = " . $mpgResponse->getCardType());
        print("\nTransAmount = " . $mpgResponse->getTransAmount());
        print("\nTxnNumber = " . $mpgResponse->getTxnNumber());
        print("\nReceiptId = " . $mpgResponse->getReceiptId());
        print("\nTransType = " . $mpgResponse->getTransType());
        print("\nReferenceNum = " . $mpgResponse->getReferenceNum());
        print("\nResponseCode = " . $mpgResponse->getResponseCode());
        print("\nISO = " . $mpgResponse->getISO());
        print("\nMessage = " . $mpgResponse->getMessage());
        print("\nIsVisaDebit = " . $mpgResponse->getIsVisaDebit());
        print("\nAuthCode = " . $mpgResponse->getAuthCode());
        print("\nComplete = " . $mpgResponse->getComplete());
        print("\nTransDate = " . $mpgResponse->getTransDate());
        print("\nTransTime = " . $mpgResponse->getTransTime());
        print("\nTicket = " . $mpgResponse->getTicket());
        print("\nTimedOut = " . $mpgResponse->getTimedOut());

        $this->status = 'cancelled';

        // Sync order delete in bluelink
        try{
            $this->BLWeb_DeleteOrder();
        }catch(\Exception $e){
            Log::info('Error in BLWeb_DeleteOrder' . $e->getMessage());
            ApiLog::create([
                'model' => self::class,
                'method' => 'BLWeb_DeleteOrder',
                'error_message' => $e->getMessage(),
            ]);
        }

        //restore the stock removed by the order NOT FINISHED
        // $content_order = json_decode($this->order_content);
        // foreach ($content_order->product_list as $product) {
        //     $product_detail = ProductDetail::where('sku', $product->sku)->first();
        //     $product_detail->quantity += $product->quantity;
        //     $product_detail->save();
        // }

        //restore quota value from users
        $userdetails = $this->user->userdetail;
        if($userdetails->veteran){
            $userdetails->quota_veteran_remaining += $this->total_quota_weight;
        }else{
            $userdetails->quota_user_remaining += $this->total_quota_weight;
        }
        $userdetails->save();


        $this->save();
    }

    public function BLWeb_DeleteOrder(){
        $client = new BluelinkService();
        $result = $client->createUrl('POST', 'Orders/(\''. $this->sales_order_number . '\')/', [], [
        ]);
    }
    protected function createItemsArrayFromJson()
    {
        
        $items = [];
        foreach ($this->order_content as $item) {
            $items[] = [
                'productCode' => $item['product_code'],
                'Quatity' => $item['quantity'],
                'UOM' => $item['uom']
            ];
        }
        return $items;
    }

    // Convert a discount amount to a percentage for BL
    public static function discountToPercentage($total, $base_total)
    {
        if ($total <= 0) {
            return 0;
        }
        
        $discount = $total - $base_total;
        return round($discount / $total, 2);
    }
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function user()
    {
        return $this->belongsTo(User::class, 'fk_user_id');
    }
}
