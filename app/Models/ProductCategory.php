<?php

namespace App\Models;

use App\Models\BaseModel;
use App\Models\ProductRegular;
use App\Models\Discount;

class ProductCategory extends BaseModel
{
    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'product_categories';
    protected $guarded = ['id'];
    protected $appends = ['title', 'slug', 'discounts'];
    protected $hidden = [
        'title_fr', 'title_en', 'slug_fr', 'slug_en',
        'created_at', 'updated_at', 'depth', 'lft', 'parent_id', 'rgt'
    ];

    protected static function boot()
    {
        parent::boot();
        
        static::addGlobalScope('ordered', function ($builder) {
            $builder->orderBy('lft', 'asc');
        });
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function products()
    {
        return $this->hasMany(ProductRegular::class);
    }

    public function discounts()
    {
        return $this->morphToMany(Discount::class, 'discountable');
    }

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public function getTitleAttribute()
    {
        return $this->localizedColumn('title');
    }
    public function getSlugAttribute()
    {
        return $this->localizedColumn('slug');
    }

    public function getDiscountsAttribute()
    {
        $discounts = $this->discounts()->get();
        return $discounts;
    }
}
