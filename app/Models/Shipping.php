<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;


// const SHIPPING_TYPES = [
//     'fixed' => 'Fixed',
//     'free' => 'Free',
//     'percent' => 'Percentage',
// ];
class Shipping extends Model
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'shippings';
    protected $guarded = ['id'];
    protected $hidden = ['id', 'created_at', 'updated_at', 'rgt', 'depth', 'parent_id', 'lft'];

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public function getPostalCodesAttribute($value)
    {
        if (Request::is('admin/*')) {
            return $value;
        }
        if (!$value) return null;

        // Return array if API
        return array_map('trim', explode(',', $value));
    }

    // public static function getShippingDelay($zipcode) {
    //     $zipcode = strtoupper($zipcode);
    //     $shippings = Shipping::where('postal_codes', '!=', null)->get();

    //     foreach ($shippings as $shipping) {
    //         if (in_array($zipcode, (array)$shipping['postal_codes'])) return $shipping?->delivery_type;
    //     }
    //     return false;
    // }

    public static function getShippingDelay($zipcode) {
        $zipcode = strtoupper($zipcode);
        $shippings = Shipping::where('postal_codes', '!=', null)->get();
    
        foreach ($shippings as $shipping) {
            // Check excluded codes first
            $excludedCodes = json_decode($shipping->postal_codes_excluded ?? '[]', true);
            // if($shipping->delivery_type == "24h"){
            //     dd($excludedCodes, $zipcode);
            // }
            foreach ($excludedCodes as $excluded) {
                if ($excluded['code'] === $zipcode) {
                    // dd($excluded['code'], $zipcode, $shipping->delivery_type);
                    continue 2; // Skip to next shipping option
                }
            }
    
            // Check included codes
            $includedCodes = json_decode($shipping->postal_codes_included ?? '[]', true);
            foreach ($includedCodes as $included) {
                // Check 3-char prefix match
                if (strlen($included['code']) == 3 && substr($zipcode, 0, 3) === $included['code']) {
                    return $shipping->delivery_type;
                }
                // Check full 6-char match
                // dd($included['code'], $zipcode);
                if (strlen($included['code']) == 6 && $included['code'] === $zipcode) {
                    return $shipping->delivery_type;
                }
            }
            if($shipping->delivery_type == "24h"){
                // dd($zipcode, (array)$shipping['postal_codes']);
                // dd(in_array(substr($zipcode, 0, 3), (array)$shipping['postal_codes']));
            }
            if (in_array(substr($zipcode, 0, 3), (array)$shipping['postal_codes'])) return $shipping?->delivery_type;
        }
        
        return false;
    }

    // public static function getShippingType($zipcode) {
    //     $zipcode = strtoupper($zipcode);
    //     $shippings = Shipping::where('postal_codes', '!=', null)->get();
    //     foreach ($shippings as $shipping) {
    //         if (in_array($zipcode, $shipping['postal_codes'])) return ['type' => $shipping->type, 'price' => $shipping->price];
    //     }
    //     return false;
    // }

    public static function getShippingType($zipcode) {
        $zipcode = strtoupper($zipcode);
        $shippings = Shipping::where('postal_codes', '!=', null)->get();
        
        foreach ($shippings as $shipping) {
            // Check excluded codes first
            $excludedCodes = json_decode($shipping->postal_codes_excluded ?? '[]', true);
            foreach ($excludedCodes as $excluded) {
                if ($excluded['code'] === $zipcode) {
                    continue 2; // Skip to next shipping option
                }
            }
    
            // Check included codes
            $includedCodes = json_decode($shipping->postal_codes_included ?? '[]', true);
            foreach ($includedCodes as $included) {
                // Check 3-char prefix match
                if (strlen($included['code']) == 3 && substr($zipcode, 0, 3) === $included['code']) {
                    return ['type' => $shipping->type, 'price' => $shipping->price];
                }
                // Check full 6-char match
                if (strlen($included['code']) == 6 && $included['code'] === $zipcode) {
                    return ['type' => $shipping->type, 'price' => $shipping->price];
                }
            }
            
            // Check legacy postal codes
            if (in_array(substr($zipcode, 0, 3), (array)$shipping['postal_codes'])) {
                return ['type' => $shipping->type, 'price' => $shipping->price];
            }
        }
        
        return false;
    }

    
}
