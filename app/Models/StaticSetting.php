<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\App;

class StaticSetting extends Model
{
    use CrudTrait;
    use HasFactory;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'static_settings';
    // protected $primaryKey = 'id';
    // public $timestamps = false;
    protected $guarded = ['id'];
    // protected $fillable = [];
    protected $hidden = ['created_at', 'updated_at', 'id'];

    /*
    |--------------------------------------------------------------------------
    | FUNCTIONS
    |--------------------------------------------------------------------------
    */

    /**
     * Get the localized version of this setting
     * 
     * @return array
     */
    public function getLocalizedData()
    {
        $locale = App::getLocale(); // Get current locale (fr or en)

        $data = [
            'identifiant' => $this->identifiant,
            'banner_image' => $this->banner_image ? asset("storage/" . $this->banner_image) : null,
        ];
        
        // Get all attributes
        $attributes = $this->getAttributes();
        
        // Loop through attributes to find localized fields
        foreach ($attributes as $key => $value) {
            // Check if this is a localized field (_en or _fr suffix)
            if (preg_match('/_(' . $locale . ')$/', $key, $matches)) {
                // Extract the base field name without locale suffix
                $baseFieldName = str_replace('_' . $matches[1], '', $key);
                
                // Add to data with the base field name
                $data[$baseFieldName] = $value;
            }
        }
        
        return $data;
    }

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | SCOPES
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
}
