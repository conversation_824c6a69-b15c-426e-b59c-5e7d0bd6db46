<?php

namespace App\Models;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Request;
use Backpack\CRUD\app\Models\Traits\CrudTrait;

class UserDetail extends Model
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */

    protected $table = 'user_details';

    protected $guarded = ['id'];

    protected $casts = [
        'symptoms'         => 'array',
        'consumption'      => 'array',
        'licence_acmpr'    => 'bool',
        'veteran'          => 'bool',
        'birth_date'       => 'datetime:Y-m-d',
        'veteran_quota_date' => 'datetime:Y-m-d',
    ];
    protected $appends = ['phone_formated'];
    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function user()
    {
        return $this->belongsTo(User::class, 'fk_user_id');
    }
    /*
    |--------------------------------------------------------------------------
    | ACCESSORS
    |--------------------------------------------------------------------------
    */
    public function getPhoneFormatedAttribute()
    {
        if (!empty($this->phone)) {
            $value = $this->phone;
            $result = substr($value, 0, 3) . '-' . substr($value, 3, 3) . '-' . substr($value, 6);
            return $result;
        }
        return null;
    }

    /*
    |--------------------------------------------------------------------------
    | MUTATORS
    |--------------------------------------------------------------------------
    */
    public function setPhoneAttribute($value)
    {
        $this->attributes['phone'] = strtoupper(str_replace([' ', '-', '(', ')'], '', $value));
    }

    public function getGenderAttribute($value)
    {
        if (!Request::is('admin/*') && !empty($value)) {
           return __('common.genders.' . $value);
        }
        return $value;
    }

    public function getRawGenderAttribute()
    {
        switch ($this->attributes['gender']) {
            case 'm':
                return 'M';
            case 'f':
                return 'F';
            case 'x':
                return 'ND';
            default:
                return 'ND';
        }
    }
    public function getLanguageAttribute($value)
    {
        // if (!Request::is('admin/*') && !empty($value)) {
        //    return __('common.languages.' . $value);
        // }
        return $value;
    }
    public function getInscriptionChoiceAttribute($value)
    {
        if (!Request::is('admin/*') && !empty($value)) {
            return __('common.prescriptions.types.' . $value);
        }
        return $value;
    }
}
