<?php

namespace App\Models;

use Backpack\CRUD\app\Models\Traits\CrudTrait;
use Illuminate\Database\Eloquent\Model;

use App\Models\ProductDetail;

class Evaluation extends Model
{
    use CrudTrait;

    /*
    |--------------------------------------------------------------------------
    | GLOBAL VARIABLES
    |--------------------------------------------------------------------------
    */
    protected $table = 'evaluations';
    protected $guarded = ['id'];
    protected $hidden = ['fk_user_id', 'fk_product_detail_id', 'created_at', 'updated_at'];

    /*
    |--------------------------------------------------------------------------
    | RELATIONS
    |--------------------------------------------------------------------------
    */
    public function user()
    {
        return $this->belongsTo(User::class, 'fk_user_id');
    }
    public function productdetail()
    {
        return $this->belongsTo(ProductDetail::class, 'fk_product_detail_id');
    }
}
