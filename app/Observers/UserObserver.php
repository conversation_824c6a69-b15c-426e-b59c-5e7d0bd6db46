<?php

namespace App\Observers;

use App\Models\User;
use Illuminate\Support\Str;
use App\Mail\EmailNotification;
use App\Services\BluelinkService;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;

class UserObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function created(User $user)
    {
        // Upload identity proof image
        if ($user->proof_identity) {
            $path = saveFile($user->proof_identity, Str::slug($user->fullname), 'identities');
            if ($path) $user->proof_identity = $path;
        }
    }

    /**
     * Handle the User "updated" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function updated(User $user)
    {
        $userDetail = $user->userdetail;

        // if api_token updated, it's just because the user login
        if (!$user->isDirty('api_token') && !$user->isDirty('remember_token') && !$user->isDirty('password') && !$user->archived) {
            if ($user->isDirty('status') && $user->status == 'active') {
                App::setLocale($user->userdetail->language);
                Mail::to($user)->send(new EmailNotification($user->email, __('mail.activation.subject'), 'customer.activation', ['user' => $user->toArray()]));
            }
        }
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param  \App\Models\User  $user
     * @return void
     */
    public function deleted(User $user)
    {
        Storage::disk('public')->delete($user->proof_identity);
    }
}
