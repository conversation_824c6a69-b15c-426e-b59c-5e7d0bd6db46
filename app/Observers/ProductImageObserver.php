<?php

namespace App\Observers;

use App\Models\ProductImage;
use Illuminate\Support\Facades\Storage;

class ProductImageObserver
{
    public function creating(ProductImage $image)
    {
        $sku = $image->product()->first()->sku;
        $image_path = saveFile($image->path, $sku, 'products');
        if ($image_path) $image->path = $image_path;
    }

    public function updating(ProductImage $image)
    {
        // Set image path to relative, bypassing absolute path from existing image field
        $temp_path = $image->path;
        $pos = strpos($temp_path, 'products/');
        if ($pos !== false) $image->path = substr($temp_path, $pos);

        $new_path = $image->path;
        $current_path = $image->getOriginal('path');

        // Create new image, and delete old one if successful
        if ($new_path and ($new_path != $current_path)) {
            $sku = $image->product()->first()->sku;
            $new_image = saveFile($new_path, $sku, 'products') ;
            if ($new_image and $current_path) {
                $this->deleteImageFromDisk($current_path);
            }
            $image->path = $new_image;
        }
    }

    public function deleted(ProductImage $image)
    {
        $this->deleteImageFromDisk($image->path);
    }

    //
    private function deleteImageFromDisk($path)
    {
        Storage::disk('public')->delete($path);
    }
}
