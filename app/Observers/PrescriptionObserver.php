<?php

namespace App\Observers;

use App\Models\User;

use App\Models\Prescription;
use App\Mail\EmailNotification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;

class PrescriptionObserver
{
    /**
     * Handle the User "created" event.
     *
     * @param  \App\Models\Prescription $prescription
     * @return void
     */
    public function created(Prescription $prescription)
    {
        // Active user based on prescription
        if ($prescription->isDirty('status') && $prescription->status == 'valid') {
            $prescription->user->status = 'active';
            $prescription->user->save();

            App::setLocale($prescription->user->userdetail->language);
            Mail::send(new EmailNotification($prescription->user->email, __('mail.confirm_prescription_demand.subject'), 'customer.medical.confirmed_medical_prescription'));
        }

        // Set user quota based on prescription
        if ($prescription->isDirty('quota_value') || $prescription->isDirty('status')) {
            if ($prescription->status == 'valid') {
                $prescription->user->userdetail->quota_user_remaining = $prescription->daily_dosage;
                $prescription->user->userdetail->save();
            }
        }
    }

    /**
     * Handle the User "updated" event.
     *
     * @param  \App\Models\Prescription $prescription
     * @return void
     */
    public function updated(Prescription $prescription)
    {
        // Active user based on prescription
        if ($prescription->isDirty('status') && $prescription->status == 'valid') {
            $prescription->user->status = 'active';
            $prescription->user->save();

            App::setLocale($prescription->user->userdetail->language);
            Mail::send(new EmailNotification($prescription->user->email, __('mail.confirm_prescription_demand.subject'), 'customer.medical.confirmed_medical_prescription'));
        }

        // Set user quota based on prescription
        if ($prescription->isDirty('quota_value') || $prescription->isDirty('status')) {
            if ($prescription->status == 'valid') {
                $prescription->user->userdetail->quota_user_remaining = $prescription->daily_dosage;
                $prescription->user->userdetail->save();
            }
        }
    }

    /**
     * Handle the User "deleted" event.
     *
     * @param  \App\Models\Prescription $prescription
     * @return void
     */
    public function deleted(Prescription $prescription)
    {
        //
    }
}
