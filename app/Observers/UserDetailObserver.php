<?php

namespace App\Observers;

use App\Models\UserDetail;
use App\Services\BluelinkService;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class UserDetailObserver
{
    public function creating(UserDetail $userDetail)
    {
        $user = $userDetail->user;
        if ($user->hasRole('client') && $user->status != 'disabled' && empty($userDetail->teedy_client_id))
            $userDetail->teedy_client_id = $this->generateTeedyId($user);
    }

    public function updating(UserDetail $userDetail)
    {
        $user = $userDetail->user;
        if ($user->hasRole('client') && empty($userDetail->teedy_client_id)) {
            $code = '';
            foreach (array_merge(explode('-', $user->firstname), explode('-', $user->lastname)) as $value) {
                $value = str_replace(' ', '', $value);
                $code .= (!empty($value[0])) ? strtoupper($value[0]) : '';
            }
            $userDetail->teedy_client_id = $code . $user->id;
        }
    }

    private function generateTeedyId ($user)
    {
        try {
            $code = explode('-', Str::slug($user->fullname));
            $code = array_filter($code, fn($w) => !empty($w)); // Remove empty strings
            $code = strtoupper(join('', array_map(fn($w) => mb_substr($w, 0, 1), $code)));
            if (empty($code)) {
                throw new \Exception('Failed to generate TeedyID from username');
            }
            return $code . $user->id;
        } catch (\Exception $e) {
            // Fallback to numeric TeedyID
            return 'ID' . $user->id . rand(1000, 9999);
        }
    }
}
