<?php

namespace App\Observers;
use App\Models\ProductDetail;
use Illuminate\Support\Facades\Date;

class ProductDetailObserver
{
    public function creating(ProductDetail $pd)
    {
        // dd($pd);
    }

    public function updating(ProductDetail $productDetail)
    {
        if ($productDetail->isDirty('stock')) {
            $oldStock = $productDetail->getOriginal('stock');
            $newStock = $productDetail->stock;
            if ($oldStock > 0 && $newStock == 0) {
                $productDetail->stock_zero_date = now()->toDateString();
            }
     }
    }
}
