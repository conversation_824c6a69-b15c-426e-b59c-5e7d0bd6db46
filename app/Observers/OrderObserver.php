<?php

namespace App\Observers;

use App\Models\Order;
use App\Mail\EmailNotification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;

class orderObserver
{
    /**
     * Handle the Order "created" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function created(Order $order)
    {
        //
    }

    /**
     * Handle the Order "updated" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function updated(Order $order)
    {
        if ($order->isDirty('status')) {
            App::setLocale($order->user->userdetail->language);
            switch ($order->status) {
                case 'in_progress': 
                    
                    break;
                case 'shipped':
                    if (env('SEND_MAIL')) {
                        Mail::send(new EmailNotification($order->user->email, __('mail.status.shipped.subject'), 'customer.order_status', ['firstname' => $order->user->firstname, 'lastname' => $order->user->lastname, 'status' => 'shipped', 'user' => $order->user]));
                    }
                    break;
                case 'delivered':
                    if (env('SEND_MAIL')) {
                        Mail::send(new EmailNotification($order->user->email, __('mail.status.delivered.subject'), 'customer.order_status', ['firstname' => $order->user->firstname, 'lastname' => $order->user->lastname, 'status' => 'delivered', 'user' => $order->user]));
                    }
                    break;
                case 'cancelled':
                    if (env('SEND_MAIL')) {
                        Mail::send(new EmailNotification($order->user->email, __('mail.status.cancelled.subject'), 'customer.order_status', ['firstname' => $order->user->firstname, 'lastname' => $order->user->lastname, 'status' => 'cancelled', 'user' => $order->user]));
                    }
                    break;
            }            
        }  
    }

    /**
     * Handle the Order "deleted" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function deleted(Order $order)
    {
        //
    }

    /**
     * Handle the Order "restored" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function restored(Order $order)
    {
        //
    }

    /**
     * Handle the Order "force deleted" event.
     *
     * @param  \App\Models\Order  $order
     * @return void
     */
    public function forceDeleted(Order $order)
    {
        //
    }
}
