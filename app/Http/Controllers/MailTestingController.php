<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\ProductDetail;
use Illuminate\Support\Facades\Mail;
use App\Mail\EmailNotification;

class MailTestingController extends Controller
{
    public function index() {
        
        $user = User::find(2);

        $eval = (object) [
            'user' => (object) [
                'fullname' => '<PERSON> Doe'
            ],
            'productdetail' => (object) [
                'title' => 'Product title'
            ],
            'value' => 5
        ];
            

        //Mail inscription (user)
        // Mail::send(new EmailNotification($user->email, __('mail.register_user.subject'), 'customer.register', ['user' => $user]));

        //Mail inscription (admin)
        // Mail::send(new EmailNotification( 'admin', __('mail.register_admin.subject'), 'admin.register', ['user' => $user]));

        //Mail evaluation
        // Mail::send(new EmailNotification($user->email, __('mail.evaluate.subject'), 'customer.evaluate', ['evaluation' => $eval]));

        // Reset password

        // Support (user)

        // Support (admin)

        // Account active
        // Mail::send(new EmailNotification($user->email, __('mail.activation.subject'), 'customer.activation', ['user' => $user->toArray()]));

        // Account delete
        // Mail::send(new EmailNotification($user->email, __('mail.delete_account.subject'), 'customer.delete_account', $user));


        return 'test email sent! :)';
    }
}