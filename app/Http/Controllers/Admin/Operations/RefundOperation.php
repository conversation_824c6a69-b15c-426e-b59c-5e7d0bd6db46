<?php

namespace App\Http\Controllers\Admin\Operations;

use App\Mail\EmailNotification;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

trait RefundOperation
{
    /**
     * Define which routes are needed for this operation.
     *
     * @param string $segment    Name of the current entity (singular). Used as first URL segment.
     * @param string $routeName  Prefix of the route name.
     * @param string $controller Name of the current CrudController.
     */
    protected function setupRefundRoutes($segment, $routeName, $controller)
    {
        Route::get($segment.'/{id}/refund', [
            'as'        => $routeName.'.refund',
            'uses'      => $controller.'@refund',
            'operation' => 'refund',
        ]);
    }

    /**
     * Add the default settings, buttons, etc that this operation needs.
     */
    protected function setupRefundDefaults()
    {
        CRUD::allowAccess('refund');

        CRUD::operation('refund', function () {
            CRUD::loadDefaultOperationSettingsFromConfig();
        });

        CRUD::operation('list', function () {
            // CRUD::addButton('top', 'refund', 'view', 'crud::buttons.refund');
            CRUD::addButton('line', 'refund', 'view', 'vendor.backpack.buttons.refund');
        });
    }

    /**
     * Show the view for performing the operation.
     *
     * @return Response
     */
    public function refund($id)
    {
        CRUD::hasAccessOrFail('refund');

        $entry = $this->crud->getEntry($id);

        $entry->Refund();

        Alert::success(__('Le remboursement à été complété'))->flash();

        App::setLocale($entry->user->userdetail->language);
        // send mail to user
        Mail::send(new EmailNotification($entry->user->email, __('mail.refund_order.subject'), 'customer.refund-order', ['amount' => $entry->total, 'moneris_id' => substr($entry->moneris_id, -6)]));

        return Redirect::to($this->crud->route);
    
        // return redirect()->back();
    }
}