<?php

namespace App\Http\Controllers\Admin\Operations;

use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use App\Mail\EmailNotification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Mail;

trait CancelOperation
{
    /**
     * Define which routes are needed for this operation.
     *
     * @param string $segment    Name of the current entity (singular). Used as first URL segment.
     * @param string $routeName  Prefix of the route name.
     * @param string $controller Name of the current CrudController.
     */
    protected function setupCancelRoutes($segment, $routeName, $controller)
    {
        Route::get($segment.'/{id}/cancel', [
            'as'        => $routeName.'.cancel',
            'uses'      => $controller.'@cancel',
            'operation' => 'cancel',
        ]);
    }

    /**
     * Add the default settings, buttons, etc that this operation needs.
     */
    protected function setupCancelDefaults()
    {
        CRUD::allowAccess('cancel');

        CRUD::operation('cancel', function () {
            CRUD::loadDefaultOperationSettingsFromConfig();
        });

        // CRUD::operation('list', function () {
        //     // CRUD::addButton('top', 'cancel', 'view', 'crud::buttons.cancel');
        //     CRUD::addButton('line', 'cancel', 'view', 'vendor.backpack.buttons.cancel');
        // });
    }

    /**
     * Show the view for performing the operation.
     *
     * @return Response
     */
    public function cancel($id)
    {
        CRUD::hasAccessOrFail('cancel');

        // prepare the fields you need to show
        $entry = $this->crud->getEntry($id);
        
        if(env('MONERIS_SYNC')){
            $entry->PurchaseCorrection();
        }

        
        Alert::success(__('L\'annulation a été complété'))->flash();


        App::setLocale($entry->user->userdetail->language);
        // send mail to user
        Mail::send(new EmailNotification($entry->user->email, __('mail.refund_order.subject'), 'customer.refund-order', ['amount' => $entry->total, 'moneris_id' => substr($entry->moneris_id, -6)]));

        return Redirect::to($this->crud->route);
    
    }
}