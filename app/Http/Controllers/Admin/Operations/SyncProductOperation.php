<?php

namespace App\Http\Controllers\Admin\Operations;

use App\Models\ProductClone;
use Illuminate\Http\Request;
use App\Models\ProductRegular;
use App\Models\ProductAccessory;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

trait SyncProductOperation
{
    /**
     * Define which routes are needed for this operation.
     *
     * @param string $segment    Name of the current entity (singular). Used as first URL segment.
     * @param string $routeName  Prefix of the route name.
     * @param string $controller Name of the current CrudController.
     */
    protected function setupSyncProductRoutes($segment, $routeName, $controller)
    {
        Route::get($segment.'/{id}/sync-product', [
            'as'        => $routeName.'.syncProduct',
            'uses'      => $controller.'@syncProduct',
            'operation' => 'syncProduct',
        ]);
    }

    /**
     * Add the default settings, buttons, etc that this operation needs.
     */
    protected function setupSyncProductDefaults()
    {
        CRUD::allowAccess('syncProduct');

        CRUD::operation('syncProduct', function () {
            CRUD::loadDefaultOperationSettingsFromConfig();
        });

        CRUD::operation('list', function () {
            // CRUD::addButton('top', 'sync_product', 'view', 'crud::buttons.sync_product');
            CRUD::addButton('line', 'sync_product', 'view', 'vendor.backpack.buttons.sync_product');
        });
    }

    /**
     * Show the view for performing the operation.
     *
     * @return Response
     */
    public function syncProduct($id)
    {
        CRUD::hasAccessOrFail('syncProduct');

        if(!env('SYNC_BLUELINK')){
            return;
        }
        // Fail if missing type param
        // dd($this->crud->entity_name);
        $product = $this->crud->entry;

        try{
            $result = $product->BLWeb_SyncInventory();
            if($result){
                Alert::success('Synchronisation réussie')->flash();
            } else {
                Alert::error('Erreur dans la synchronisation : SKU non reconnu ')->flash();
            }
        } catch (\Exception $e) {
            Alert::error('Erreur lors de la synchronisation')->flash();
        }
        // Sync product


        // Alert::add('success', 'Sync successful');
        // Alert::success('Sync successful')->flash();
        // return redirect()->route('products.index');
        return Redirect::to($this->crud->route);
    }
}