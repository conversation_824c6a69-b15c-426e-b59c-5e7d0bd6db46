<?php

namespace App\Http\Controllers\Admin\Operations;

use App\Models\ApiLog;
use App\Mail\EmailNotification;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\PrescriptionRequest; // Add this import
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

trait SyncUserOperation
{
    /**
     * Define which routes are needed for this operation.
     *
     * @param string $segment    Name of the current entity (singular). Used as first URL segment.
     * @param string $routeName  Prefix of the route name.
     * @param string $controller Name of the current CrudController.
     */
    protected function setupSyncUserRoutes($segment, $routeName, $controller)
    {
        Route::get('users/{user}/sync-user', [
            'uses'      => $controller.'@syncUser',
            'operation' => 'syncUser',
        ]);
    }

    /**
     * Add the default settings, buttons, etc that this operation needs.
     */
    protected function setupSyncUserDefaults()
    {
        CRUD::allowAccess('syncUser');

        CRUD::operation('syncUser', function () {
            CRUD::loadDefaultOperationSettingsFromConfig();
        });

        CRUD::operation('list', function () {
            CRUD::addButton('line', 'sync_user', 'view', 'vendor.backpack.buttons.sync_user');
        });
    }

    /**
     * Comprehensive user synchronization with BlueLink
     *
     * @param int $id User ID
     * @return Response
     */
    public function syncUser($id)
    {
        CRUD::hasAccessOrFail('syncUser');

        $user = $this->crud->getEntry($id);
        $syncResults = [];
        $hasErrors = false;

        try {
            Log::info('Starting user sync', ['user_id' => $user->id, 'email' => $user->email]);

            $customerResult = $this->findOrCreateCustomer($user);
            $syncResults[] = $customerResult;
            
            if (!$customerResult['success']) {
                $hasErrors = true;
                Alert::error($customerResult['message'])->flash();
                return Redirect::to('/admin/client/regular');
            }

            $address = $user->addresses()->first();
            if ($address) {
                $addressResult = $this->syncUserAddress($address);
                $syncResults[] = $addressResult;
                
                if (!$addressResult['success']) {
                    $hasErrors = true;
                    Log::warning('Address sync failed but continuing', $addressResult);
                }
            } else {
                $syncResults[] = ['step' => 'address', 'success' => true, 'message' => 'No address to sync', 'skipped' => true];
            }

            $prescriptions = $user->prescriptions()->where('status', 'Approved')->get();
            if ($prescriptions->count() > 0) {
                foreach ($prescriptions as $prescription) {
                    
                    $prescriptionResult = $this->syncUserPrescription($prescription);
                    $syncResults[] = $prescriptionResult;
                    
                    if (!$prescriptionResult['success']) {
                        $hasErrors = true;
                        Log::warning('Prescription sync failed but continuing', $prescriptionResult);
                    }
                }
            } else {
                $syncResults[] = ['step' => 'prescriptions', 'success' => true, 'message' => 'No approved prescriptions to sync', 'skipped' => true];
            }

            $user->userdetail->update(['bluelink_synced' => true]);

            Log::info('User sync completed', [
                'user_id' => $user->id,
                'results' => $syncResults,
                'has_errors' => $hasErrors
            ]);

            if ($hasErrors) {
                Alert::warning(__('Le client a été synchronisé avec quelques erreurs. Vérifiez les logs pour plus de détails.'))->flash();
            } else {
                Alert::success(__('Le client a été synchronisé avec succès'))->flash();
            }

        } catch (\Exception $e) {
            Log::error('Critical error during user sync', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            ApiLog::create([
                'model' => get_class($user),
                'method' => 'syncUser',
                'error_message' => $e->getMessage(),
                'additional_data' => json_encode(['user_id' => $user->id])
            ]);

            Alert::error(__('Une erreur critique est survenue lors de la synchronisation'))->flash();
        }

        return Redirect::to('/admin/client/regular');
    }

    /**
     * Find existing customer or create new one in BlueLink
     *
     * @param \App\Models\User $user
     * @return array
     */
    private function findOrCreateCustomer($user)
    {
        try {
            if ($user->userdetail->bluelink_id) {
                Log::info('Checking existing BlueLink ID', ['bluelink_id' => $user->userdetail->bluelink_id]);
                
                // Temporarily store the original teedy_client_id
                $originalTeedyId = $user->userdetail->teedy_client_id;
                
                // Set teedy_client_id to bluelink_id for the API call
                $user->userdetail->teedy_client_id = $user->userdetail->bluelink_id;
                
                $customerResult = $user->BLWeb_GetCustomer();
                
                // Restore original teedy_client_id
                $user->userdetail->teedy_client_id = $originalTeedyId;
                
                if (!empty($customerResult->value)) {
                    Log::info('Customer found with BlueLink ID', ['bluelink_id' => $user->userdetail->bluelink_id]);
                    return [
                        'step' => 'find_customer_bluelink_id',
                        'success' => true,
                        'message' => 'Customer found with existing BlueLink ID',
                        'data' => $customerResult
                    ];
                }
            }

            if ($user->userdetail->teedy_client_id) {
                Log::info('Checking with Teedy client ID', ['teedy_client_id' => $user->userdetail->teedy_client_id]);
                
                $customerResult = $user->BLWeb_GetCustomer();
                
                if (!empty($customerResult->value)) {
                    Log::info('Customer found with Teedy ID', ['teedy_client_id' => $user->userdetail->teedy_client_id]);
                    
                    $user->userdetail->update(['bluelink_id' => $user->userdetail->teedy_client_id]);
                    
                    return [
                        'step' => 'find_customer_teedy_id',
                        'success' => true,
                        'message' => 'Customer found with Teedy client ID',
                        'data' => $customerResult
                    ];
                }
            }

            Log::info('Customer not found, creating new customer', ['user_id' => $user->id]);
            
            $createResult = $user->BLWeb_CreateCustomer();
            
            // If createResult is empty, it means the customer was created successfully with a code 200 ok returned, orelse there would be a error logged
            if (empty($createResult)) {
                Log::info('Customer created successfully', [
                    'user_id' => $user->id,
                    'bluelink_id' => $user->userdetail->bluelink_id
                ]);
                
                return [
                    'step' => 'create_customer',
                    'success' => true,
                    'message' => 'New customer created in BlueLink',
                    'data' => $createResult
                ];
            } else {
                throw new \Exception('Failed to create customer in BlueLink');
            }

        } catch (\Exception $e) {
            Log::error('Error in findOrCreateCustomer', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);

            return [
                'step' => 'find_or_create_customer',
                'success' => false,
                'message' => 'Failed to find or create customer: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync user address to BlueLink
     *
     * @param \App\Models\Address $address
     * @return array
     */
    private function syncUserAddress($address)
    {
        try {
            Log::info('Syncing address', ['address_id' => $address->id, 'user_id' => $address->user->id]);

            if (empty($address->shipcode)) {
                $address->update(['shipcode' => 'main']);
            }
            $result = $address->BLWeb_CreateAddress();

            if ($result !== false) {
                Log::info('Address synced successfully', ['address_id' => $address->id]);
                
                return [
                    'step' => 'sync_address',
                    'success' => true,
                    'message' => 'Address synced successfully',
                    'data' => $result
                ];
            } else {
                throw new \Exception('Address sync returned false');
            }

        } catch (\Exception $e) {
            Log::error('Error syncing address', [
                'address_id' => $address->id ?? null,
                'user_id' => $address->user->id ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'step' => 'sync_address',
                'success' => false,
                'message' => 'Failed to sync address: ' . $e->getMessage(),
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Sync user prescription to BlueLink
     *
     * @param \App\Models\Prescription $prescription
     * @return array
     */
    private function syncUserPrescription($prescription)
    {
        try {
            Log::info('Syncing prescription', [
                'prescription_id' => $prescription->id,
                'user_id' => $prescription->user->id,
                'prescription_number' => $prescription->prescription_number
            ]);

            // Use existing PrescriptionRequest validation
            $validationResult = $this->validatePrescriptionWithExistingRules($prescription);
            if (!$validationResult['valid']) {
                Log::warning('Prescription validation failed, skipping sync', [
                    'prescription_id' => $prescription->id,
                    'validation_errors' => $validationResult['errors']
                ]);

                return [
                    'step' => 'sync_prescription',
                    'success' => true,
                    'message' => 'Prescription skipped - validation failed: ' . implode(', ', array_keys($validationResult['errors'])),
                    'skipped' => true,
                    'prescription_id' => $prescription->id,
                    'validation_errors' => $validationResult['errors']
                ];
            }

            try{
                $existingPrescription = $prescription->BLWeb_GetPrescription($prescription->prescription_number);
            }catch (\Exception $e) {
                Log::error('Prescription not found', [
                    'prescription_id' => $prescription->id,
                    'user_id' => $prescription->user->id,
                    'error' => $e->getMessage()
                ]);
                $existingPrescription = null; // Treat as not found
            }

            
            
            if (!empty($existingPrescription->value)) {
                $result = $prescription->BLWeb_UpdatePrescription();
                $action = 'updated';
            } else {
                $result = $prescription->BLWeb_CreatePrescription();
                $action = 'created';
            }

            if ($result !== false) {
                Log::info("Prescription {$action} successfully", [
                    'prescription_id' => $prescription->id,
                    'action' => $action
                ]);
                
                return [
                    'step' => 'sync_prescription',
                    'success' => true,
                    'message' => "Prescription {$action} successfully",
                    'data' => $result,
                    'prescription_id' => $prescription->id
                ];
            } else {
                throw new \Exception("Prescription {$action} returned false");
            }

        } catch (\Exception $e) {
            Log::error('Error syncing prescription', [
                'prescription_id' => $prescription->id ?? null,
                'user_id' => $prescription->user->id ?? null,
                'error' => $e->getMessage()
            ]);

            return [
                'step' => 'sync_prescription',
                'success' => false,
                'message' => 'Failed to sync prescription: ' . $e->getMessage(),
                'error' => $e->getMessage(),
                'prescription_id' => $prescription->id ?? null
            ];
        }
    }

    /**
     * Validate prescription using existing PrescriptionRequest rules
     *
     * @param \App\Models\Prescription $prescription
     * @return array
     */
    private function validatePrescriptionWithExistingRules($prescription)
    {
        $request = new PrescriptionRequest();
        $rules = $request->rules();
        
        $dataToValidate = [
            'fk_user_id' => $prescription->fk_user_id,
            'status' => $prescription->status,
            'start_date' => $prescription->start_date,
            'end_date' => $prescription->end_date,
            // 'clinic_name' => $prescription->clinic_name,
            'doc_name' => $prescription->doc_name,
            'daily_dosage' => $prescription->daily_dosage,
            'prescription_number' => $prescription->prescription_number,
        ];

        // Remove prescription_photo validation since we're not validating files during sync
        unset($rules['prescription_photo']);
        $rules['prescription_number'] = 'required|string';
        $validator = Validator::make($dataToValidate, $rules);

        if ($validator->fails()) {
            return [
                'valid' => false,
                'errors' => $validator->errors()->toArray()
            ];
        }

        return [
            'valid' => true,
            'errors' => []
        ];
    }
}