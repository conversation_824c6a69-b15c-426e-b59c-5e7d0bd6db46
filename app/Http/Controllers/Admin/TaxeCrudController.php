<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\TaxeRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class TaxeCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class TaxeCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    // use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Taxe::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/taxes');
        CRUD::setEntityNameStrings(__('teedy/orders.taxes.singular'), __('teedy/orders.taxes.plural'));

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     *
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::addColumns([
            [
                'name' => 'code',
                'label' => ucfirst(__('teedy/orders.taxes.labels.code')),
                'type' => 'text',
            ],
            [
                'name' => 'value',
                'label' => ucfirst(__('teedy/orders.taxes.labels.value')),
                'type' => 'text',
            ],
            // [
            //     'name' => 'add_existance_tax',
            //     'label' => ucfirst(__('teedy/orders.taxes.labels.add_existance_tax')),
            //     'type' => 'select_from_array',
            //     'options' => __('admin.common.boolean_values'),
            // ],
            [
                'name'    => 'province',
                'label'   => ucfirst(__('teedy/orders.taxes.labels.province')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.taxes.province_values'),
            ],
            [
                'name'    => 'country',
                'label'   => ucfirst(__('teedy/orders.taxes.labels.country')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.taxes.country_values'),
            ],
        ]);
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(TaxeRequest::class);

        CRUD::addFields([
            [
                'name' => 'code',
                'label' => ucfirst(__('teedy/orders.taxes.labels.code')),
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-controller col-md-4'
                ]
            ],
            [
                'name' => 'value',
                'label' => ucfirst(__('teedy/orders.taxes.labels.value')),
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-controller col-md-4'
                ]
            ],
            [
                'name'    => 'province',
                'label'   => ucfirst(__('teedy/orders.taxes.labels.province')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.taxes.province_values'),
                'allows_null' => false,
                'wrapper' => [
                    'class' => 'form-controller col-md-4'
                ]
            ],
            [
                'name'    => 'country',
                'label'   => ucfirst(__('teedy/orders.taxes.labels.country')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.taxes.country_values'),
                'allows_null' => false,
                'wrapper' => [
                    'class' => 'form-controller col-md-4'
                ]
            ],
            // [
            //     'name' => 'add_existance_tax',
            //     'label' => ucfirst(__('teedy/orders.taxes.labels.add_existance_tax')),
            //     'type' => 'select_from_array',
            //     'options' => __('admin.common.boolean_values'),
            //     'wrapper' => [
            //         'class' => 'form-controller col-md-4'
            //     ]
            // ],
        ]);
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}
