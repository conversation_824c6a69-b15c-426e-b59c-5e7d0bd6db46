<?php

namespace App\Http\Controllers\Admin;

use App\Models\ProductAccessory;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Validator;

use App\Http\Requests\ProductDetailRequest;
use App\Http\Requests\ProductAccessoryRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class ProductAccessoryCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ProductAccessoryCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation { destroy as traitDestroy; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\CloneOperation { clone as traitClone; }
    use \App\Http\Controllers\Admin\Operations\SyncProductOperation;


    private $currentId;

    public function setup()
    {
        CRUD::setModel(\App\Models\ProductAccessory::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/accessories');
        CRUD::setEntityNameStrings('accessoire', 'accessoires');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }

        CRUD::setRequiredFields(ProductAccessoryRequest::class, 'create');
        CRUD::setRequiredFields(ProductDetailRequest::class, 'create');

        $current = $this->crud->getCurrentEntry();
        $this->currentId = $current ? $current->id : null;
    }

    protected function setupListOperation()
    {
        //CRUD::column('id');

        CRUD::column('sku')->label('SKU')
            ->value(fn ($p) => $p->productdetail->sku)
            ->orderable(true)
            ->orderLogic(function ($query, $column, $columnDirection) {
                return $query->leftJoin('product_details', 'product_accessories.id', '=', 'product_details.productable_id')
                            ->where('product_details.productable_type', 'App\\Models\\ProductAccessory')
                            ->orderBy('product_details.sku', $columnDirection)
                            ->select('product_accessories.*');
            });

        CRUD::column('productdetail')
            ->label('Nom')
            ->type('closure')
            ->function(fn ($entry) => $entry->productdetail->title_fr)

            // Extend search to productdetail model
            ->searchLogic(function ($query, $column, $searchTerm) {
                $query->whereHas('productdetail', function ($q) use ($searchTerm) {
                    $q->where('title_fr', 'like', '%'.$searchTerm.'%');
                    $q->orWhere('title_en', 'like', '%'.$searchTerm.'%');
                });
            });

        CRUD::column('stock')->label('Stock')
            ->value(fn ($p) => $p->productdetail->stock)
            ->orderable(true)
            ->orderLogic(function ($query, $column, $columnDirection) {
                return $query->leftJoin('product_details', 'product_accessories.id', '=', 'product_details.productable_id')
                            ->where('product_details.productable_type', 'App\\Models\\ProductAccessory')
                            ->orderBy('product_details.stock', $columnDirection)
                            ->select('product_accessories.*');
            });

        CRUD::column('price')->value(fn ($p) => $p->productdetail->price)
            ->suffix('$')
            ->label('Prix')
            ->orderable(true)
            ->orderLogic(function ($query, $column, $columnDirection) {
                return $query->leftJoin('product_details', 'product_accessories.id', '=', 'product_details.productable_id')
                            ->where('product_details.productable_type', 'App\\Models\\ProductAccessory')
                            ->orderBy('product_details.price', $columnDirection)
                            ->select('product_accessories.*');
            });

        CRUD::column('publish')->label('Statut de Publication')
            ->value(fn ($p) => $p->productdetail->publish ? 'Publié' : 'Dépublié')
            ->type('text')
            ->orderable(true)
            ->orderLogic(function ($query, $column, $columnDirection) {
                return $query->leftJoin('product_details', 'product_accessories.id', '=', 'product_details.productable_id')
                            ->where('product_details.productable_type', 'App\\Models\\ProductAccessory')
                            ->orderBy('product_details.publish', $columnDirection)
                            ->select('product_accessories.*');
            });

        CRUD::addColumn([
            'name' => 'bluelink_synced',
            'label' => 'Sync Status',
            'type' => 'custom_html',
            'value' => function ($entry) {
                return $entry->productdetail->bluelink_synced 
                    ? '<div style="text-align: center;"><i class="la la-check-circle" style="color: green;" title="Product synced with BlueLink"></i></div>' 
                    : '<div style="text-align: center;"><i class="la la-exclamation-triangle" style="color: red;" title="Product not synced with BlueLink"></i></div>';
            }
        ]);

        // $this->crud->addButtonFromModelFunction('line', 'open', 'getOpenButton', 'beginning');
    }

    protected function setupCreateOperation()
    {
        $this->addAccessoryFields();
    }

    protected function setupUpdateOperation()
    {
        $this->addAccessoryFields(true);
    }

    public function store()
    {
        // Merge validation rules and attributes from all requests
        $rules = array_merge(
            (new ProductDetailRequest)->rules(),
            (new ProductAccessoryRequest)->rules()
        );
        $attributes = array_merge(
            (new ProductDetailRequest)->attributes(),
            (new ProductAccessoryRequest)->attributes()
        );

        // Validate everything and save
        $fields = $this->crud->getRequest()->all();
        Validator::make($fields, $rules, [], $attributes)->validate();

        // Merge productdetails fields placed outside the relation field
        $this->mergeProductDetailInfo($fields);
        $response = $this->traitStore();

        // Add images to ProductDetail model
        $product = $this->data['entry']->productdetail;
        $images = @$fields['images'] ?: [];
        foreach ($images as $value) {
            $product->images()->create($value);
        }

        return $response;
    }

    public function update()
    {
        $request = $this->crud->getRequest();
        $fields = $request->all();

        // Merge validation rules and attributes from all requests
        $rules = array_merge(
            (new ProductDetailRequest)->rules(),
            (new ProductAccessoryRequest)->rules()
        );
        $attributes = array_merge(
            (new ProductDetailRequest)->attributes(),
            (new ProductAccessoryRequest)->attributes()
        );

        // Validate everything
        Validator::make($fields, $rules, [], $attributes)->validate();

        // Merge productdetails fields placed outside the relation field
        $this->mergeProductDetailInfo($fields);

        // Remove images to add them after main model updating
        $images = @$fields['images'] ?: null;
        $request->request->remove('images');

        // Update product
        $response = $this->traitUpdate();

        // Manage images
        if ($images) {
            $product = $this->data['entry']->productdetail;
            foreach ($images as $key => $image) {
                $existing_image = $product->images()->find($image['id']);
                if ($existing_image) $existing_image->update($image);
                else {
                    //
                    $created = $product->images()->create($image);
                    $images[$key]['id'] = $created->id; // To safeguard against delete image loop
                }
            }
        }

        // Delete unused images
        $ids = empty($images) ? [] : array_map(fn ($f) => $f['id'], $images);
        $unusedImages = ProductAccessory::find($request->get('id'))->productdetail()->first()->images()->whereNotIn('id', $ids)->get();
        foreach ($unusedImages as $i) { $i->delete(); }

        return $response;
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');
        $this->crud->getCurrentEntry()->productdetail()->delete();
        return $this->crud->delete($id);
    }

    public function clone($id)
    {
        CRUD::hasAccessOrFail('clone');
        CRUD::setOperation('clone');

        // Get the product with the given ID
        $product = ProductAccessory::with('productdetail', 'productdetail.images')->find($id);

        if (!$product) {
            abort(404, 'Product not found');
        }

        // Clone the product
        $clonedProduct = $product->replicate();
        $clonedProduct->push();

        // Clone the product details
        $clonedProductDetail = $product->productdetail->replicate();
        $clonedProductDetail->productable_id = $clonedProduct->id; // alter to new product id
        $clonedProductDetail->publish = 0; // unpublish the cloned product
        $clonedProductDetail->slug_fr = $product->productdetail->slug_fr . '-copie'; // alter slug
        $clonedProductDetail->push();

        // clone the images
        $images = $product->productdetail->images;
        foreach ($images as $image) {
            $clonedProductDetail->images()->create($image->toArray());
        }

        return redirect(backpack_url('accessories/' . $clonedProduct->id . '/edit'));
    }

    //
    protected function addAccessoryFields($update = false)
    {
        // ---------------------------------------------------------------
        // TABs - Francais/Anglais
        CRUD::addFields([
            [
                'name'          => 'title_fr',
                'label'         => __('teedy/products.fields.title_fr'),
                'type'          => 'text',
                'tab'           => __('teedy/products.tabs.fr'),
                'entity'        => 'productdetail.title_fr',
                'wrapper'       => ['class' => 'form-group col-md-8'],
            ],
            [
                'name'          => 'slug_fr',
                'label'         => __('teedy/products.fields.slug_fr'),
                'type'          => 'text',
                'tab'           => __('teedy/products.tabs.fr'),
                'entity'        => 'productdetail.slug_fr',
                'wrapper'       => ['class' => 'form-group col-md-4'],
            ],
            [
                'name'          => 'description_fr',
                'label'         => __('teedy/products.fields.description_fr'),
                'type'          => 'textarea',
                'tab'           => __('teedy/products.tabs.fr'),
                'entity'        => 'productdetail.description_fr',
                'wrapper'       => ['class' => 'form-group col-md-12'],
            ],
            [
                'name'          => 'meta_title_fr',
                'label'         => __('teedy/products.fields.meta_title_fr'),
                'tab'           => __('teedy/products.tabs.fr'),
                'entity'        => 'productdetail.meta_title_fr',
                'wrapper'       => ['class' => 'form-group col-md-4'],
                'hint'          => 'Si les champs méta ne sont pas remplis, les champs non-méta seront utilisés comme méta.'
            ],
            [
                'name'          => 'meta_description_fr',
                'label'         => __('teedy/products.fields.meta_description_fr'),
                'tab'           => __('teedy/products.tabs.fr'),
                'entity'        => 'productdetail.meta_description_fr',
                'wrapper'       => ['class' => 'form-group col-md-8'],
                'hint'          => 'Si les champs méta ne sont pas remplis, les champs non-méta seront utilisés comme méta.'
            ],

            // ---

            [
                'name'          => 'basic_separator_5',
                'type'          => 'custom_html',
                'tab'           => __('teedy/products.tabs.en'),
                'value'         => '<p class="alert alert-info">' . __('teedy/products.messages.en-section') . '</p>'
            ],
            [
                'name'          => 'title_en',
                'label'         => __('teedy/products.fields.title_en'),
                'type'          => 'text',
                'tab'           => __('teedy/products.tabs.en'),
                'entity'        => 'productdetail.title_en',
                'wrapper'       => ['class' => 'form-group col-md-8'],
            ],
            [
                'name'          => 'slug_en',
                'label'         => __('teedy/products.fields.slug_en'),
                'type'          => 'text',
                'tab'           => __('teedy/products.tabs.en'),
                'entity'        => 'productdetail.slug_en',
                'wrapper'       => ['class' => 'form-group col-md-4'],
            ],
            [
                'name'          => 'description_en',
                'label'         => __('teedy/products.fields.description_en'),
                'type'          => 'textarea',
                'tab'           => __('teedy/products.tabs.en'),
                'entity'        => 'productdetail.description_en',
                'wrapper'       => ['class' => 'form-group col-md-12'],
            ],
            [
                'name'          => 'meta_title_en',
                'label'         => __('teedy/products.fields.meta_title_en'),
                'tab'           => __('teedy/products.tabs.en'),
                'entity'        => 'productdetail.meta_title_en',
                'wrapper'       => ['class' => 'form-group col-md-4']
            ],
            [
                'name'          => 'meta_description_en',
                'label'         => __('teedy/products.fields.meta_description_en'),
                'tab'           => __('teedy/products.tabs.en'),
                'entity'        => 'productdetail.meta_description_en',
                'wrapper'       => ['class' => 'form-group col-md-8']
            ],
        ]);

        // ---------------------------------------------------------------
        // TAB - Basic
        // Product details fields (common in all product types)
        CRUD::addField([
            // This field is set as repeatable to offset the lack of 'init_rows' on relationship types
            'name'              => 'productdetail',
            'label'             => ' Détail du produit',
            'type'              => 'repeatable',
            'tab'               => __('teedy/products.tabs.basic'),
            'min_rows'          => 1,
            'max_rows'          => 1,
            'wrapper'           => ['class' => 'product-detail-subform'],   // Custom css to make it look like part of the same form
            'subfields'         => [
                [
                    'name'          => 'sku',
                    'label'         => __('teedy/products.fields.sku'),
                    'type'          => 'text',
                    'showAsterisk'  => true,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'price',
                    'label'         => __('teedy/products.fields.price'),
                    'type'          => 'number',
                    'attributes'    => ['step' => 'any'],
                    'suffix'        => '$',
                    'showAsterisk'  => true,
                    'default'       => 0,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'publish',
                    'label'         => __('teedy/products.fields.publish'),
                    'type'          => 'select_from_array',
                    'options'       => __('admin.common.boolean_values'),
                    'allows_null'   => false,
                    'default'       => true,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'featured',
                    'label'         => __('teedy/products.fields.featured'),
                    'type'          => 'select_from_array',
                    'options'       => __('admin.common.boolean_values'),
                    'allows_null'   => false,
                    'default'       => false,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'basic_separator_1',
                    'type'          => 'custom_html',
                    'value'         => '<hr>'
                ],
                [
                    'name'          => 'status',
                    'label'         => __('teedy/products.fields.status'),
                    'type'          => 'select_from_array',
                    'options'       => __('teedy/products.status'),
                    'allows_null'   => false,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'stock',
                    'label'         => __('teedy/products.fields.stock'),
                    'type'          => 'number',
                    'showAsterisk'  => true,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'max_per_person',
                    'label'         => __('teedy/products.fields.max_per_person'),
                    'type'          => 'number',
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                    'hint'          => __('teedy/products.messages.max_per_person'),
                ],
                [
                    'name'          => 'description_bl',
                    'label'         => 'Description Bluelink',
                    'type'          => 'textarea',
                    'wrapper'       => ['class' => 'form-group col-md-6'],
                ],
                [
                    'name'          => 'basic_separator_1',
                    'type'          => 'custom_html',
                    'value'         => '<hr>'
                ],
                [
                    'name'          => 'producer',
                    'label'         => __('teedy/products.fields.producer'),
                    'type'          => 'relationship',
                    'allows_null'   => false,
                    'wrapper'       => ['class' => 'form-group col-md-3'],
                ],
                [
                    'name'              => 'labels',
                    'label'             => __('teedy/products.fields.labels'),
                    'type'              => 'select2_from_array',
                    'options'           => __('teedy/products.labels'),
                    'allows_null'       => true,
                    'allows_multiple'   => true,
                    'wrapperAttributes' => ['class' => 'form-group col-md-3']
                ],
                [
                    'name'          => 'related_products',
                    'label'         => __('teedy/products.fields.related_products'),
                    'type'          => 'relationship',
                    'model'         => 'App\Models\ProductDetail',
                    'attribute'     => 'sku',
                    'allows_null'   => true,
                    'wrapper'       => ['class' => 'form-group col-md-4'],
                    'options' => (function ($query) { // filter out current product and all parent + only display same type
                        $query = $this->currentId
                            ? $query->where('id', '!=', $this->crud->entry->productDetail->id)
                            : $query;

                        return $query->where('productable_type', 'App\Models\ProductAccessory')
                            ->whereNotIn('id', function ($subQuery) {
                                $subQuery->select('product_detail_id')
                                    ->from('related_products');
                            })->get();
                    }),
                    'hint'          => __('teedy/products.messages.variation'),
                ],
                // [
                //     'name'          => 'create_variation',
                //     'type'          => 'custom_html',
                //     'value'         => (boolval($this->currentId) ? '<label>' . __('teedy/products.fields.create_variation') . '</label><a href="duplicate/" class="btn btn-success" style="display: block;">Créer une variation de ce produit</a>' : ''),
                //     'wrapper'       => ['class' => 'form-group col-md-4'],
                // ],
                [
                    'name'          => 'basic_separator_1',
                    'type'          => 'custom_html',
                    'value'         => '<hr>'
                ],
                [
                    'name'          => 'is_applicable_veteran',
                    'label'         => __('teedy/products.fields.is_applicable_veteran'),
                    'type'          => 'select_from_array',
                    'options'       => __('admin.common.boolean_values'),
                    'allows_null'   => false,
                    'default'       => true,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'nb_max_veteran',
                    'label'         => __('teedy/products.fields.nb_max_veteran'),
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                    'hint'          => __('teedy/products.messages.max_per_person'),
                ],
            ],
        ]);


        // Physical details
        CRUD::addFields([
            [
                'name'      => 'size',
                'label'     => __('teedy/products.fields.size'),
                'tab'       => __('teedy/products.tabs.basic'),
                'wrapper'   => ['class' => 'form-group col-md-2'],
            ],
            [
                'name'      => 'unity',
                'label'     => __('teedy/products.fields.unity'),
                'type'      => 'select_from_array',
                'tab'       => __('teedy/products.tabs.basic'),
                'options'   => __('teedy/products.unity'),
                'wrapper'   => ['class' => 'form-group col-md-2'],
            ],
            [
                'name'          => 'bl_displayuom',
                'label'         => 'Display UOM',
                'type'          => 'text',
                'tab'           => __('teedy/products.tabs.basic'),
                'wrapper'       => ['class' => 'form-group col-md-2'],
                'showAsterisk'  => true,
                'hint'          => 'Champ requis pour Bluelink (Ex: EA, ML, PK6...)',
            ],
        ]);

        // ---------------------------------------------------------------
        // TAB - Images
        CRUD::addField([
            'name'          => 'images',
            'label'         => __('teedy/products.fields.images'),
            'type'          => $update ? 'relationship' : 'repeatable',
            'tab'           => __('teedy/products.tabs.images'),
            'entity'        => $update ? 'productdetail.images' : null,
            'wrapper'       => ['class' => 'form-group col-md-12'],
            'subfields'     => [
                [
                    'name'          => 'id',
                    'type'          => 'hidden',
                ],
                [
                    'name'          => 'path',
                    'label'         => __('teedy/products.fields.path'),
                    'type'          => 'image',
                    'disk'          => 'public',
                    'wrapper'       => ['class' => 'form-group col-md-4'],
                ],
                [
                    'name'          => 'alt_text',
                    'label'         => __('teedy/products.fields.alt_text'),
                    'type'          => 'text',
                    'wrapper'       => ['class' => 'form-group col-md-4'],
                ],
                [
                    'name'          => 'primary',
                    'label'         => __('teedy/products.fields.primary'),
                    'type'          => 'select_from_array',
                    'options'       => __('admin.common.boolean_values'),
                    'allows_null'   => false,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
                [
                    'name'          => 'og',
                    'label'         => __('teedy/products.fields.og'),
                    'type'          => 'select_from_array',
                    'options'       => __('admin.common.boolean_values'),
                    'allows_null'   => false,
                    'wrapper'       => ['class' => 'form-group col-md-2'],
                ],
            ]
        ]);
    }

    //
    function mergeProductDetailInfo ($fields) {
        // These fields from the productdetail field are spread outside the main relation field...
        $product_detail_fields = [
            'title_fr', 'slug_fr', 'description_fr', 'meta_title_fr', 'meta_description_fr',
            'title_en', 'slug_en', 'description_en', 'meta_title_en', 'meta_description_en',
        ];

        // ... so here we move them manually to productdetail
        $product_detail = $fields['productdetail'];
        foreach ($product_detail_fields as $field) {
            $product_detail[0][$field] = $fields[$field];
            $this->crud->getRequest()->request->remove($field);
        }

        // ... and replace productdetail inside the request
        $this->crud->getRequest()->request->add(['productdetail' => $product_detail]);
    }
}
