<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\ProducerRequest;
// use Backpack\CRUD\app\Http\Controllers\CrudController;
use App\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class ProducerCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ProducerCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\Producer::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/producers');
        CRUD::setEntityNameStrings('producteur', 'producteurs');

        if (!backpack_user()->hasRole(['admin', 'superadmin', 'editor'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        CRUD::column('id');
        CRUD::column('title_fr')->label(__('teedy/producers.fields.title_fr'));
        CRUD::column('slug_fr')->label(__('teedy/producers.fields.slug_fr'));
        CRUD::addColumn([
            'name' => 'publish',
            'label' => __('teedy/producers.fields.publish'),
            'type' => 'boolean',
        ]);
        CRUD::addColumn([
            'name' => 'publish_on_website',
            'label' => __('teedy/producers.fields.publish_on_website'),
            'type' => 'boolean',
        ]);
    }

    protected function setupCreateOperation()
    {
        $this->addProducerFields();
        CRUD::setValidation(ProducerRequest::class);
    }

    protected function setupUpdateOperation()
    {
        $this->addProducerFields();
        CRUD::setValidation(ProducerRequest::class);
    }


    protected function addProducerFields()
    {
        // FR
        CRUD::addFields([
            [
                'name'      => 'title_fr',
                'label'     => __('teedy/producers.fields.title_fr'),
                'tab'       => __('teedy/producers.tabs.fr'),
                'type'      => 'text',
                'wrapper'   => ['class' => 'form-group col-md-8']
            ],
            [
                'name'      => 'slug_fr',
                'label'     => __('teedy/producers.fields.slug_fr'),
                'tab'       => __('teedy/producers.tabs.fr'),
                'type'      => 'text',
                'wrapper'   => ['class' => 'form-group col-md-4']
            ],
            [
                'name'      => 'description_fr',
                'label'     => __('teedy/producers.fields.description_fr'),
                'tab'       => __('teedy/producers.tabs.fr'),
                'type'      => 'summernote',
                'wrapper'   => ['class' => 'form-group col-md-12']
            ],
            [
                'name'      => 'meta_title_fr',
                'label'     => __('teedy/producers.fields.meta_title_fr'),
                'tab'       => __('teedy/producers.tabs.fr'),
                'type'      => 'text',
                'wrapper'   => ['class' => 'form-group col-md-12']
            ],
            [
                'name'      => 'meta_description_fr',
                'label'     => __('teedy/producers.fields.meta_description_fr'),
                'tab'       => __('teedy/producers.tabs.fr'),
                'type'      => 'textarea',
                'wrapper'   => ['class' => 'form-group col-md-12']
            ]
        ]);

        // EN
        CRUD::addFields([
            [
                'name'      => 'title_en',
                'label'     => __('teedy/producers.fields.title_en'),
                'tab'       => __('teedy/producers.tabs.en'),
                'type'      => 'text',
                'wrapper'   => ['class' => 'form-group col-md-8']
            ],
            [
                'name'      => 'slug_en',
                'label'     => __('teedy/producers.fields.slug_en'),
                'tab'       => __('teedy/producers.tabs.en'),
                'type'      => 'text',
                'wrapper'   => ['class' => 'form-group col-md-4']
            ],
            [
                'name'      => 'description_en',
                'label'     => __('teedy/producers.fields.description_en'),
                'tab'       => __('teedy/producers.tabs.en'),
                'type'      => 'summernote',
                'wrapper'   => ['class' => 'form-group col-md-12']
            ],
            [
                'name'      => 'meta_title_en',
                'label'     => __('teedy/producers.fields.meta_title_en'),
                'tab'       => __('teedy/producers.tabs.en'),
                'type'      => 'text',
                'wrapper'   => ['class' => 'form-group col-md-12']
            ],
            [
                'name'      => 'meta_description_en',
                'label'     => __('teedy/producers.fields.meta_description_en'),
                'tab'       => __('teedy/producers.tabs.en'),
                'type'      => 'textarea',
                'wrapper'   => ['class' => 'form-group col-md-12']
            ]
        ]);

        // Logo
        CRUD::addField([
            'name'          => 'logo',
            'label'         => __('teedy/producers.fields.logo'),
            'type'          => 'image',
            'withFiles'     => [
                'disk' => 'public',
                'path' => 'producers-logo',
            ],
            'wrapper'       => ['class' => 'form-group col-md-6']
        ]);



        // Banner
        CRUD::addField([
            'name'          => 'banner',
            'label'         => __('teedy/producers.fields.banner'),
            'type'          => 'image',
            'withFiles'     => [
                'disk' => 'public',
                'path' => 'producers-banner',
            ],
            'wrapper'       => ['class' => 'form-group col-md-6']
        ]);

        // Publish options
        CRUD::addFields([
            [
                'name'          => 'publish',
                'label'         => __('teedy/producers.fields.publish'),
                'type'          => 'select_from_array',
                'options'       => __('admin.common.boolean_values'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-2'],
            ],
            [
                'name'          => 'publish_on_website',
                'label'         => __('teedy/producers.fields.publish_on_website'),
                'type'          => 'select_from_array',
                'options'       => __('admin.common.boolean_values'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'link_to_products',
                'label'         => __('teedy/producers.fields.link_to_products'),
                'type'          => 'select_from_array',
                'options'       => __('admin.common.boolean_values'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-2']
            ]
        ]);
    }

    public function store()
    {
        $this->validateSlug();
        $response = $this->traitStore();
        return $response;
    }

    public function update()
    {
        $this->validateSlug();
        $response = $this->traitUpdate();
        return $response;
    }
}
