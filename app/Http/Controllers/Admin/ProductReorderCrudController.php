<?php

namespace App\Http\Controllers\Admin;

use App\Models\ProductDetail;
use App\Models\ProductCategory;
use Illuminate\Support\Facades\Log;
use App\Http\Requests\ProductReorderRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class ProductReorderCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ProductReorderCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation { reorder as traitReorder; saveReorder as saveReorderTrait; }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(ProductDetail::class);
        CRUD::setEntityNameStrings('produit', 'produits');
        
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('reorder');
        }
    }
    
    public function reorder($categoryId)
    {
        Log::info('Reorder called with categoryId: ' . $categoryId);
        Log::info('User roles: ' . json_encode(backpack_user()->getRoleNames()));
        Log::info('CRUD access: ' . json_encode(CRUD::hasAccess('reorder')));

        $category = ProductCategory::findOrFail($categoryId);
        
        // Set the route for this specific category
        CRUD::setRoute(config('backpack.base.route_prefix') . '/product-categories/' . $categoryId . '/products');
        
        // Add filter to only show products from this category
        // CRUD::addClause('whereHas', 'productable', function($query) use ($categoryId) {
        //     $query->where(function($subQuery) use ($categoryId) {
        //         // For ProductRegular
        //         $subQuery->where(function($regularQuery) use ($categoryId) {
        //             $regularQuery->where('productable_type', 'App\Models\ProductRegular')
        //                     ->where('fk_category_id', $categoryId);
        //         })
        //         // For ProductAccessory  
        //         ->orWhere(function($accessoryQuery) use ($categoryId) {
        //             $accessoryQuery->where('productable_type', 'App\Models\ProductAccessory')
        //                         ->where('fk_category_id', $categoryId);
        //         });
        //     });
        // });
        
        // Set up the reorder operation
        $this->setupReorderOperation($category);
        
        // Set page title
        // CRUD::setHeading('Réorganiser les produits - ' . $category->title);
        // CRUD::setSubheading('Catégorie: ' . $category->title);
        
        return $this->traitReorder();
    }

    protected function setupReorderOperation($category = null)
    {
        // Define which field will be used as label in reorder view
        CRUD::set('reorder.label', 'title_fr'); // or whatever field you use for product title
        CRUD::set('reorder.max_level', 1);
        CRUD::set('reorder.tree', false);

        CRUD::setOperationSetting('reorderColumnNames', [
            'parent_id' => 'category_parent_id',
            'lft' => 'cat_lft',
            'rgt' => 'cat_rgt',
            'depth' => 'cat_depth',
        ]);
    }

    public function saveReorder()
    {
        $categoryId = request()->route('categoryId');
        
        // Set the model and add the category filter
        CRUD::setModel(ProductDetail::class);
        CRUD::addClause('where', 'product_category_id', $categoryId);
        
        return $this->saveReorderTrait();
    }
}
