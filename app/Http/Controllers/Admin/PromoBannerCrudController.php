<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\PromoBannerRequest;
use App\Models\PromoBanner;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class PromoBannerCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class PromoBannerCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\PromoBanner::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/promo-banners');
        CRUD::setEntityNameStrings('Bandeaux infos', 'un bandeau');
        CRUD::orderBy('published', 'DESC');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        CRUD::addColumns([
            [
                'name' => 'published',
                'label' => __('admin.promo_banners.published'),
                'value' => fn ($e) => !!$e->published ? 'Oui' : 'Non',
            ],
            [
                'name' => 'content_fr',
                'label' => __('admin.promo_banners.content_fr'),
            ],
            [
                'name' => 'content_en',
                'label' => __('admin.promo_banners.content_en'),
            ],
            [
                'name' => 'icon',
                'label' => __('admin.promo_banners.icon'),
            ],
            [
                'name' => 'position',
                'label' => __('admin.promo_banners.position'),
                'value' => fn ($e) => $e->position === 'top' ? 'Au dessus du header' : 'Sous le hero',
            ]
        ]);
    }

    protected function setupCreateOperation()
    {
        CRUD::setValidation(PromoBannerRequest::class);
        $this->addPromoBannerFields();
    }

    protected function setupUpdateOperation()
    {
        CRUD::setValidation(PromoBannerRequest::class);
        $this->addPromoBannerFields();
    }

    private function addPromoBannerFields()
    {
        CRUD::addFields([
            [
                'name'          => 'content_fr',
                'label'         => __('admin.promo_banners.content_fr'),
            ],
            [
                'name'          => 'content_en',
                'label'         => __('admin.promo_banners.content_en'),
            ],
            [
                'name'          => 'position',
                'label'         => __('admin.promo_banners.position'),
                'type'          => 'select_from_array',
                'options'       => [
                    'top' => 'Au dessus du header',
                    'bottom' => 'Sous le hero',
                ]
            ],
            [
                'name'          => 'icon',
                'label'         => __('admin.promo_banners.icon'),
                'type'          => 'select_from_array',
                'options'       => [
                    '' => '-',
                    'alert' => 'Attention',
                    'truck' => 'Camion',
                    'clock' => 'Horloge',
                    'message' => 'Message',
                ]
            ],
            [
                'name'          => 'published',
                'label'         => __('admin.promo_banners.published'),
                'type'          => 'checkbox',
            ],
        ]);
    }

    public function store()
    {
        $request = $this->crud->getRequest();
        if ($this->crud->getRequest()->published) $this->unpublishAllOtherBanners($request->position);
        $response = $this->traitStore();
        return $response;
    }

    public function update()
    {
        $request = $this->crud->getRequest();
        if ($this->crud->getRequest()->published) $this->unpublishAllOtherBanners($request->position);
        $response = $this->traitUpdate();
        return $response;
    }

    // Make sure only one banner is published at any time
    private function unpublishAllOtherBanners ($position)
    {
        PromoBanner::where('position', $position)->update(['published' => 0]);
    }
}
