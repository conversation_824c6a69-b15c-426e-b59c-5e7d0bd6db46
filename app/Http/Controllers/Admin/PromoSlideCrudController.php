<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\PromoSlideRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Support\Facades\App;

/**
 * Class PromoSlideCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class PromoSlideCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\PromoSlide::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/promo-slides');
        CRUD::setEntityNameStrings('une publication', 'Visionneuse accueil');
        CRUD::orderBy('published', 'DESC');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupReorderOperation()
    {
        // define which model attribute will be shown on draggable elements
        CRUD::set('reorder.label', 'title_fr');
        // define how deep the admin is allowed to nest the items
        // for infinite levels, set it to 0
        CRUD::set('reorder.max_level', 1);

        // if you don't fully trust the input in your database, you can set 
        // "escaped" to true, so that the label is escaped before being shown
        // you can also enable it globally in config/backpack/operations/reorder.php
        CRUD::set('reorder.escaped', true);
    }

    protected function setupListOperation()
    {
        CRUD::addColumns([
            [
                'name'      => 'published',
                'label'     => __('admin.promo-slides.published'),
                'value' => fn ($e) => !!$e->published ? 'Oui' : 'Non',
            ],
            [
                'name'      => 'title_fr',
                'label'     => __('admin.promo-slides.title_fr'),
            ],
            [
                'name'      => 'image',
                'label'     => __('admin.promo-slides.image'),
                'type'      => 'image',
                'disk'      => 'public',
                'height'    => '80px',
                'width'     => '120px',
            ],
            [
                'name'      => 'image_en',
                'label'     => __('admin.promo-slides.image_en'),
                'type'      => 'image',
                'disk'      => 'public',
                'height'    => '80px',
                'width'     => '120px',
            ],
        ]);
    }

    protected function setupCreateOperation()
    {
        CRUD::setValidation(PromoSlideRequest::class);
        $this->addPromoSlideFields();
    }

    protected function setupUpdateOperation()
    {
        CRUD::setValidation(PromoSlideRequest::class);
        $this->addPromoSlideFields();
    }

    private function addPromoSlideFields()
    {
        CRUD::addFields([
            [
                'name'          => 'published',
                'label'         => __('admin.promo-slides.published'),
                'type'          => 'checkbox',
                'wrapper'       => ['class' => 'form-group col-md-4']
            ],
            [
                'name'          => 'image',
                'label'         => __('admin.promo-slides.image'),
                'type'          => 'image',
                'tab'           => 'Français',
                'withFiles'     => [
                    'disk' => 'public',
                    'path' => 'promo-slides',
                ],
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'image_en',
                'label'         => __('admin.promo-slides.image_en'),
                'type'          => 'image',
                'tab'           => 'Anglais',
                'withFiles'     => [
                    'disk' => 'public',
                    'path' => 'promo-slides',
                ],
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'title_fr',
                'label'         => __('admin.promo-slides.title_fr'),
                'tab'           => 'Français',
                'type'          => 'text',
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'link_fr',
                'label'         => __('admin.promo-slides.link_fr'),
                'tab'           => 'Français',
                'type'          => 'text',
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'text_fr',
                'label'         => __('admin.promo-slides.text_fr'),
                'tab'           => 'Français',
                'type'          => 'textarea',
            ],
            [
                'name'          => 'title_en',
                'label'         => __('admin.promo-slides.title_en'),
                'tab'           => 'Anglais',
                'type'          => 'text',
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'link_en',
                'label'         => __('admin.promo-slides.link_en'),
                'tab'           => 'Anglais',
                'type'          => 'text',
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'text_en',
                'label'         => __('admin.promo-slides.text_en'),
                'tab'           => 'Anglais',
                'type'          => 'textarea',
            ],
        ]);
    }

    public function store()
    {
        // $this->validateWithComparedFields();
        $response = $this->traitStore();
        return $response;
    }

    public function update()
    {
        // dd($this->crud->getRequest()->all());
        $response = $this->traitUpdate();
        return $response;
    }
}
