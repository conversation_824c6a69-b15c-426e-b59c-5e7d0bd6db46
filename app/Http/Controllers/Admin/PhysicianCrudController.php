<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\PhysicianRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class PhysicianCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class PhysicianCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Physician::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/physician');
        CRUD::setEntityNameStrings('médecin', 'Médecins');
        CRUD::denyAccess('delete');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     * 
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        
        //set name and bluelink columns
        CRUD::column('name')->label('Nom');
        CRUD::column('bluelink_id')->label('Code professionnel');
        /**
         * Columns can be defined using the fluent syntax or array syntax:
         * - CRUD::column('price')->type('number');
         * - CRUD::addColumn(['name' => 'price', 'type' => 'number']); 
         */
    }

    /**
     * Define what happens when the Create operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        //set model fields, style it so I can ahve 3 fields per row


        CRUD::field('name')->label('Nom')->wrapper(['class' => 'form-group col-md-4']);
        CRUD::field('bluelink_id')->label('Code professionnel')->wrapper(['class' => 'form-group col-md-4']);
        CRUD::field('address')->label('Adresse')->wrapper(['class' => 'form-group col-md-4']);
        CRUD::field('city')->label('Ville')->wrapper(['class' => 'form-group col-md-4']);
        CRUD::field('zip')->label('Code postal')->wrapper(['class' => 'form-group col-md-4']);
        CRUD::addFields([
            [
                'name' => 'province',
                'type' => 'select_from_array',
                'options' => __('admin.common.region'),
                'label' => 'Province',
                'wrapper' => ['class' => 'form-group col-md-4']
            ]
        ]);
        CRUD::field('phone')->label('Téléphone')->wrapper(['class' => 'form-group col-md-4']);
        CRUD::field('email')->label('Email')->wrapper(['class' => 'form-group col-md-4']);

        
        CRUD::setValidation(PhysicianRequest::class);
        
        /**
         * Fields can be defined using the fluent syntax or array syntax:
         * - CRUD::field('price')->type('number');
         * - CRUD::addField(['name' => 'price', 'type' => 'number'])); 
         */
    }

    //override store operation to sync bluelink
    public function store()
    {
        $response = $this->traitStore();
        // Get the updated user
        $physician = $this->crud->entry;

        if(env('SYNC_BLUELINK')){
            // sync to bluelink
            $physician->BLWeb_CreatePhysician();
        }
        return $response;
    }

    /**
     * Define what happens when the Update operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
        //lock the bluelink_id field
        CRUD::field('bluelink_id')->attributes(['readonly' => 'readonly']);
    }

    //override update operation to sync bluelink
    public function update()
    {
        $response = $this->traitUpdate();
        // Get the updated user
        $physician = $this->crud->entry;

        if(env('SYNC_BLUELINK')){
            // sync to bluelink
            $physician->BLWeb_UpdatePhysician();
        }
        return $response;
    }
}
