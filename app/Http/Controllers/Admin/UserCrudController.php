<?php

namespace App\Http\Controllers\Admin;

use App\Services\BluelinkService;
use App\Http\Requests\UserRequest;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Database\Eloquent\Factories\Relationship;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\PermissionManager\app\Http\Controllers\UserCrudController as CrudController;
use Illuminate\Support\Facades\Hash;
use \App\Http\Controllers\Admin\Operations\SyncUserOperation;

/**
 * Class UserCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class UserCrudController extends CrudController
{
    use \App\Http\Controllers\Admin\Operations\SyncUserOperation;
    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(config('backpack.permissionmanager.models.user'));
        CRUD::setRoute(backpack_url('user'));
        CRUD::setEntityNameStrings('utilisateur', 'utilisateurs');
        Widget::add()->type('script')->content('js/crud/user-details.js');
        CRUD::denyAccess('delete');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    public function setupListOperation()
    {
        CRUD::addFilter(
            [
                'name'  => 'role',
                'type'  => 'dropdown',
                'label' => 'Rôle'
            ],
            function () {
                // Get all roles from the database
                return \Spatie\Permission\Models\Role::pluck('name', 'id')->toArray();
            },
            function ($value) {
                $this->crud->addClause('whereHas', 'roles', function ($query) use ($value) {
                    $query->where('id', $value);
                });
            }
        );
        
        CRUD::addFilter(
            [
                'name'  => 'status',
                'type'  => 'dropdown',
                'label' => ucfirst(__('teedy/users.labels.status'))
            ],
            function () {
                return __('teedy/users.customers.status_value');
            },
            function ($value) {
                $this->crud->addClause('where', 'status', $value);
            }
        );

        CRUD::addColumns([
            [
                'name' => 'userdetail.bluelink_id',
                'type' => 'text',
                'label' => __('teedy/users.labels.bluelink_id'),
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->where(function($q) use ($searchTerm) {
                        $q->where('firstname', 'like', '%'.$searchTerm.'%')
                          ->orWhere('lastname', 'like', '%'.$searchTerm.'%')
                          ->orWhereRaw("CONCAT(firstname, ' ', lastname) LIKE ?", ['%'.$searchTerm.'%'])
                          ->orWhereHas('userdetail', function($q) use ($searchTerm) {
                              $q->where('bluelink_id', 'like', '%'.$searchTerm.'%');
                          });
                    });
                }
            ],
            [
                'name' => 'fullname',
                'type' => 'text',
                'label' => __('teedy/users.labels.fullname'),
            ],
            [
                'name' => 'userdetail.phone',
                'type' => 'text',
                'label' => __('teedy/users.labels.phone'),
            ],
            [
                'name' => 'email',
                'type' => 'text',
                'label' => __('teedy/users.labels.email'),
            ],
            [
                'name'  => 'status',
                'label' => __('teedy/users.labels.status'),
                'type'  => 'select_from_array',
                'options' => __('teedy/users.customers.status_value')
            ],
        ]);
        CRUD::addColumn([
            'name' => 'bluelink_synced',
            'label' => 'Sync Status',
            'type' => 'custom_html',
            'value' => function ($entry) {
                if ($entry->userdetail->bluelink_synced) {
                    return '<div style="text-align: center;"><i class="la la-check" style="color: green;" title="User synced with BlueLink"></i></div>';
                } else {
                    return '<div style="text-align: center;"><i class="la la-exclamation-triangle" style="color: red;" title="User not synced with BlueLink"></i></div>';
                }
            }
        ]);
    }

    public function setupCreateOperation()
    {
        $this->addUserFields();
        $this->crud->setValidation(UserRequest::class);
    }

    public function setupUpdateOperation()
    {
        $this->addUserFields();
        $this->crud->setValidation(UserRequest::class);

        $this->crud->removeField(['password', 'password_confirmation']);
    }

    public function store()
    {
        // Password 
        $this->crud->setRequest($this->crud->validateRequest()); // validate before changing anything
        $this->crud->setRequest($this->handlePasswordInput($this->crud->getRequest())); // handle password input
        $this->crud->unsetValidation(); // validation has already been run

        // This will store the user
        $response = $this->traitStore();

        // Get the newly created user
        $user = $this->crud->entry;

        $user->load('userdetail');

        // sync to bluelink
        if(env('SYNC_BLUELINK')){
            $user->BLWeb_CreateCustomer();

            $user->addresses->each(function($address) use ($user){

                // Set the shipcode
                // $shipcode = $user->userdetail->teedy_client_id . '-' . substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil(10 / strlen($x)))), 1, 10);
                // $shipcode = substr($shipcode, 0, 10);
                // $address->shipcode = $shipcode;
                $address->shipcode = 'main';
                $address->save();

                $address->BLWeb_CreateAddress();
            });
        }


        return $response;
    }

    public function update()
    {
        // Password update
        $this->crud->setRequest($this->crud->validateRequest()); // validate before changing anything
        $this->crud->setRequest($this->handlePasswordInput($this->crud->getRequest())); // handle password input
        $this->crud->unsetValidation(); // validation has already been run

        // This will update the user
        $response = $this->traitUpdate();

        // Get the updated user
        $user = $this->crud->entry;

        $user->load('userdetail');

        // sync to bluelink
        if(env('SYNC_BLUELINK')){
            if($user->userdetail->bluelink_synced){
                $user->BLWeb_UpdateCustomer();

                $user->addresses->each(function($address) use ($user){
                    // If the address has no shipcode, it's a new address, we create it in BL instead of updatating
                    if ($address->shipcode == null) {
                        //old code generation
                        // $shipcode = $user->userdetail->teedy_client_id . '-' . substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil(10 / strlen($x)))), 1, 10);
                        // $shipcode = substr($shipcode, 0, 10);
                        // $address->shipcode = $shipcode;
    
                        //use teedy client id as shipcode, same as user creation
                        $address->shipcode = "main";
                        $address->save();
    
                        $address->BLWeb_CreateAddress();
                    } else {
                        $address->BLWeb_UpdateAddress();
                    }
                });
            }

        }


        return $response;
    }

    protected function addUserFields()
    {
        CRUD::addFields([
            [
                'name'  => 'firstname',
                'label' => __('teedy/users.labels.firstname'),
                'type'  => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [
                'name'  => 'lastname',
                'label' => __('teedy/users.labels.lastname'),
                'type'  => 'text',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [
                'name'  => 'email',
                'label' => __('teedy/users.labels.email'),
                'type'  => 'email',
                'wrapper' => ['class' => 'form-group col-md-4'],
            ],
            [
                'type' => 'repeatable',
                'name' => 'userdetail',
                'label' => __('teedy/users.labels.userdetail'),
                'init_rows' => 1,
                'min_rows' => 1,
                'max_rows' => 1,
                'tab' => __('teedy/users.labels.complementary-tab'),
                'subfields' => [
                    [
                        'name'    => 'quota_user_remaining',
                        'type'    => 'number',
                        'suffix'  => 'gr',
                        'label'   => __('teedy/users.labels.quota_user'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => ['readonly' => 'readonly'],
                    ],
                    [
                        'name'    => 'licence_acmpr',
                        'type'    => 'select_from_array',
                        'label'   => __('teedy/users.labels.licence_acmpr'),
                        'options' => __('teedy/users.boolean_values'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'  => 'contact_info',
                        'type'  => 'custom_html',
                        'value' => '<h3>' . __('teedy/users.labels.contact_info') . '</h3>',
                    ],
                    [
                        'name' => 'teedy_client_id',
                        'type' => 'text',
                        'label' => __('teedy/users.labels.teedy_client_id'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => ['readonly' => 'readonly'],
                    ],
                    [
                        'name' => 'bluelink_id',
                        'type' => 'text',
                        'label' => __('teedy/users.labels.bluelink_id'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => ['readonly' => 'readonly'],
                    ],
                    [
                        'name'    => 'phone',
                        'type'    => 'text',
                        'label'   => __('teedy/users.labels.phone'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'birth_date',
                        'type'    => 'date',
                        'showAsterisk' => true,
                        'label'   => __('teedy/users.labels.birth_date'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => ['required' => 'required'],
                    ],
                    [
                        'name'    => 'language',
                        'type'    => 'select_from_array',
                        'label'   => __('teedy/users.labels.language'),
                        'options' => __('teedy/users.languages'),
                        'default' => 'fr',
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'gender',
                        'type'    => 'select_from_array',
                        'label'   => __('teedy/users.labels.gender'),
                        'options' => __('teedy/users.genders'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'  => 'complementary',
                        'type'  => 'custom_html',
                        'value' => '<br><h3>' . __('teedy/users.labels.complementary') . '</h3>',
                    ],
                    [
                        'name'      => 'inscription_choice',
                        'label'     => __('teedy/users.labels.inscription_choice'),
                        'type'      => 'select_from_array',
                        'options'   => __('teedy/users.customers.inscription_choice_values'),
                        'wrapper'   => ['class' => 'form-group col-md-4']
                    ],
                    [
                        'name'              => 'symptoms',
                        'label'             => __('teedy/users.labels.symptoms'),
                        'type'              => 'select2_from_array',
                        'options'           => __('teedy/users.customers.symptoms_values'),
                        'allows_multiple'   => true,
                        'wrapper'           => ['class' => 'form-group col-md-6'],
                    ],
                    [
                        'name'              => 'consumption',
                        'label'             => __('teedy/users.labels.consumption'),
                        'type'              => 'select2_from_array',
                        'options'           => __('teedy/users.customers.consumption_values'),
                        'allows_multiple'   => true,
                        'wrapper'           => ['class' => 'form-group col-md-6'],
                    ],
                    [
                        'name'      => 'symptoms_other',
                        'label'     => __('teedy/users.labels.symptoms_other'),
                        'type'      => 'textarea',
                        'wrapper'   => ['class' => 'form-group col-md-6']
                    ],
                    [
                        'name'      => 'consumption_other',
                        'label'     => __('teedy/users.labels.consumption_other'),
                        'type'      => 'textarea',
                        'wrapper'   => ['class' => 'form-group col-md-6']
                    ],
                    // Veteran part
                    [
                        'name'  => 'section_veteran',
                        'type'  => 'custom_html',
                        'value' => '<hr/><h3>' . __('teedy/users.labels.section_veteran') . '</h3>',
                    ],
                    [
                        'name'        => 'veteran',
                        'type'        => 'select_from_array',
                        'label'       => __('teedy/users.labels.veteran'),
                        'options'     => __('teedy/users.boolean_values'),
                        'allows_null' => false,
                        'wrapper'     => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'veteran_id',
                        'type'    => 'text',
                        'label'   => 'Code vétéran (External ID)',
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'quota_veteran_remaining',
                        'type'    => 'number',
                        'attributes' => [
                            'step' => "any",
                        ],
                        'suffix'  => 'gr',
                        'label'   => __('teedy/users.labels.quota_veteran_remaining'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'quota_veteran_allowed',
                        'type'    => 'number',
                        'suffix'  => 'gr',
                        'label'   => __('teedy/users.labels.quota_veteran_allowed'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'veteran_quota_date',
                        'type'    => 'date',
                        'label'   => __('teedy/users.labels.veteran_quota_date'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                ],
            ],
        ]);

        // Address part
        CRUD::addFields([
            [
                'type' => 'relationship',
                'name' => 'addresses',
                'label' => __('teedy/users.addresses.labels.addresses'),
                'tab' => __('teedy/users.addresses.labels.addresses'),
                'min_rows' => 1,
                'max_rows' => 3,
                'subfields' => [
                    [
                        'name'    => 'shipname',
                        'type'    => 'text',
                        'label'   => __('teedy/users.addresses.labels.shipname'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'default' => 'Default',
                    ],
                    [
                        'name'    => 'shipcode',
                        'type'    => 'text',
                        'label'   => __('teedy/users.addresses.labels.shipcode'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                        'attributes' => ['readonly' => 'readonly'],
                    ],
                    [
                        'name'    => 'address',
                        'type'    => 'textarea',
                        'label'   => __('teedy/users.addresses.labels.address'),
                        'wrapper' => ['class' => 'form-group col-md-4'],
                    ],
                    [
                        'name'    => 'city',
                        'type'    => 'text',
                        'label'   => __('teedy/users.addresses.labels.city'),
                        'wrapper' => ['class' => 'form-group col-md-3'],
                    ],
                    [
                        'name'    => 'pc',
                        'type'    => 'text',
                        'label'   => __('teedy/users.addresses.labels.pc'),
                        'wrapper' => ['class' => 'form-group col-md-2'],
                    ],
                    [
                        'name'        => 'province',
                        'type'        => 'select_from_array',
                        'label'       => __('teedy/users.addresses.labels.province'),
                        'allows_null' => false,
                        'default'     => 'qc',
                        'options' => array_change_key_case(__('admin.common.region'), CASE_UPPER),
                        'wrapper'     => ['class' => 'form-group col-md-3'],
                    ],
                    [
                        'name'        => 'delivery',
                        'type'        => 'select_from_array',
                        'label'       => __('teedy/users.addresses.labels.delivery'),
                        'options'     => __('teedy/users.boolean_values'),
                        'wrapper'     => ['class' => 'form-group col-md-3'],
                        'default'     => true,
                    ],
                    [
                        'name'        => 'billing',
                        'type'        => 'select_from_array',
                        'label'       => __('teedy/users.addresses.labels.billing'),
                        'options'     => __('teedy/users.boolean_values'),
                        'wrapper'     => ['class' => 'form-group col-md-3'],
                        'default'     => true,
                    ],
                    [
                        'name'        => 'last_use',
                        'type'        => 'select_from_array',
                        'label'       => __('teedy/users.addresses.labels.last_use'),
                        'options'     => __('teedy/users.boolean_values'),
                        'wrapper'     => ['class' => 'form-group col-md-3'],
                        'default'     => true,
                    ],
                ]
            ]
        ]);

        // Prescriptions
        if (request()->id) {
            CRUD::addFields([
                [
                    'name'  => 'section_prescriptions',
                    'type'  => 'custom_html',
                    'value' => '<h3>' . __('teedy/users.labels.prescriptions_part') . '</h3>',
                    'tab'   => __('teedy/users.labels.prescriptions_tab'),
                ],
                [
                    'name'  => 'prescriptions',
                    'type'  => "relationship_prescriptions",
                    'label' => __('teedy/users.prescriptions.title_view'),
                    'tab'   => __('teedy/users.labels.prescriptions_tab'),
                ],
            ]);
        }

        // Admin
        CRUD::addFields([
            [
                'name'  => 'password',
                'type'  => 'password',
                'label' => __('backpack::permissionmanager.password'),
                'tab' => __('teedy/users.labels.admin_tab'),
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => ['required' => true],
            ],
            [
                'name'      => 'roles',
                'type'      => 'select2_multiple',
                'label'     => 'Rôle(s) de l\'utilisateur',
                'tab'       => __('teedy/users.labels.admin_tab'),
                'entity'    => 'roles',
                'attribute' => 'name',
                'model'     => config('permissionmanager.models.role'),
                'pivot'     => true,
                'wrapper'   => ['class' => 'form-group col-md-6'],
                'default'   => [3] // Default role is customer
            ],
            [
                'name'      => 'password_confirmation',
                'type'      => 'password',
                'label'     => __('backpack::permissionmanager.password_confirmation'),
                'tab'       => __('teedy/users.labels.admin_tab'),
                'wrapper'   => ['class' => 'form-group col-md-6'],
                'attributes' => ['required' => true],
            ],
            [
                'name'      => 'status',
                'type'      => 'select_from_array',
                'label'     => __('teedy/users.labels.status'),
                'tab'       => __('teedy/users.labels.admin_tab'),
                'options'   => __('teedy/users.customers.status_value'),
                'wrapper'   => ['class' => 'form-group col-md-3'],
            ],
            [
                'name'      => 'archived',
                'type'      => 'select_from_array',
                'label'     => __('teedy/users.labels.archived'),
                'tab'       => __('teedy/users.labels.admin_tab'),
                'options'   => __('teedy/users.boolean_values'),
                'wrapper'   => ['class' => 'form-group col-md-3'],
            ],
        ]);

        CRUD::addfield([
            'name' => 'proof_identity',
            'label'     => __('teedy/users.labels.proof_identity'),
            'tab'       => __('teedy/users.labels.admin_tab'),
            'type' => 'image',
        ]);
    }

    /**
     * Handle password input fields.
     */
    protected function handlePasswordInput($request)
    {

        // Encrypt password if specified.
        if ($request->input('password')) {
            $request->request->set('password', Hash::make($request->input('password')));
        } else { // Remove password validation if not specified.
            $request->request->remove('password');
        }

        return $request;
    }
}
