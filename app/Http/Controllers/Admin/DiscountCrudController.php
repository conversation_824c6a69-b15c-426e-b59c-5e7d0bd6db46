<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Date;
use Backpack\CRUD\app\Library\Widget;
use App\Http\Requests\DiscountRequest;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Validator;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class DiscountCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class DiscountCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\Discount::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/discounts');
        CRUD::setEntityNameStrings('promo globale', 'promos globales');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        // CRUD::column('id');
        CRUD::column('title_fr')
            ->label(__('teedy/orders.discount.fields.title_fr'));

        CRUD::column('amount')
            ->label(__('teedy/orders.discount.fields.amount'))
            ->value(function ($q) {
                $amount = ($q->type == 'free_shipping') ? '' : ($q->type == 'percent' ? floatval($q->amount) : $q->amount);
                return $amount . __('teedy/orders.promo.types')[$q->type];
            });

        CRUD::column('start_date')
            ->label(__('teedy/orders.discount.fields.start_date'))
            ->value(function ($q) {
                if ($q->start_date) return Date::parse($q->start_date)->translatedFormat('jS F Y');
            });
        CRUD::column('end_date')
            ->label(__('teedy/orders.discount.fields.end_date'))
            ->value(function ($q) {
                if ($q->end_date) return Date::parse($q->end_date)->translatedFormat('jS F Y');
            });

        CRUD::column('recurring_days')
            ->label('Jour Réccurent')
            ->value(function ($q) {
                return $q->is_recurring ? $q->recurring_days : '';
            });

        CRUD::column('is_active')
            ->label('Actif')
            ->type('boolean');

    }

    protected function setupCreateOperation()
    {
        CRUD::setValidation(DiscountRequest::class);
        $this->addDiscountFields();
        Widget::add()->type('script')->content('js/crud/promo-code.js');
    }

    protected function setupUpdateOperation()
    {
        CRUD::setValidation(DiscountRequest::class);
        $this->addDiscountFields();
        Widget::add()->type('script')->content('js/crud/promo-code.js');
    }

    private function addDiscountFields()
    {
        CRUD::addFields([
            [
                'name'          => 'separator_1', 'type' => 'custom_html', 'value' => '<hr><h2>S\'applique à</h2>'
            ],
            [
                'name'          => 'products',
                'label'         => 'Produits',
                'type'          => 'select2_multiple',
                'entity'        => 'products',
                'model'         => "App\Models\ProductDetail",
                'attribute'     => 'sku',
                'pivot'         => true,
                'select_all'    => true,
                'options'       => (fn ($q) => $q->orderBy('sku')->get()),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'separator_2', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name'          => 'producers',
                'label'         => 'Producteurs',
                'type'          => 'select2_multiple',
                'entity'        => 'producers',
                'model'         => "App\Models\Producer",
                'attribute'     => 'title_fr',
                'pivot'         => true,
                'select_all'    => true,
                'options'       => (fn ($q) => $q->orderBy('title_fr')->get()),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'separator_3', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name'          => 'categories',
                'label'         => 'Catégories',
                'type'          => 'select2_multiple',
                'entity'        => 'categories',
                'model'         => "App\Models\ProductCategory",
                'attribute'     => 'title_fr',
                'pivot'         => true,
                'select_all'    => true,
                'options'       => (fn ($q) => $q->orderBy('title_fr')->get()),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'separator_4', 'type' => 'custom_html', 'value' => '<hr/>'
            ],
            [
                'name'          => 'title_fr',
                'label'         => __('teedy/orders.discount.fields.title_fr'),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'title_en',
                'label'         => __('teedy/orders.discount.fields.title_en'),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'separator_0', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name'          => 'amount',
                'label'         => __('teedy/orders.promo.fields.amount'),
                'type'          => 'number',
                'default'       => 0,
                'attributes'    => ['step' => '0.01'],
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'type',
                'label'         => __('teedy/orders.promo.fields.type'),
                'type'          => 'select_from_array',
                'options'       => __('teedy/orders.promo.types_global'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-3']
            ],
            [
                'name'          => 'start_date',
                'label'         => __('teedy/orders.promo.fields.start_date'),
                'wrapper'       => ['class' => 'form-group col-md-3']
            ],
            [
                'name'          => 'end_date',
                'label'         => __('teedy/orders.promo.fields.end_date'),
                'wrapper'       => ['class' => 'form-group col-md-3']
            ],
            [
                'name'          => 'separator_1', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name' => 'is_recurring',
                'label' => 'Rabais réccurent',
                'type' => 'checkbox',
                'wrapper' => ['class' => 'form-group col-md-3'],
                'hint' => 'Ne pas oublier de définir la date de début et de fin plus haut'
            ],
            [
                'name' => 'is_active',
                'label' => 'Promotion active',
                'type' => 'checkbox',
                'wrapper' => ['class' => 'form-group col-md-9']
            ],
            [
                'name' => 'recurring_days',
                'label' => 'Jour Réccurent',
                'type' => 'select_from_array',
                'options' => [
                    'Monday' => 'Lundi',
                    'Tuesday' => 'Mardi',
                    'Wednesday' => 'Mercredi',
                    'Thursday' => 'Jeudi',
                    'Friday' => 'Vendredi',
                    'Saturday' => 'Samedi',
                    'Sunday' => 'Dimanche'
                ],
                'wrapper' => ['class' => 'form-group col-md-6']
            ],
            [
                'name' => 'recurring_start_time',
                'label' => 'Heure de début',
                'type' => 'time',
                'wrapper' => ['class' => 'form-group col-md-3']
            ],
            [
                'name' => 'recurring_end_time',
                'label' => 'Heure de fin',
                'type' => 'time',
                'wrapper' => ['class' => 'form-group col-md-3']
            ]
        ]);
    }

    public function store()
    {
        $this->validateWithComparedFields();
        $response = $this->traitStore();
        
        return $response;
    }

    public function update()
    {
        $this->validateWithComparedFields();

        $response = $this->traitUpdate();

        return $response;
    }

    // Extend PromoCodeRequest validation with compared price/item/dates fields
    // TODO: Find a way to set this into the request file
    private function validateWithComparedFields()
    {
        $fields = $this->crud->getRequest()->all();
        $pc = new DiscountRequest;
        Validator::make($fields, $pc->rules(), $pc->messages(), $pc->attributes())
            ->sometimes('start_date', 'before:end_date', fn ($input) => $input->start_date && $input->end_date)
            ->validate();
    }
}
