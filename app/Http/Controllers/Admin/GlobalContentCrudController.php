<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\CrudController;
use App\Models\GlobalContent;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Illuminate\Http\Request;

/**
 * Class GlobalCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class GlobalContentCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {
        $this->setCrudParameters();
    }

    protected function setCrudParameters()
    {
        CRUD::setModel(GlobalContent::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/global-content');
        CRUD::setEntityNameStrings('contenu global', 'Contenus global');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        CRUD::column('name')->label('Nom');
    }

    protected function setupCreateOperation()
    {
        CRUD::field('name')->type('text')->label('Name')->wrapper(['class' => 'form-group col-md-6']);
        CRUD::field('type')->type('text')->label('Type')->wrapper(['class' => 'form-group col-md-6']);

        CRUD::field('data')->type('repeatable')->label('Data')->fields([
            [
                'name' => 'key',
                'type' => 'text',
                'label' => 'Clé',
                'wrapper' => ['class' => 'form-group col-md-6'],
            ],
            [
                'name' => 'content',
                'type' => 'textarea',
                'label' => 'Contenu',
            ],
        ]);

         // Add custom JavaScript to handle readonly attribute
         CRUD::addField([
            'name' => 'custom_js',
            'type' => 'custom_html',
            'value' => '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    const repeatableFields = document.querySelectorAll(".repeatable-element");

                    repeatableFields.forEach(function(field) {
                        const keyInput = field.querySelector("input[name$=\'[key]\']");
                        if (keyInput && !keyInput.value) {
                            keyInput.removeAttribute("readonly");
                        }
                    });

                    document.addEventListener("click", function(event) {
                        if (event.target && event.target.matches(".add-repeatable-element")) {
                            setTimeout(function() {
                                const newField = document.querySelector(".repeatable-element:last-child");
                                const newKeyInput = newField.querySelector("input[name$=\'[key]\']");
                                if (newKeyInput) {
                                    newKeyInput.removeAttribute("readonly");
                                }
                            }, 100);
                        }
                    });
                });
            </script>',
        ]);
    }

    protected function setupUpdateOperation()
    {
        CRUD::field('name')->type('text')->label('Name')->attributes(['readonly' => 'readonly'])->wrapper(['class' => 'form-group col-md-6']);
        CRUD::field('type')->type('text')->label('Type')->attributes(['readonly' => 'readonly'])->wrapper(['class' => 'form-group col-md-6']);

        CRUD::field('data')->type('repeatable')->label('Data')->fields([
            [
                'name' => 'key',
                'type' => 'text',
                'label' => 'Key',
                'wrapper' => ['class' => 'form-group col-md-6'],
                'attributes' => ['readonly' => 'readonly'],
            ],
            [
                'name' => 'content',
                'type' => 'textarea',
                'label' => 'Content',
            ],
        ]);

        // Add custom JavaScript to handle readonly attribute
        CRUD::addField([
            'name' => 'custom_js',
            'type' => 'custom_html',
            'value' => '<script>
                document.addEventListener("DOMContentLoaded", function() {
                    const repeatableFields = document.querySelectorAll(".repeatable-element");

                    repeatableFields.forEach(function(field) {
                        const keyInput = field.querySelector("input[name$=\'[key]\']");
                        if (keyInput && !keyInput.value) {
                            keyInput.removeAttribute("readonly");
                        }
                    });

                    document.addEventListener("click", function(event) {
                        if (event.target && event.target.matches(".add-repeatable-element")) {
                            setTimeout(function() {
                                const newField = document.querySelector(".repeatable-element:last-child");
                                const newKeyInput = newField.querySelector("input[name$=\'[key]\']");
                                if (newKeyInput) {
                                    newKeyInput.removeAttribute("readonly");
                                }
                            }, 100);
                        }
                    });
                });
            </script>',
        ]);
    }

    public function store()
    {
        $response = $this->traitStore();
        return $response;
    }

    public function update()
    {
        $response = $this->traitUpdate();
        return $response;
    }
}