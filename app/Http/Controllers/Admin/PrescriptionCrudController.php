<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use App\Models\Physician;
use App\Models\Prescription;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;
use App\Http\Requests\PrescriptionRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class PrescriptionCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class PrescriptionCrudController extends CrudController
{
    // use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation { destroy as traitDestroy; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\InlineCreateOperation;

    private $user;
    private $user_args;
    private $user_route;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {

        CRUD::setModel(\App\Models\Prescription::class);
        CRUD::setRoute(backpack_url('prescription', ['client_type' => request()->client_type]));
        CRUD::setEntityNameStrings('prescription', 'prescriptions');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }

        $this->user = User::find(request()->user_id ?? request()->fk_user_id);
        $this->user_route = route('client/{client_type}.edit', ['client_type' => $this->user->client_type, 'id' => $this->user->id]) . '#prescriptions';
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        $this->overrideBreadcrumbs();
        
        $this->getFields();

        Prescription::creating(function($entry) {
            do {
                // Generate 8 character string with capital letters and numbers
                $number = substr(str_shuffle(str_repeat('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 2)), 0, 12);
                
                // Check if number exists
                $exists = Prescription::where('prescription_number', $number)->exists();
            } while ($exists);
        
            $entry->prescription_number = $number;
        });


        // check every prescription of this user and update user the status
        Prescription::created(function($entry) {
            if($entry->status === 'Approved') {
                // Deactivate other prescriptions when status is changed to active
                Prescription::where('fk_user_id', $entry->fk_user_id)
                ->where('id', '!=', $entry->id)
                ->update(['status' => 'Expired']);
            }


            // Sync to bluelink
            if(env('SYNC_BLUELINK')){
                //check if user is bluelink synced first, orelse the prescription creation will fail due to no user on bl
                if($entry->user->userdetail->bluelink_synced){

                    $address = $entry->user->addresses()->first();

                    if(!$address){
                        Log::info('User has no address, fail and exit');
                        return;
                    }

                    if ($address->shipcode == null) {
                        $address->shipcode = 'main';
                        $address->save();

                        $address->BLWeb_CreateAddress();
                    }
                    $entry->BLWeb_CreatePrescription();
                }else{
                    Log::info('User not synced to Bluelink, creating user on bluelink...');
                    //check if the user has an address, if not fail the creation

                    $address = $entry->user->addresses()->first();

                    if(!$address){
                        Log::info('User has no address, fail and exit');
                        return;
                    }


                    $entry->user->BLWeb_CreateCustomer();

                    // dd($address->shipcode);
                    if ($address->shipcode == null) {
                        $address->shipcode = 'main';
                        $address->save();

                        $address->BLWeb_CreateAddress();
                    }



                    $entry->BLWeb_CreatePrescription();
                }
            }

            // Update user status
            $entry->user->updateStatus();

            $old_quota = 0;
            $this->user->updateQuota($old_quota, $entry->daily_dosage);

            //reactivate the mail notifications
            $this->user->userdetail->mail_notifications = true;
            $this->user->userdetail->save();
        });

        CRUD::addSaveAction([
            'name' => 'save_action_one',
            'redirect' => function($crud, $request, $itemId) {
                $clientType = User::find($request->input('fk_user_id'))->client_type;
                $clientId = $request->input('fk_user_id') ?? $request->input('user_id');
                $baseUrl = config('backpack.base.route_prefix', 'admin');
                return url($baseUrl . '/client/' . $clientType . '/' . $clientId . '/edit') . '#prescriptions';
            },
            'button_text' => 'Enregistrer',
        ]);


    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->overrideBreadcrumbs();
        $this->getFields();

        // If current prescription already has a number, disabled it
        $prescription_number = $this->crud->getCurrentEntry()->prescription_number;
        if ($prescription_number) {
            CRUD::field('prescription_number')->attributes(['readonly' => 'readonly']);
        }

        // check the update of the prescription if there is an existing number. there wont be any if coming from the create api
        Prescription::updating(function($entry) {
            if(!$entry->prescription_number){
                do {
                    // Generate 8 character string with capital letters and numbers
                    $number = substr(str_shuffle(str_repeat('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789', 2)), 0, 12);
                    
                    // Check if number exists
                    $exists = Prescription::where('prescription_number', $number)->exists();
                } while ($exists);
            
                $entry->prescription_number = $number;

                // Sync status to Bluelink
                if(env('SYNC_BLUELINK')) {
                    $entry->BLWeb_UpdatePrescription();
                }
            }

        });

        // // // check every prescription of this user and update user the status
        // Deactivate other prescriptions when status is changed to active
        Prescription::updated(function($entry) {
            if($entry->status === 'Approved') {

                $otherPrescriptions = Prescription::where('fk_user_id', $entry->fk_user_id)
                ->where('id', '!=', $entry->id)
                ->get();


                foreach($otherPrescriptions as $prescription) {
                    $prescription->update(['status' => 'Expired']);
                    
                    // Sync status to Bluelink
                    if(env('SYNC_BLUELINK')) {
                        $prescription->BLWeb_UpdatePrescription();
                    }
                }
                //reactivate the mail notifications
                $entry->user->userdetail->mail_notifications = true;
                $entry->user->userdetail->save();

            }
            $entry->user->updateStatus();


        });

        CRUD::addSaveAction([
            'name' => 'save_action_one',
            'redirect' => function($crud, $request, $itemId) {
                $clientType = User::find($request->input('fk_user_id'))->client_type;
                $clientId = $request->input('fk_user_id') ?? $request->input('user_id');
                $baseUrl = config('backpack.base.route_prefix', 'admin');
                return url($baseUrl . '/client/' . $clientType . '/' . $clientId . '/edit') . '#prescriptions';
            },
            'button_text' => 'Enregistrer',
        ]);
    }

    public function destroy($id)
    {
        $this->crud->hasAccessOrFail('delete');
        return $this->crud->delete($id);
    }

    public function update()
    {
        $old_entry = $this->crud->getCurrentEntry();

        $response = $this->traitStore();

        $entry = $this->crud->getCurrentEntry();
        // Sync to Bluelink
        if(env('SYNC_BLUELINK')){
            if (!$old_entry->getOriginal('prescription_number')) {
                // dd("creating");
                // dd($entry);
                // No previous prescription number - create new
                $entry->BLWeb_CreatePrescription();
            } else {
                // Has existing prescription number - update
                // dd("updating");
                $entry->BLWeb_UpdatePrescription();

            }
        }

        $entry->user->updateQuota($old_entry->daily_dosage, $entry->daily_dosage);


      // do something after save
      return $response;
    }

    protected function getFields()
    {
        CRUD::setValidation(PrescriptionRequest::class);
        
        // Secure folder logic
        $folder = null;
        if($this->user != null){
            $folder = $this->user->folder_token;
        }

        // get physicians bl id
        $physicians = Physician::pluck('bluelink_id', 'bluelink_id')->toArray();

        CRUD::addFields([
            [
                'name'          => 'user',
                'type'          => 'relationship',
                'label'         => __('teedy/users.prescriptions.labels.user'),
                'value'         => request()->user_id,
                'attribute'     => 'full_name',
                'attributes'    => ['disabled' => 'disabled'],
                'wrapper'       => ['class' => 'form-group col-md-3'],
            ],
            [
                'name'          => 'fk_user_id',
                'type'          => 'hidden',
                'value'         => request()->user_id,
            ],
            [
                'name'    => 'code_teedy',
                'type'    => 'text',
                'value'   => $this->user->userdetail->teedy_client_id,
                'attributes' => ['disabled' => 'disabled'],
                'label'   => __('teedy/users.prescriptions.labels.code_admin'),
                'wrapper' => ['class' => 'form-group col-md-3'],
            ],
            [
                'name'    => 'code_admin',
                'type'    => 'hidden',
                'value'   => $this->user->userdetail->teedy_client_id,
            ],
            [
                'name'    => 'status',
                'type'    => 'select_from_array',
                'label'   => __('teedy/users.prescriptions.labels.status'),
                'options' => __('common.prescriptions.status'),
                'wrapper' => ['class' => 'form-group col-md-3'],
                'default' => 'pending',
            ],
            [
                'name'    => 'sep',
                'type'    => 'custom_html',
                'value'   => '<hr>',
            ],
            [
                'name'    => 'prescription_number',
                'type'    => 'text',
                'label'   => __('teedy/users.prescriptions.labels.number'),
                'attributes'    => ['disabled' => 'disabled'],
                'wrapper' => ['class' => 'form-group col-md-3'],           
            ],
            [
                'name'    => 'start_date',
                'type'    => 'date',
                'label'   => __('teedy/users.prescriptions.labels.start_date'),
                'wrapper' => ['class' => 'form-group col-md-3'],
            ],
            [
                'name'    => 'end_date',
                'type'    => 'date',
                'label'   => __('teedy/users.prescriptions.labels.end_date'),
                'wrapper' => ['class' => 'form-group col-md-3'],
                'attributes' => [
                    'min' => \Carbon\Carbon::tomorrow()->toDateString(),
                ],
                'validation' => 'required|date|after:today',
            ],
            // [
            //     'name'    => 'clinic_name',
            //     'type'    => 'select_from_array',
            //     'label'   => __('teedy/users.prescriptions.labels.clinic'),
            //     'options' => $physicians,
            //     'wrapper' => ['class' => 'form-group col-md-6'],
            // ],
            [
                'name'    => 'doc_name',
                'type'    => 'select_from_array',
                'label'   => __('teedy/users.prescriptions.labels.doctor'),
                'options' => $physicians,
                'wrapper' => ['class' => 'form-group col-md-6'],
            ],
            //daily dosage is actually monthly dosage
            [
                'name'       => 'daily_dosage',
                'type'       => 'number',
                'label'      => __('teedy/users.prescriptions.labels.dosage'),
                'attributes' => [
                    'step'   => '0.01',
                    'min'    => '0'
                ],
            ],
            // [
            //     'name'    => 'prescription_photo',
            //     'type'    => 'upload',
            //     'label'   => __('teedy/users.prescriptions.labels.image'),
            //     'disk'    => 'private',
            //     'upload'  => true,
            //     // 'withFiles' => [
            //     //     'disk' => 'private', // the disk where file will be stored
            //     //     'path' => '/' . $folder, // the path inside the disk where file will be stored
            //     // ],
            //     'prefix'  => $folder ? $folder . '/' : '',
            // ],
            // [
            //     'name'    => 'validity',
            //     'type'    => 'checkbox',
            //     'label'   => __('teedy/users.prescriptions.labels.image_is_valid'),
            //     'wrapper' => ['class' => 'form-group col-md-3'],
            // ],
            
        ]);

        CRUD::addfield([
            'name' => 'prescription_photo',
            'label'     => __('teedy/users.prescriptions.labels.image'),
            'type'      => 'upload',
            'upload'    => true,
            'disk'      => 'private',
        ]);



        $this->crud->replaceSaveActions([
            'name' => __('teedy/users.prescriptions.save'),
            'visible' => true,
            'redirect' => fn () => $this->user_route,
        ]);
    }

    // Replace breadcrumbs and title link for user info
    private function overrideBreadcrumbs ()
    {
        $this->crud->denyAccess('list');

        $this->data['breadcrumbs'] = [
            trans('backpack::crud.admin') => backpack_url('dashboard'),
            $this->user->fullname => $this->user_route,
        ];

        CRUD::setHeading('<a href="' . $this->user_route . '">' . $this->user->fullname . '</a>');
    }

}
