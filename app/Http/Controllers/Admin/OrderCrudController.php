<?php

namespace App\Http\Controllers\Admin;

use App\Models\Order;
use Prologue\Alerts\Facades\Alert;
use App\Http\Requests\OrderRequest;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Redirect;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use App\Http\Controllers\Admin\Operations\RefundOperation;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class OrderCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class OrderCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    // use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation {
        update as traitUpdate;
    }
    use \App\Http\Controllers\Admin\Operations\RefundOperation;
    use \App\Http\Controllers\Admin\Operations\CancelOperation;



    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     *
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Order::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/order');
        CRUD::setEntityNameStrings(__('teedy/orders.orders.singular'), __('teedy/orders.orders.plural'));
        CRUD::allowAccess('update-status');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     * @see  https:   //backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        $this->crud->addButtonFromModelFunction('line', 'change_status', 'changeStatus', 'beginning');
        $this->crud->addFilter(
            [
                'name'  => 'status',
                'type'  => 'dropdown',
                'label' => ucfirst(__('teedy/orders.orders.labels.status'))
            ],
            __('teedy/orders.orders.status_values'),
            function ($value) {
                $this->crud->addClause('where', 'status', $value);
            }
        );
        $this->crud->addFilter(
            [
                'name'  => 'fk_user_id',
                'type'  => 'select2_ajax',
                'label' => ucfirst(__('teedy/users.labels.fullname'))
            ],
            route('user.filter'),
            function ($value) {
                $this->crud->addClause('where', 'fk_user_id', $value);
            }
        );
        CRUD::addColumns([
            [
                'name' => 'user.userdetail.teedy_client_id',
                'type' => 'text',
                'label' => __('teedy/users.labels.teedy_client_id') . ' du client',
                'searchLogic' => function ($query, $column, $searchTerm) {
                    $query->whereHas('user.userdetail', function ($query) use ($searchTerm) {
                        $query->where('teedy_client_id', 'like', '%' . $searchTerm . '%');
                    });
                },
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return route('client/{client_type}.edit', ['id' => $entry->fk_user_id, 'client_type' => $entry->user->client_type]);
                    },
                ]
            ],
            [
                'name'      => 'user',
                'label'     => ucfirst(__('teedy/orders.orders.labels.user')),
                'type'      => 'relationship',
                'value'     => request()->query('user_id'),
                'attribute' => 'full_name',
                'wrapper' => [
                    'href' => function ($crud, $column, $entry, $related_key) {
                        return route('client/{client_type}.edit', ['id' => $entry->fk_user_id, 'client_type' => $entry->user->client_type]);
                    },
                ]
            ],
            // [
            //     'name'  => 'moneris_id',
            //     'label' => ucfirst(__('teedy/orders.orders.labels.moneris_id'))
            // ],
            [
                'name'    => 'status',
                'label'   => ucfirst(__('teedy/orders.orders.labels.status')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.orders.status_values'),
            ],
            [
                'name'  => 'date_in_progress',
                'label' => ucfirst(__('teedy/orders.orders.labels.date_in_progress')),
                'type'  => 'datetime',
            ],
            [
                'name'  => 'total',
                'label' => ucfirst(__('teedy/orders.orders.labels.total'))
            ],
            [
                'name'  => 'bluelink_status',
                'label' => 'Statut Bluelink'
            ]



        ]);
    }

    /**
     * Define what happens when the Create operation is loaded.
     *
     * @see https:   //backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(OrderRequest::class);
        
        $moneris_id = $this->crud->getCurrentEntry()->moneris_id;

        CRUD::addFields([
            [
                'name'    => 'user',
                'label'   => ucfirst(__('teedy/orders.orders.labels.user')),
                'type'    => 'relationship',
                'allows_null' => false,
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'value'     => request()->query('user_id'),
                'attribute' => 'full_name'
            ],
            [
                'name'    => 'moneris_id',
                'label'   => ucfirst(__('teedy/orders.orders.labels.moneris_id')),
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
            ],
            [
                'name'    => 'total',
                'label'   => ucfirst(__('teedy/orders.orders.labels.total')),
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'        => 'status',
                'label'       => ucfirst(__('teedy/orders.orders.labels.status')),
                'type'        => 'select_from_array',
                'options'     => __('teedy/orders.orders.status_values'),
                'allows_null' => false,
                'wrapper'     => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'disabled' => 'disabled'
                ]
            ],
            [
                'name'    => 'bluelink_status',
                'label'   => 'Statut Bluelink',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'sales_order_number',
                'label'   => 'Numero de commande BlueLink',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'date_in_progress',
                'label'   => ucfirst(__('teedy/orders.orders.labels.date_in_progress')),
                'type'    => 'datetime',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'date_shipped',
                'label'   => ucfirst(__('teedy/orders.orders.labels.date_shipped')),
                'type'    => 'datetime',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'date_delivered',
                'label'   => ucfirst(__('teedy/orders.orders.labels.date_delivered')),
                'type'    => 'datetime',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'date_cancelled',
                'label'   => ucfirst(__('teedy/orders.orders.labels.date_cancelled')),
                'type'    => 'datetime',
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'cardholder',
                'label'   => ucfirst(__('teedy/orders.orders.labels.cardholder')),
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
                'type'    => 'text',
                'attributes' => [
                    'readonly' => 'readonly'
                ]
            ],
            [
                'name'    => 'additional_content',
                'label'   => ucfirst(__('teedy/orders.orders.labels.additional_content')),
                'wrapper' => [
                    'class' => 'form-group col-md-4',
                ],
            ],
            [
                'name'  => 'section_content',
                'type'  => 'custom_html',
                'value' => '<hr><a class="btn btn-primary" href="/api/user/order/generate/' . $moneris_id . '">Télécharger la facture</a>',
            ]
            // [
            //     'name'      => 'order_content',
            //     'type'      => 'order_content',
            //     'label'     => ucfirst(__('teedy/orders.orders.labels.order_content_fields.repeatable')),
            // ],
            // [
            //     'name'  => 'client_info',
            //     'type'  => 'section_address',
            //     'label' => '<hr/><h3>' . __('teedy/orders.orders.labels.section_address') . '</h3>',
            // ],
        ]);

        // Order::created(function($entry) {
        //     $entry->BLWeb_Createorder();
        // });
    }

    /**
     * Define what happens when the Update operation is loaded.
     *
     * @see https:   //backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
        CRUD::field('user')->attributes(['disabled' => true]);
        CRUD::field('moneris_id')->attributes(['readonly' => 'readonly']);
    }

    // create new action for changing status with button action
    protected function setupUpdateStatusRoutes($segment, $routeName, $controller)
    {
        Route::get($segment . '/{id}/update-status/{new_status}', [
            'as'        => $routeName . '.update_status',
            'uses'      => $controller . '@updateStatus',
            'operation' => 'update-status',
        ]);
    }

    // Update status timestamp
    // This will update using the quick action button in list
    public function updateStatus($id, $new_status)
    {
        $this->crud->hasAccessOrFail('update-status');
        $this->crud->setOperation('update-status');

        $order = $this->crud->model->findOrFail($id);

        $old_status = $order->status;

        // if date field are linked to this status, update the datetime
        if (!isset($order->{'date_' . $new_status})) {
            $order->{'date_' . $new_status} = now();
        }
        $order->save();

        //sync update order with bluelink
        try {
            $order->BLWeb_UpdateOrder($new_status);
            // Alert::success(__('teedy/orders.orders.status_changing'))->flash();
        } catch (\Exception $e) {
            Log::info('error while syncing order status with bluelink: ' . $e->getMessage());
            $order->status = $old_status;
            $order->save();
            Alert::error('Error while syncing')->flash();
        }



        return Redirect::to($this->crud->route);
    }

    // Update status timestamp
    // This will update using the form
    public function update()
    {

        $response = $this->traitUpdate();

        //store date based on order status
        $order = Order::find(request()->id);

        if (isset($order->status)) {
            switch ($order->status) {
                case 'in_progress':
                    $order->date_in_progress = now();
                    break;
                case 'shipped':
                    $order->date_shipped = now();
                    break;
                case 'delivered':
                    $order->date_delivered = now();
                    break;
                case 'cancelled':
                    $order->date_cancelled = now();
                    break;
            }
            $order->save();


        }
        return $response;

        //sync update order with bluelink
    }


}
