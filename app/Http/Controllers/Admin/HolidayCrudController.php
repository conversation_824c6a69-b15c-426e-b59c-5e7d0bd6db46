<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\HolidayRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class HolidayCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class HolidayCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\Holiday::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/holidays');
        CRUD::setEntityNameStrings('date sans livraison', 'dates sans livraison');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        CRUD::column('title_fr')
            ->label(__('teedy/orders.promo.fields.title_fr'));
        CRUD::column('title_en')
            ->label(__('teedy/orders.promo.fields.title_en'));
        CRUD::column('date')
            ->label(__('teedy/orders.promo.fields.date'));
        CRUD::addColumn([
            'name' => 'active',
            'label' => __('teedy/orders.promo.fields.active'),
            'type' => 'model_function',
            'function_name' => 'getActiveAttribute', // nom de la méthode dans le modèle
        ]);

    }

    protected function setupCreateOperation()
    {
        CRUD::setValidation(HolidayRequest::class);

        CRUD::addFields([
            [
                'name'          => 'title_fr',
                'label'         => __('teedy/orders.discount.fields.title_fr'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'title_en',
                'label'         => __('teedy/orders.discount.fields.title_en'),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'date',
                'label'         => __('teedy/orders.promo.fields.date'),
                'type'          => 'date',
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'active',
                'label'         => __('teedy/orders.promo.fields.active'),
                'type'          => 'select_from_array',
                'options'       => __('admin.common.boolean_values'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-4']
            ],
        ]);
    }

    protected function setupUpdateOperation()
    {
        $this->addHolidayFields();
    }

    private function addHolidayFields()
    {
        $entry = $this->crud->getCurrentEntryId();

        if ($entry) {
            $entry = \App\Models\Holiday::find($entry);
        }


        // dd($entry->getActiveAttribute());
        CRUD::addFields([
            [
                'name'          => 'title_fr',
                'label'         => __('teedy/orders.discount.fields.title_fr'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'title_en',
                'label'         => __('teedy/orders.discount.fields.title_en'),
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'date',
                'label'         => __('teedy/orders.promo.fields.date'),
                'type'          => 'date',
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-6']
            ],
            [
                'name'          => 'active',
                'label'         => __('teedy/orders.promo.fields.active'),
                'type'          => 'select_from_array',
                'options'       => __('admin.common.boolean_values'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-4'],
                'value'       => $entry ? $entry->getRawOriginal('active') : 1
            ],
        ]);
    }
}
