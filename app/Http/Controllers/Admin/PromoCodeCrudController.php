<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\PromoCodeRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;
use Backpack\CRUD\app\Library\Widget;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Validator;

/**
 * Class PromoCodeCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class PromoCodeCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\PromoCode::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/promo-codes');
        CRUD::setEntityNameStrings('code promo', 'codes promo');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        CRUD::column('code')
            ->label(__('teedy/orders.promo.fields.code'));
        CRUD::column('amount')
            ->label(__('teedy/orders.promo.fields.amount'))
            ->value(function ($q) {
                $amount = ($q->type == 'free_shipping') ? '' : ($q->type == 'percent' ? floatval($q->amount) : $q->amount);
                return $amount . __('teedy/orders.promo.types')[$q->type];
            });
        CRUD::column('start_date')
            ->label(__('teedy/orders.promo.fields.start_date'))
            ->value(function ($q) {
                if ($q->start_date) return Date::parse($q->start_date)->translatedFormat('jS F Y');
            });
        CRUD::column('end_date')
            ->label(__('teedy/orders.promo.fields.end_date'))
            ->value(function ($q) {
                if ($q->end_date) return Date::parse($q->end_date)->translatedFormat('jS F Y');
            });
        CRUD::column('minimum_price')
            ->label(__('teedy/orders.promo.fields.minimum_price'))
            ->value(function ($q) {
                if ($q->minimum_price) return number_format($q->minimum_price, 2).'$';
            });
        CRUD::column('maximum_price')
            ->label(__('teedy/orders.promo.fields.maximum_price'))
            ->value(function ($q) {
                if ($q->maximum_price) return number_format($q->maximum_price, 2).'$';
            });
    }

    protected function setupCreateOperation()
    {
        CRUD::setValidation(PromoCodeRequest::class);
        $this->addPromoCodeFields();
        Widget::add()->type('script')->content('js/crud/promo-code.js');
    }

    protected function setupUpdateOperation()
    {
        CRUD::setValidation(PromoCodeRequest::class);
        $this->addPromoCodeFields();
        Widget::add()->type('script')->content('js/crud/promo-code.js');
    }

    private function addPromoCodeFields()
    {
        CRUD::addFields([
            [
                'name'          => 'code',
                'label'         => __('teedy/orders.promo.fields.code'),
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'amount',
                'label'         => __('teedy/orders.promo.fields.amount'),
                'type'          => 'number',
                'default'       => 0,
                'attributes'    => ['step' => '0.01'],
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'type',
                'label'         => __('teedy/orders.promo.fields.type'),
                'type'          => 'select_from_array',
                'options'       => __('teedy/orders.promo.types'),
                'allows_null'   => false,
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'separator_1', 'type' => 'custom_html', 'value' => '<hr><h2>Conditions</h2>'
            ],
            [
                'name'          => 'minimum_price',
                'label'         => __('teedy/orders.promo.fields.minimum_price'),
                'type'          => 'number',
                'suffix'        => '$',
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'maximum_price',
                'label'         => __('teedy/orders.promo.fields.maximum_price'),
                'type'          => 'number',
                'suffix'        => '$',
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'separator_2', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name'          => 'minimum_items',
                'label'         => __('teedy/orders.promo.fields.minimum_items'),
                'type'          => 'number',
                'attributes'    => ['step' => '1'],
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'maximum_items',
                'label'         => __('teedy/orders.promo.fields.maximum_items'),
                'type'          => 'number',
                'attributes'    => ['step' => '1'],
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
            [
                'name'          => 'separator_3', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name'          => 'start_date',
                'label'         => __('teedy/orders.promo.fields.start_date'),
                'wrapper'       => ['class' => 'form-group col-md-2'],
                'showAsterisk'  => true
            ],
            [
                'name'          => 'end_date',
                'label'         => __('teedy/orders.promo.fields.end_date'),
                'wrapper'       => ['class' => 'form-group col-md-2'],
                'showAsterisk'  => true
            ],
            [
                'name'          => 'separator_4', 'type' => 'custom_html', 'value' => '<div></div>'
            ],
            [
                'name'          => 'nb_per_user',
                'label'         => __('teedy/orders.promo.fields.nb_per_user'),
                'type'          => 'number',
                'attributes'    => ['step' => '1'],
                'wrapper'       => ['class' => 'form-group col-md-2']
            ],
        ]);
    }

    public function store()
    {
        $this->validateWithComparedFields();
        $response = $this->traitStore();
        return $response;
    }

    public function update()
    {
        $this->validateWithComparedFields();
        $response = $this->traitUpdate();
        return $response;
    }

    // Extend PromoCodeRequest validation with compared price/item/dates fields
    // TODO: Find a way to set this into the request file
    private function validateWithComparedFields()
    {
        $fields = $this->crud->getRequest()->all();
        $pc = new PromoCodeRequest;

        Validator::make($fields, $pc->rules(), $pc->messages(), $pc->attributes())
            ->sometimes('minimum_price', 'lt:maximum_price', fn ($input) => $input->minimum_price > 0 && $input->maximum_price > 0)
            ->sometimes('minimum_items', 'lt:maximum_items', fn ($input) => $input->minimum_items > 0 && $input->maximum_items > 0)
            ->sometimes('start_date', 'before:end_date', fn ($input) => $input->start_date && $input->end_date)
            ->validate();
    }
}
