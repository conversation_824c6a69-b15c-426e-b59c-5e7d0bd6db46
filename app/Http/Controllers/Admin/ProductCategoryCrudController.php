<?php

namespace App\Http\Controllers\Admin;

// Custom controller for slug check
// use Backpack\CRUD\app\Http\Controllers\CrudController;
use App\Services\ProductCacheService;

use Illuminate\Support\Facades\Artisan;
use App\Http\Controllers\CrudController;
use App\Http\Requests\ProductCategoryRequest;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class ProductCategoryCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ProductCategoryCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation { store as traitStore; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation { update as traitUpdate; }
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation {saveReorder as traitSaveReorder; }

    public function setup()
    {
        CRUD::setModel(\App\Models\ProductCategory::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/product-categories');
        CRUD::setEntityNameStrings('catégorie', 'catégories');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
            CRUD::denyAccess('reorder');
        }
    }

    protected function setupListOperation()
    {
        CRUD::column('title_fr')
            ->label(__('teedy/products.categories.title_fr'));
        CRUD::column('slug_fr');
        CRUD::column('title_en')
            ->label(__('teedy/products.categories.title_en'));
        CRUD::column('slug_en');
    }

    protected function setupCreateOperation()
    {
        CRUD::field('title_fr')->label(__('teedy/products.categories.title_fr'))->size(8);
        CRUD::field('slug_fr')->label(__('teedy/products.categories.slug_fr'))->size(4);
        CRUD::field('title_en')->label(__('teedy/products.categories.title_en'))->size(8);
        CRUD::field('slug_en')->label(__('teedy/products.categories.slug_en'))->size(4);
        CRUD::setValidation(ProductCategoryRequest::class);
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }

    protected function setupReorderOperation()
    {
        // Define which field will be used to reorder
        CRUD::set('reorder.label', 'title_fr');
        CRUD::set('reorder.max_level', 1);
        // Disable nested set functionality
        // CRUD::set('reorder.tree', false);
    }

    protected function saveReorder()
    {
        $response = $this->traitSaveReorder();
        ProductCacheService::refreshCache();
        return $response;
    }

    public function store()
    {
        $this->validateSlug();
        $response = $this->traitStore();
        return $response;
    }

    public function update()
    {
        $this->validateSlug();
        $response = $this->traitUpdate();
        return $response;
    }
}
