<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Backpack\CRUD\app\Library\Widget;
use App\Http\Controllers\Admin\UserCrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class UserCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ClientCrudController extends UserCrudController
{
    public function setup()
    {
        CRUD::setModel(config('backpack.permissionmanager.models.user'));
        CRUD::setRoute(backpack_url('client', ['client_type' => request()->client_type]));
        CRUD::setEntityNameStrings(__('teedy/users.customers.singular-' . request()->client_type), __('teedy/users.customers.plural-' . request()->client_type));
        Widget::add()->type('script')->content('js/crud/user-details.js');
        CRUD::denyAccess('delete');
        //deny access if user role is not admin or superadmin

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }

    }

    public function setupListOperation()
    {
        parent::setupListOperation();

        $this->crud->with('userdetail');
        CRUD::removeColumns(['roles']);

        $this->crud->addClause('whereHas', 'roles', fn ($query) => $query->where('name', 'client'));
        $this->crud->addClause('whereHas', 'userdetail', fn ($query) => $query->where('veteran', request()->client_type == 'veteran' ? 1 : 0));

        if(request()->client_type == 'veteran') {
            CRUD::addColumn([
                'name'    => 'userdetail.quota_veteran_remaining',
                'type'    => 'number',
                'label'   => 'Quota Vétéran Restant',
                'orderable' => true,
                'priority' => 1,
                'orderLogic' => function ($query, $column, $columnDirection) {
                return $query->leftJoin('user_details', 'users.id', '=', 'user_details.fk_user_id')
                            ->orderBy('user_details.quota_veteran_remaining', $columnDirection)
                            ->select('users.*');
            }
            ]);
        }else{
            CRUD::addColumn([
                'name'    => 'userdetail.quota_user_remaining',
                'type'    => 'number',
                'label'   => 'Quota Utilisateur Restant',
                'priority' => 1,
                'orderable' => true,
                'orderLogic' => function ($query, $column, $columnDirection) {
                return $query->leftJoin('user_details', 'users.id', '=', 'user_details.fk_user_id')
                            ->orderBy('user_details.quota_user_remaining', $columnDirection)
                            ->select('users.*');
            }
            ]);
        }

        CRUD::addColumn([
            'name'    => 'orders',
            'type'    => 'relationship_count',
            'label'   => __('teedy/orders.orders.plural'),
            'suffix'  => ' ' . __('teedy/orders.orders.plural'),
            'wrapper' => ['href' => fn ($crud, $column, $entry) => route('order.index', ['fk_user_id' => $entry->id])]
        ]);


    }
    // Ajax function to search by name of client in other crud panel
    public function searchFilter() {
        $term = request()->input('term');
        return User::where('firstname', 'like', '%' . $term . '%')
            ->orWhere('lastname', 'like', '%' . $term . '%')
            ->get()
            ->pluck('fullname', 'id');
    }
}
