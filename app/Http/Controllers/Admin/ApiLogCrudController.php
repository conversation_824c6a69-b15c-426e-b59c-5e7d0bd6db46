<?php

namespace App\Http\Controllers\Admin;

use App\Http\Requests\ApiLogRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class ApiLogCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ApiLogCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ShowOperation;

    public function setup()
    {
        CRUD::setModel(\App\Models\ApiLog::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/api-log');
        CRUD::setEntityNameStrings('API Log', 'API Logs');
        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    protected function setupListOperation()
    {
        CRUD::column('model');
        CRUD::column('method');
        CRUD::column('error_message');
        CRUD::column('created_at');
    }

    protected function setupCreateOperation()
    {
        CRUD::setValidation(ApiLogRequest::class);

        CRUD::field('model');
        CRUD::field('method');
        CRUD::field('error_message');
    }

    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}
