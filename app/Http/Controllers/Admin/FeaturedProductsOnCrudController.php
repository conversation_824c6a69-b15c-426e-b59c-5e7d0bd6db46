<?php

namespace App\Http\Controllers\Admin;

use App\Models\ProductDetail;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Artisan;
use App\Services\ProductCacheService;
use Illuminate\Support\Facades\Log;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class FeaturedProductsOnCrudController
 */
class FeaturedProductsOnCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation {
        saveReorder as traitSaveReorder;
    }

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     */
    public function setup()
    {
        CRUD::setModel(ProductDetail::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/featured-products-on');
        CRUD::setEntityNameStrings('produit vedette ON', 'produits vedettes ON');
        CRUD::addClause('where', 'featured', true);
        CRUD::addClause('where', 'region', 'on');
    }

    /**
     * Configure the ReorderOperation
     */
    protected function setupReorderOperation()
    {
        CRUD::set('reorder.label', 'title_fr');
        CRUD::set('reorder.max_level', 0);
    }

    /**
     * Override the saveReorder method to clear cache after reordering
     */
    public function saveReorder()
    {
        $response = $this->traitSaveReorder();
        
        // Clear cache after successful reorder
        Log::channel('cache')->info('Featured products reordered');
        ProductCacheService::refreshCache();
        
        return $response;
    }

    /**
     * Define what happens when the List operation is loaded.
     */
    protected function setupListOperation()
    {
        CRUD::orderBy('lft', 'asc');
        
        // Add filters
        CRUD::addFilter([
            'name'  => 'productable_type',
            'type'  => 'dropdown',
            'label' => 'Type de produit'
        ], [
            'App\Models\ProductRegular' => 'Produit régulier',
            'App\Models\ProductAccessory' => 'Accessoire',
            'App\Models\ProductClone' => 'Clone'
        ], function($value) {
            CRUD::addClause('where', 'productable_type', $value);
        });

        // Define columns
        CRUD::addColumns([
            [
                'name' => 'sku',
                'label' => 'SKU',
            ],
            [
                'name' => 'title_fr',
                'label' => 'Nom (FR)',
            ],
            [
                'name' => 'product_type_display',
                'label' => 'Type',
                'type' => 'closure',
                'function' => function($entry) {
                    switch($entry->productable_type) {
                        case 'App\Models\ProductRegular':
                            return 'Régulier';
                        case 'App\Models\ProductAccessory':
                            return 'Accessoire';
                        case 'App\Models\ProductClone':
                            return 'Clone';
                        default:
                            return 'Inconnu';
                    }
                }
            ],
            [
                'name' => 'producer',
                'label' => 'Producteur',
                'type' => 'relationship',
                'attribute' => 'title_fr'
            ],
            [
                'name' => 'price',
                'label' => 'Prix',
                'type' => 'number',
                'suffix' => ' $',
                'decimals' => 2
            ],
            [
                'name' => 'stock',
                'label' => 'Stock',
                'type' => 'number'
            ],
            [
                'name' => 'publish',
                'label' => 'Publié',
                'type' => 'boolean',
            ],
            [
                'name' => 'region',
                'label' => 'Région',
                'type' => 'text',
                'value' => 'ON'
            ]
        ]);

        CRUD::addButtonFromModelFunction('line', 'remove_featured', 'getRemoveFeaturedButton', 'beginning');
    }
    
    /**
     * Remove product from featured list
     */
    public function removeFeatured($id)
    {
        $product = ProductDetail::findOrFail($id);
        $product->featured = false;
        $product->save();
        
        Artisan::call('cache:clear');
        Alert::success('Produit ON retiré des produits vedettes avec succès')->flash();
        
        return redirect()->back();
    }
}
