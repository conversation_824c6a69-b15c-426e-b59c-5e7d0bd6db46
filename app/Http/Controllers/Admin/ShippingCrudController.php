<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Support\Facades\App;
use App\Http\Requests\ShippingRequest;
use Backpack\CRUD\app\Http\Controllers\CrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

/**
 * Class ShippingCrudController
 * @package App\Http\Controllers\Admin
 * @property-read \Backpack\CRUD\app\Library\CrudPanel\CrudPanel $crud
 */
class ShippingCrudController extends CrudController
{
    use \Backpack\CRUD\app\Http\Controllers\Operations\ListOperation;
    // use \Backpack\CRUD\app\Http\Controllers\Operations\CreateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\UpdateOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\DeleteOperation;
    use \Backpack\CRUD\app\Http\Controllers\Operations\ReorderOperation;

    /**
     * Configure the CrudPanel object. Apply settings to all operations.
     * 
     * @return void
     */
    public function setup()
    {
        CRUD::setModel(\App\Models\Shipping::class);
        CRUD::setRoute(config('backpack.base.route_prefix') . '/shipping');
        CRUD::setEntityNameStrings(__('teedy/orders.shippings.singular'), __('teedy/orders.shippings.plural'));
        $this->crud->set('reorder.label', 'title_' . App::getLocale());
        $this->crud->set('reorder.max_level', 1);
        $this->crud->orderBy('lft');

        if (!backpack_user()->hasRole(['admin', 'superadmin'])) {
            CRUD::denyAccess('list');
            CRUD::denyAccess('create');
            CRUD::denyAccess('update');
        }
    }

    /**
     * Define what happens when the List operation is loaded.
     * 
     * @see  https://backpackforlaravel.com/docs/crud-operation-list-entries
     * @return void
     */
    protected function setupListOperation()
    {
        CRUD::addColumns([
            [
                'name' => 'title_' . App::getLocale(),
                'label' => ucfirst(__('teedy/orders.shippings.labels.title_' . App::getLocale())),
                'type' => 'text',
            ],
            [
                'name'    => 'price',
                'label'   => ucfirst(__('teedy/orders.shippings.labels.price')),
                'type'    => 'text',
            ],
            [
                'name'    => 'type',
                'label'   => ucfirst(__('teedy/orders.shippings.labels.type')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.shippings.type_values'),
            ],
            [
                'name' => 'postal_codes',
                'label' => ucfirst(__('teedy/orders.shippings.labels.postal_codes')),
                'type' => 'text',
            ],
            // [
            //     'name' => 'province',
            //     'label' => ucfirst(__('teedy/orders.shippings.labels.province')),
            //     'type' => 'select_from_array',
            //     'options' => __('teedy/orders.shippings.province_values'),
            // ],
            [
                'name' => 'country',
                'label' => ucfirst(__('teedy/orders.shippings.labels.country')),
                'type' => 'select_from_array',
                'options' => __('teedy/orders.shippings.country_values'),
            ],
        ]);
    }

    /**
     * Define what happens when the Create operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-create
     * @return void
     */
    protected function setupCreateOperation()
    {
        CRUD::setValidation(ShippingRequest::class);

        CRUD::addFields([
            [
                'name' => 'title_fr',
                'label' => ucfirst(__('teedy/orders.shippings.labels.title_fr')),
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-controller col-md-6'
                ]
            ],
            [
                'name' => 'title_en',
                'label' => ucfirst(__('teedy/orders.shippings.labels.title_en')),
                'type' => 'text',
                'wrapper' => [
                    'class' => 'form-controller col-md-6'
                ]
            ],
            [
                'name'    => 'price',
                'label'   => ucfirst(__('teedy/orders.shippings.labels.price')),
                'type'    => 'text',
                'suffix' => '$',
                'wrapper' => [
                    'class' => 'form-controller col-md-6'
                ]
            ],
            [
                'name'    => 'type',
                'label'   => ucfirst(__('teedy/orders.shippings.labels.type')),
                'type'    => 'select_from_array',
                'options' => __('teedy/orders.shippings.type_values'),
                'allows_null' => false,
                'wrapper' => [
                    'class' => 'form-controller col-md-6'
                ]
            ],
            [
                'name' => 'country',
                'label' => ucfirst(__('teedy/orders.shippings.labels.country')),
                'type' => 'select_from_array',
                'allows_null' => false,
                'options' => __('teedy/orders.shippings.country_values'),
                'wrapper' => [
                    'class' => 'form-controller col-md-6'
                ]
            ],
            // [
            //     'name' => 'province',
            //     'label' => ucfirst(__('teedy/orders.shippings.labels.province')),
            //     'type' => 'select_from_array',
            //     'allows_null' => false,
            //     'options' => __('teedy/orders.shippings.province_values'),
            //     'wrapper' => [
            //         'class' => 'form-controller col-md-6'
            //     ]
            // ],
            [
                'name' => 'postal_codes',
                'label' => ucfirst("Code postaux"),
                'type' => 'textarea',
                'hint' => __('teedy/orders.shippings.hint_pc'),
                'wrapper' => [
                    'class' => 'form-controller col-md-12'
                ],
                'tab' => 'Postal Codes'
            ],
            [
                'name' => 'postal_codes_included',
                'label' => ucfirst("Code postaux Inclus"),
                'type' => 'repeatable',
                'fields' => [
                    [
                        'name' => 'code',
                        'type' => 'text',
                        'label' => 'Code postal',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'placeholder' => 'e.g. G7H1A1',
                            'pattern' => '^[A-Za-z][0-9][A-Za-z]([0-9][A-Za-z][0-9])?$',
                            'title' => 'Entrez un code postal de 6 caractères'
                        ]
                    ]
                ],
                'new_item_label' => 'Ajoutez un code postal',
                'tab' => 'Codes postaux inclus'
            ],
            [
                'name' => 'postal_codes_excluded',
                'label' => ucfirst("Code postaux Inclus"),
                'type' => 'repeatable',
                'fields' => [
                    [
                        'name' => 'code',
                        'type' => 'text',
                        'label' => 'Code postal',
                        'wrapper' => ['class' => 'form-group col-md-6'],
                        'attributes' => [
                            'placeholder' => 'e.g. G7H1A1',
                            'pattern' => '^[A-Za-z][0-9][A-Za-z][0-9][A-Za-z][0-9]$',
                            'title' => 'Entrez un code postal de 6 caractères'
                        ]
                    ]
                ],
                'new_item_label' => 'Ajoutez un code postal',
                'tab' => 'Code postaux exclus'
            ],
            [
                'name' => 'delivery_type',
                'label' => ucfirst(__('teedy/orders.shippings.labels.delivery_type')),
                'type' => 'select_from_array',
                'allows_null' => false,
                'options' => __('teedy/orders.shippings.delivery_type_values'),
                'wrapper' => [
                    'class' => 'form-controller col-md-4'
                ]
            ],
        ]);
    }

    /**
     * Define what happens when the Update operation is loaded.
     * 
     * @see https://backpackforlaravel.com/docs/crud-operation-update
     * @return void
     */
    protected function setupUpdateOperation()
    {
        $this->setupCreateOperation();
    }
}
