<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Models\PromoCode;
use Illuminate\Http\Request;
use App\Models\ProductDetail;
use Illuminate\Support\Carbon;

use App\Models\CartProductFrozen;
use App\Http\Controllers\Controller;

class CartApiController extends Controller
{
    /**
     * Return the user's current cart, or a newly created one
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request) {
        // Unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        $cart = $request->user()->cart()->with('items', 'promoCode')->firstOrCreate();

        // dd($cart->items->products);
        // $products = $cart->items;
        // foreach($products as $product) {
        //     $product->product->discounts = [];
        //     // dd($product->product->discounts);
        //     // Recalculate discounted price
        //     // $product->product->discounted_price = $product->product->getDiscountedPriceAttribute();
        // }
        // dd($cart->items);

        $cart = $this->formatCartPrices($cart);

        return response()->api(true, $cart);
    }

    private function formatCartPrices($cart) {
        $priceFields = [
            'total_price',
            'total_price_discounted',
            'promo_code_amount',
            'total_taxes',
            'shipping_cost',
            'final_price',
            'total_goods_value',
            'final_price_veteran',
            'total_price_discounted_veteran'
        ];
        
        $json = $cart->toJson();
        $data = json_decode($json);
        
        foreach ($priceFields as $field) {
            if (isset($data->$field) && is_numeric($data->$field)) {
                $data->$field = number_format((float)$data->$field, 2, '.', '');
            }
        }

        return $data;
    }

    /**
     * Check if all items in cart are valid, and return individual item errors if invalid
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function validateItems(Request $request) {
        // Fail if unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        if (empty($request->user()->cart) or !count($request->user()->cart->items))
            return response()->api(false, __('api.cart.empty'), 400);

        $cart_errors = $request->user()->cart->validateItems();
        return response()->api(!$cart_errors, $cart_errors);
    }

    /**
     * Update cart with new items/quantities
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateItems(Request $request) {
        // Fail if unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        // Fail if no items
        if (!$request->items)
            return response()->api(false, __('api.product.no-items'), 400);

        // Update cart
        $cart = $request->user()->cart()->firstOrCreate();

        foreach ($request->items as $item) {
            // Fetch product from ID
            $product = ProductDetail::where('id', $item['id'])->count();
            if (!$product)
                return response()->api(false, __('api.product.not-found', ['id' => $item['id']]));

            $qty = $item['qty'];
            if (!$qty or !is_numeric($qty) or $qty < 0) $qty = 0;

            $item = $cart->items()->updateOrCreate(['fk_product_id' => $item['id']]);
            $item->qty = $qty;
            if ($item['qty'] <= 0) $item->delete();
            else $item->save();
        }

        // Re-verify promo code if it exists in the cart
        if ($cart->fk_promo_code_id) {
            $promo = PromoCode::find($cart->fk_promo_code_id);
            if ($promo) {
                $validationResult = $cart->validatePromoCode($promo, $request->user());
                if (!$validationResult['status']) {
                    // Remove the promo code from the cart
                    $cart->fk_promo_code_id = null;
                    $cart->save();
                }
            }
        }

        // Delete cart if it's empty
        $cart_item = $cart->items()->get();
        if (count($cart_item) < 1) {
            // delete regular cart
            $cart->delete();

            // delete frozen cart
            $cartPrice = $request->user()->cartPrice()->first();
            if($cartPrice) {
                $cartPrice->delete();
            }
        }

        $cart = $this->formatCartPrices($cart);

        return response()->api(true, $cart);
    }

    /**
     * Update a cart with new shipping data
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function updateShipping(Request $request) {
        // Fail if unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        // Fail if no cart
        if (!$request->user()->cart)
            return response()->api(false, __('api.cart.not-found'), 404);

        // Validate shipping date/time
        if (!$request->user()->cart->validateShipping($request->date, $request->time))
            return response()->api(false, __('api.shipping.invalid', ['date' => $request->date, 'time' => $request->time]), 400);

        // Update cart shipping info
        $cart = $request->user()->cart;
        $cart->shipping_date = $request->date;
        $cart->shipping_time = $request->time;
        $cart->save();

        // Also update frozen cart
        $cartPrice = $request->user()->cartPrice()->first();
        if($cartPrice) {
            $cartPrice->shipping_date = $request->date;
            $cartPrice->shipping_time = $request->time;
            $cartPrice->save();
        }

        $response = $request->user()->cart()->with('items')->first();
        $response = $this->formatCartPrices($response);
        return response()->api(true, $response);
    }

    /**
     * Clear user cart
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function delete(Request $request) {
        // Fail if unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        // Delete cart if it exists
        $cart = $request->user()->cart()->first();
        if ($cart) $cart->delete();

        // Check if cart has been deleted
        $cart = $request->user()->cart()->first();
        return response()->api(!boolval($cart), $cart);
    }

    /**
     * Check for a valid promo code and return its details
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function verifyPromo(Request $request) {
        // Fail if unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        // Fail if no promo code
        if (!$request->code)
            return response()->api(false, __('api.promo.no-code'), 400);

        // Fetch promo code
        $promo = PromoCode::whereRaw('LOWER(code) = ?', [strtolower($request->code)])->first();

        // Fail if promo code not found
        if (!$promo) {
            return response()->api(false, ['message' => __('api.promo.not-found')]);
        }        

    
        $cart = $request->user()->cart;
        $validationResult = $cart->validatePromoCode($promo, $request->user());
    
        if (!$validationResult['status']) {
            return response()->api(false, ['message' => $validationResult['message']]);
        }

        //old code to disable % on % sales
        // $products = $cart->items;
        // foreach($products as $product) {
        //     if(!empty($product->product->active_discounts)){
        //         $activeDiscounts = $product->product->active_discounts;

        //         $originalPrice = $product->product->price;
        //         $discountedPrice = $product->product->discounted_price;
    
        //         // dd($originalPrice . "___", $discountedPrice);
        //         if (!empty($activeDiscounts)) {
        //             foreach ($activeDiscounts as $discount) {
        //                 if ($discount->type === 'percent' && $promo->type == 'percent') {
        //                     // return response()->api(false, ['message' => __('api.promo.cant-apply')]);
        //                 }
        //             }
        //         }
        //     }

        // }

    
        // Apply promo code to cart
        $cart->fk_promo_code_id = $promo->id;
        $cart->save();
    
        return response()->api(true, ['message' => __('api.promo.applied')]);
    }

    /**
     * Remove a promo code from the user's cart
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function removePromo(Request $request) {
        // Fail if unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'));

        // Fail if no promo code
        if (!$request->user()->cart->fk_promo_code_id)
            return response()->api(false, __('api.promo.no-code'));

        // Remove promo code from cart
        $cart = $request->user()->cart;
        $cart->fk_promo_code_id = null;
        $cart->save();

        return response()->api(true, ['message' => __('api.promo.removed')]);
    }

    public function freeze(Request $request) {

        $user = $request->user();

        // $cart = $request->user()->cart;
        // Fail if unauthenticated user
        // if (!$request->user())
        //     return response()->api(false, __('api.user.not-found'), 404);

        // Fail if no items
        // if (!$request->items)
        //     return response()->api(false, __('api.product.no-items'), 400);

        //delete existing frozen cart
        $cartPrice = $user->cartPrice()->first();
        if($cartPrice) {
            $cartPrice->delete();
        }

        // duplicate cart
        $cart = $user->cart()->firstOrCreate();
        $cartPrice = $user->cartPrice()->firstOrCreate();
        foreach ($cart->getAttributes() as $key => $value) {
            $cartPrice->$key = $value;
        }
        $cartPrice->save();
        

        foreach($cart->items as $item) {
            if($cartPrice->itemsFrozen()->where('fk_product_id', $item->fk_product_id)->count() > 0) {
                $cartProductFrozen = $cartPrice->itemsFrozen()->where('fk_product_id', $item->fk_product_id)->first();
                // dd($cartProductFrozen);
            }else{
                $cartProductFrozen = new CartProductFrozen();
            }
            // $cartProductFrozen = new CartProductFrozen();
            $cartProductFrozen->fk_cart_id = $cartPrice->id;
            $cartProductFrozen->fk_product_id = $item->fk_product_id;
            $cartProductFrozen->qty = $item->qty;

            $product = $item->product;
            $price = $product->discounted_price ?? $product->price;
            
            $cartProductFrozen->price = $price;
            $cartProductFrozen->save();
        }

        
        

        // Delete cart if it's empty
        // $cart_item = $cart->items()->get();
        // if (count($cart_item) < 1) {
        //     $cart->delete();
        // }

        return response()->api(true, $cart);
    }

    public function getTimeLeftFrozen(Request $request) {
        $cartPrice = $request->user()->cartPrice;
        return response()->api(true, $cartPrice->getTimeLeft());
    }


}