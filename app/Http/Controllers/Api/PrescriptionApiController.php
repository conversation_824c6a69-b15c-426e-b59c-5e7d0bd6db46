<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Mail\EmailNotification;
use App\Services\RecaptchaService;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;

class PrescriptionApiController extends Controller
{
    /**
     * Submit a user's prescription
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        if (!$request->file)
            return response()->api(false, __('api.prescription.invalid_file'));

        // Recaptcha Validation
        if (!empty(env('CAPTCHA_SECRET'))) {
            $captchaResponse = (new RecaptchaService())->verifyRecaptcha($request->recaptcha_token);
            if ($captchaResponse['code'] == 500) {
                return $captchaResponse;
            }
        }

        Log::info('Prescription photo value:' . $request->file);

        // Create prescription for user
        $prescription = $request->user()->prescriptions()->create(
            array_merge(
                $request->all(),
                ['status' => 'Pending'],
                ['prescription_photo' => $request->file]
            )
        );



        // Update user status
        if ($prescription) {
            //log prescription
            Log::info('Prescription created: ' . $prescription->prescription_photo);
            $request->user()->status = 'pending';
            $request->user()->save();
        }

        // Send mail 
        Mail::send(new EmailNotification(env('MAIL_FROM_ADDRESS_SUPPORT'),  __('mail.admin_confirm_prescription_demand.subject'), 'admin.medical.added_medical_prescription', ['id' => $request->user()->id]));        

        return response()->api(!!$prescription);
    }
}
