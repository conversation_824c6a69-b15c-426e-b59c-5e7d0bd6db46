<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\User;
use App\Models\Address;
use App\Models\UserDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use App\Mail\EmailNotification;
use App\Services\RecaptchaService;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class UserApiController extends Controller
{
    /**
     * Register user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstname' => [
                'required',
                'max:255',
                'regex:/^[a-zA-ZÀ-ÿ\-\'\s]+$/' // Allow letters, accents, hyphens, apostrophes and spaces
            ],
            'lastname' => [
                'required',
                'max:255',
                'regex:/^[a-zA-ZÀ-ÿ\-\'\s]+$/' // Allow letters, accents, hyphens, apostrophes and spaces
            ],
            'birth_date'     => 'required|date',
            'email'          => 'required|unique:users,email|email',
            'phone'          => 'required|numeric',
            'address'        => 'required',
            'city'           => 'required',
            'pc'             => 'required|regex:/^[A-Za-z]\d[A-Za-z][ -]?\d[A-Za-z]\d$/',
            'province'       => 'required|regex:/^[A-Z]{2}$/',
            'password'       => 'required|min:8',
            'language'       => 'required|in:fr,en',
        ]);

        // Incorrect information
        if ($validator->fails())
            return response()->api(false, $validator->messages()->all());

        try {

            // Recaptcha Validation
            if (!empty(env('CAPTCHA_SECRET'))) {
                $captchaResponse = (new RecaptchaService())->verifyRecaptcha($request->recaptcha_token);
                if ($captchaResponse['code'] == 500) {
                    return $captchaResponse;
                }
            }

            $user = new User($request->all());
            $userdetail = new UserDetail($request->all());
            $address =  new Address($request->all());

            // add default to shipname
            $address->shipname = $request->firstname . ' ' . $request->lastname;

            if (!empty($user) && !empty($userdetail) && !empty($address)) {
                $user->password = Hash::make($request->password);
                $user->save();
                $user->assignRole('client');
                
                // Set userdetail
                $userdetail->quota_user_remaining = 0;
                $userdetail->language = App::getLocale();
                $user->userdetail()->save($userdetail);
                $user->addresses()->save($address);

                // Sync to bluelink
                if(env('SYNC_BLUELINK')){
                    $user->BLWeb_CreateCustomer();

                    $user->addresses->each(function($address) use ($user){

                        $address->shipcode = 'main';
                        //kept in case we have to go back to random shipcode for bluelink
                        // $shipcode = $user->userdetail->teedy_client_id . '-' . substr(str_shuffle(str_repeat($x = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ', ceil(10 / strlen($x)))), 1, 10);
                        // $shipcode = substr($shipcode, 0, 10);
                        // $address->shipcode = $shipcode;
                        $address->save();

                        $address->BLWeb_CreateAddress();
                    });
                }
                
                // Send notification for user / admin
                $user->notifyAccountCreated();

                // Return token
                return response()->api(true, __('api.user.registered'), 201, ['token' => $user->createToken($request->email)->plainTextToken]);
            }
        }

        catch (\Exception $e) {
            // delete user if failed
            if (isset($user)) {
                $user->delete();
            }

            return response()->api(false, __('api.user.register-failed'), 400, ['error' => $e->getMessage()]);
        }
    }

    /**
     * Return user data as json
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function show(Request $request)
    {
        // Unauthenticated user
        if (!$request->user())
            return response()->api(false, __('api.user.not-found'), 404);

        $user = User::with('addresses', 'prescriptions')->find($request->user()->id);

        // User not found
        if (!$user)
            return response()->api(false, __('api.user.not-found'), 404);

        // User inactive
        if ($user->status == 'disabled')
            return response()->api(false, __('api.user.inactive'), 400);

        // Merge userdetail fields into main model to simplify API data
        $user = $this->mergeUserDetails($user);

        return response()->api(true, $user);
    }

    /**
     * Logout current user
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        $request->user()->tokens()->delete();
        return response()->api(true, __('auth.logout'));
    }

    /**
     * Log the user and return the bearer token
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {
        $request->validate(['email' => 'required|email', 'password' => 'required']);
        $user = User::where('email', $request->email)->first();

        // Recaptcha Validation
        if (!empty(env('CAPTCHA_SECRET'))) {
            $captchaResponse = (new RecaptchaService())->verifyRecaptcha($request->recaptcha_token);
            if ($captchaResponse['code'] == 500) {
                return $captchaResponse;
            }
        }

        // User not found
        if (!$user)
            return response()->api(false, __('auth.failed'));

        // Wrong password
        elseif (!Hash::check($request->password, $user->password))
            return response()->api(false, __('auth.password'));

        // Wrong role
        elseif (!$user->hasRole('client'))
            return response()->api(false, __('auth.roles'));

        // Inactive user
        elseif ($user->status == 'disabled')
            return response()->api(false, __('api.user.inactive'));

        // Login success
        $token = $user->createToken($request->email)->plainTextToken;
        $user->forceFill(['api_token' => $token])->save();

        return response()->api(true, __('auth.success'), 200, ['token' => $token]);
    }

    /**
     * Returns user token validity
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function checkToken(Request $request)
    {
        $user = User::where('api_token', $request->bearerToken())->first();
        $time_limit = Carbon::now()->subHour(env('TOKEN_LIFETIME', 1));

        return (!$user || $user->updated_at < $time_limit)
            ? response()->api(false, __('auth.token_expired'))
            : response()->api(true, __('auth.token_valid'));
    }

    /**
     * Change user language in DB
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function changeLanguage(Request $request)
    {
        // Fetch user and abort if null
        $user = User::with('userdetail')->find($request->user()->id);
        if (!$user) return response()->api(false, __('api.user.not-found'), 401);

        // Update language only if valid AND different from current language
        $lang = $request->lang;
        if ($lang != $user->userdetail->language && in_array($lang, ['fr', 'en'])) {
            $user->userdetail->language = $lang;
            $user->userdetail->save();
            return response()->api(true, __('api.user.language-changed'), 200, ['lang' => $user->userdetail->language]);
        }

        // Default response if invalid or nothing changed
        return response()->api(true, __('api.user.language-unchanged'), 200, ['lang' => false]);
    }

    /**
     * Make user account inactive
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function deleteAccount(Request $request)
    {
        $user = User::find($request->user()->id);

        // Invalid user
        if (empty($user))
            return response()->api(false, __('api.user.not-found'));

        // Inactive user
        if ($user->status == 'disabled')
            return response()->api(false, __('api.user.inactive'));

        $user->status = 'disabled';
        $user->archived = 1;
        $user->save();

        // Send mail 
        Mail::send(new EmailNotification($user->email, __('mail.delete_account.subject'), 'customer.delete_account', $user));

        return response()->api(true, __('api.user.deactivated'));
    }

    /**
     * Simplify user json model
     *
     * @param  \App\Models\User $user
     * @return \App\Models\User
     */
    private function mergeUserDetails($user)
    {
        // Transfer every column from userdetail relation to root model
        $ignore = ['id', 'fk_user_id', 'created_at', 'updated_at', 'symptoms', 'symptoms_other', 'consumption', 'consumption_other'];
        $columns = array_keys($user->userdetail->getAttributes());
        foreach (array_diff($columns, $ignore) as $column) {
            $user[$column] = $user->userdetail[$column];
        }

        // Hide obsolete userdetail field
        $user->makeHidden('userdetail');

        return $user;
    }

    /**
     * Returns the available Shipping schedule for the user
     *
     * @param  \App\Models\User $user
     * @return \App\Models\User
     */
    public function getShipping (Request $request) {
        $user = User::find($request->user()->id);

        // Invalid user
        if (empty($user))
            return response()->api(false, __('api.user.not-found'));

        // Inactive user
        if ($user->status == 'disabled')
            return response()->api(false, __('api.user.inactive'));

        $schedule = $user->shippingSchedule();
        
        try {
            return $schedule
                ? response()->api(true, $schedule)
                : response()->api(false, __('api.shipping.unsupported-zipcode', ['zipcode' => $user->shipping_address->pc]), 200);
        } catch (\Exception $e) {
            return response()->api(false, $e->getMessage(), 400);
        }
    }

    /**
     * Return user orders
     *
     * @param  \App\Models\User $user
     * @return \App\Models\User
     */
    public function getOrders (Request $request) {
        $user = User::find($request->user()->id);
        if (!$user) return response()->api(false, __('api.user.not-found'), 401);

        $orders = $user->orders();

        $id = $request->order_id;
        if ($id) $orders = $orders->where('id', $id);

        $ordersCollection = $orders->orderBy('created_at')->get();
        $formattedOrders = $ordersCollection->map(function($order) {
            $orderContent = json_decode($order->order_content, true);
            
            if ($orderContent) {
                $priceFields = [
                    'price_before_taxes',
                    'cart_discount',
                    'shipping_cost',
                    'total_taxes',
                    'final_order_price',
                    'cart_discount_percentage'
                ];
                
                foreach ($priceFields as $field) {
                    if (isset($orderContent[$field]) && is_numeric($orderContent[$field])) {
                        $orderContent[$field] = number_format((float)$orderContent[$field], 2, '.', '');
                    }
                }
            
                if (isset($orderContent['product_list']) && is_array($orderContent['product_list'])) {
                    foreach ($orderContent['product_list'] as &$product) {
                        $productPriceFields = [
                            'product_price',
                            'base_price',
                            'discount',
                            'total',
                            'base_total'
                        ];
                        foreach ($productPriceFields as $field) {
                            if (isset($product[$field]) && is_numeric($product[$field])) {
                                $product[$field] = number_format((float)$product[$field], 2, '.', '');
                            }
                        }
                    }
                }
                $order->order_content = json_encode($orderContent);
            }
            
            return $order;
        });

        return response()->api(true, $formattedOrders);
    }

    /**
     * Function used when user request a password reset
     *
     * @param \Illuminate\Http\Request $request
     * @return string json response
     */
    public function resetPassword(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required',
        ]);
        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->messages()->all()], 200);
        }

        $actualUser = User::where('email', $request->email)->first();
        if (empty($actualUser)) {
            return response()->json(['status' => false, 'message' => [__('api.password.user_not_found')]], 200);
        }

        // create new validation token
        $actualUser->remember_token = md5(uniqid());
        $actualUser->save();

        // Data for mail template
        $data = $actualUser->toArray();
        $data['link_reset'] = env('FRONT_URL_RESET_PASSWORD_' . strtoupper(\App::getLocale())) . '/' . $actualUser->remember_token;

        // send mail
        Mail::send(new EmailNotification($actualUser->email, __('mail.password.subject'), 'customer.reset_password', $data));

        return response()->json(['status' => true, 'message' => [__('api.password.mail_send')]], 200);
    }

    /**
     * Function used when user submit form with their new password
     *
     * @param \Illuminate\Http\Request $request
     * @return string json response
     */
    public function changePassword(Request $request)
    {
        // If a token, it's a call for reset password by mail
        if ($request->token) {
            $validator = Validator::make($request->all(), [
                'new_pass' => 'required',
            ]);
            if ($validator->fails()) {
                return response()->json(['status' => false, 'message' => $validator->messages()->all()], 200);
            }
            $actualUser = User::where('remember_token', $request->token)->first();
            if (empty($actualUser)) {
                return response()->json(['status' => false, 'message' => [__('api.password.token_not_found')]], 200);
            }
            
        }
        
        // else, it's a call for change password (seems to be not used in front)
        //  else {
        //     $validator = Validator::make($request->all(), [
        //         'old_pass' => 'required',
        //         'new_pass' => 'required',
        //     ]);
        //     if ($validator->fails()) {
        //         return response()->json(['status' => false, 'message' => $validator->messages()->all()], 200);
        //     }
        //     $actualUser = User::find(Auth::user()->id);
        //     // if not correct old pass for actual user
        //     if (!\Hash::check($request->old_pass, $actualUser->password)) {
        //         return response()->json(['status' => false, 'message' => [__('api.password.old_pass_uncorrect')]], 200);
        //     }
        // }

        // save new password
        $actualUser->password = bcrypt($request->new_pass);

        // Reset remember token after password change
        $actualUser->remember_token = null;
        
        $actualUser = User::withoutEvents(function () use ($actualUser) {
            $actualUser->save();
        });

        return response()->json(['status' => true, 'message' => [__('api.password.password_changed')]], 200);
    }

    public function unsubscribeExpiry($token)
    {
        try {
            $data = decrypt($token);
            
            // Validate token data
            if (!isset($data['user_id']) || !isset($data['type']) || $data['type'] !== 'expiry_unsubscribe') {
                throw new Exception('Invalid token data');
            }
            
            // Get user first to determine language
            $user = User::findOrFail($data['user_id']);
            $language = $user->userdetail->language ?? 'fr';
            
            // Check token expiry
            if (isset($data['expires_at']) && now()->timestamp > $data['expires_at']) {
                $errorUrl = $language === 'en' 
                    ? env('FRONT_URL_EN', 'https://teedy.com/en') . '/unsubscribe?error=expired'
                    : env('FRONT_URL_FR', 'https://teedy.com/fr') . '/desabonnement?error=expired';
                return redirect()->to($errorUrl);
            }
            
            $user->userdetail->update([
                'mail_notifications' => false,
            ]);

            // Redirect to success page based on user's language
            $successUrl = $language === 'en' 
                ? env('FRONT_URL_EN', 'https://teedy.com/en') . '/unsubscribe?success=true'
                : env('FRONT_URL_FR', 'https://teedy.com/fr') . '/desabonnement?success=true';

            return redirect()->to($successUrl);
            
        } catch (\Exception $e) {
            // Default to French error page
            return redirect()->to(env('FRONT_URL_FR', 'https://teedy.com/fr') . '/desabonnement?error=invalid');
        }
    }
}
