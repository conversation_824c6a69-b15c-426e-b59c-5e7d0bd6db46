<?php

namespace App\Http\Controllers\api;

use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\App;
use App\Mail\EmailNotification;
use Spatie\Newsletter\Facades\Newsletter;
use App\Services\RecaptchaService;

class ContactApiController extends Controller
{
    public function supportContact(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'firstname' => 'required',
            'lastname'  => 'required',
            'email'     => 'required',
            'subject'   => 'required',
            'message'   => 'required'
        ]);
        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->messages()->all()], 200);
        }

        // Recaptcha Validation
        if (!empty(env('CAPTCHA_SECRET'))) {
            $captchaResponse = (new RecaptchaService())->verifyRecaptcha($request->recaptcha_token);
            if ($captchaResponse['code'] == 500) {
                return $captchaResponse;
            }
        }

        // $request->request->remove('token');
        // $request->request->remove('language');

        // Mail for admin
        Mail::send(new EmailNotification(env('MAIL_FROM_ADDRESS_SUPPORT'), __('mail.support.subject_mail'), 'admin.support_notifications', $request->all()));
        // Mail for user
        Mail::send(new EmailNotification($request->email, __('mail.support.subject_mail'), 'customer.confirm_support_notifications', $request->all()));

        return response()->json(['status' => true, 'message' => [__('api.support_demand.done')]], 200);
    }

    public function newsletter(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
        ]);

        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->messages()->all()], 200);
        }

        // Recaptcha Validation
        if (!empty(env('CAPTCHA_SECRET'))) {
            $captchaResponse = (new RecaptchaService())->verifyRecaptcha($request->recaptcha_token);
            if ($captchaResponse['code'] == 500) {
                return $captchaResponse;
            }
        }

        $email = $request->email;
        $user = User::where('email', $email)->first();
        $groups = [];
        if (env('NEWSLETTER_LIST_INTEREST_ID_1') && env('NEWSLETTER_LIST_INTEREST_ID_2')) {
            $groups['interests'] = [
                env('NEWSLETTER_LIST_INTEREST_ID_1') => (App::getLocale() == 'fr') ? true : false,
                env('NEWSLETTER_LIST_INTEREST_ID_2') => (App::getLocale() == 'fr') ? false : true
            ];
        }

        if (!empty($user)) {
            Newsletter::subscribeOrUpdate($email, ['FNAME' => $user->firstname, 'LNAME' => $user->lastname], 'subscribers', $groups);
        } else {
            Newsletter::subscribeOrUpdate($email, [], 'subscribers', $groups);
        }
        if (Newsletter::getLastError()) {
            $error = Newsletter::getLastError();
            return response()->json(['status' => true, 'message' => [$error]], 200);
        }
        return response()->json(['status' => true, 'message' => [__('api.general.newsletter_status_on')]], 200);
    }
}
