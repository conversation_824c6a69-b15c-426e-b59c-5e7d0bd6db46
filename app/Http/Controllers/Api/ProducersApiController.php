<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Producer;
use App\Models\ProductDetail;
use Illuminate\Http\Request;

class ProducersApiController extends Controller
{
    public function index(Request $request) {
        $producerCounts = ProductDetail::where('publish', 1)
            ->select('fk_producer_id', \DB::raw('COUNT(*) as product_count'))
            ->groupBy('fk_producer_id')
            ->orderBy('product_count', 'desc')
            ->pluck('product_count', 'fk_producer_id')
            ->toArray();

        $producers = Producer::where('publish', 1)->get();
        
        $sortedProducers = $producers->sortByDesc(function($producer) use ($producerCounts) {
            return $producerCounts[$producer->id] ?? 0;
        })->values();
        
        return response()->api(true, $sortedProducers);
    }

    public function show($slug) {
        $page = Producer::where(fn ($q) => $q->where('slug_fr', $slug)->orWhere('slug_en', $slug))->first();
        return response()->api(!!$page, $page);
    }
}
