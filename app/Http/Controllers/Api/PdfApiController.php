<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\User;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;


class PdfApiController extends Controller
{
    public function generatePdf(Request $request)
    {

        $data = Order::where('moneris_id', $request->id)->first();

        if (!$data) {
            return response()->json(['error' => 'Order not found'], 404);
        }
        $data->order_content = json_decode($data->order_content);
        $data->client_info = json_decode($data->client_info);
        if (!empty($data->user->userdetail->phone)) {
            $data->phone_formatted = $this->formatPhoneNumber($data->user->userdetail->phone);
        }
        $orderDate = Carbon::parse($data->created_at);
        $data->prescription = $data->user->prescriptions
            ->filter(function ($prescription) use ($orderDate) {
                $startDate = Carbon::parse($prescription->start_date);
                $endDate = $prescription->end_date ? Carbon::parse($prescription->end_date) : null;

                return $startDate->lte($orderDate) && ($endDate === null || $endDate->gte($orderDate));
            })->first();
        // Fallback: if no valid prescription found for the order date, get the closest one
        if (!$data->prescription) {
            $data->prescription = $data->user->prescriptions
                ->sortBy(function ($prescription) use ($orderDate) {
                    return abs(Carbon::parse($prescription->start_date)->diffInDays($orderDate));
                })
                ->first();
        }
        $pdf = Pdf::loadView('pdf', [
            'data' => $data,
            'subject' => __('mail.order.subject')
        ]);
    
        $canvas = $pdf->getDomPDF()->getCanvas();
        $font = $pdf->getDomPDF()->getFontMetrics()->getFont('Inter', 'normal');

        $fontSize = 8;
        $pdf->render(); 
        $canvas->page_text(541, 90, __('mail.order.pagination', [
            'current' => '{PAGE_NUM}',
            'total' => '{PAGE_COUNT}',
        ]), $font, $fontSize, [0, 0, 0]);
       
        return $pdf->download(__('mail.order.filename') . '.pdf');


    }
    
    private function formatPhoneNumber(string $number): string
    {
        $digits = preg_replace('/\D/', '', $number);

        if (strlen($digits) === 10) {
            return sprintf(
                '(%s) %s-%s',
                substr($digits, 0, 3),
                substr($digits, 3, 3),
                substr($digits, 6, 4)
            );
        }

        return $number;
    }
}
