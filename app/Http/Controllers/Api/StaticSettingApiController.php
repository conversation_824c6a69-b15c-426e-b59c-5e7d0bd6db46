<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\StaticSetting;

class StaticSettingApiController extends Controller
{
    // show specific global content based on type passed by URL
    public function show($identifiant)
    {
        $content = StaticSetting::where('identifiant', $identifiant)->first();

        if (!$content) {
            return response()->api(false, null);
        }
        
        // Get localized data
        $localizedData = $content->getLocalizedData();
        
        return response()->api(true, $localizedData);
    }
}
