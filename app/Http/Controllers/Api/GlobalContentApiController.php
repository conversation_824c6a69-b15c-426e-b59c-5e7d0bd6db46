<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\GlobalContent;

class GlobalContentApiController extends Controller
{
    // show specific global content based on type passed by URL
    public function show($type)
    {
        $content = GlobalContent::where('type', $type)->first();
        return response()->api(!!$content, $content->data_formatted);
    }
}
