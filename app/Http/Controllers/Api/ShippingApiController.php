<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Shipping;
use App\Models\Holiday;
use Carbon\Carbon;

class ShippingApiController extends Controller
{
    // Return all shipping information
    public function index() {
        $shipping = Shipping::all();
        return response()->api(true, $shipping);
    }

    // Return list of dates with no delivery possible
    public function holidays() {
        $holidays = Holiday::where('date', '>=', Carbon::now()->startOfDay())->where('active', true)->get();
        return response()->api(true, $holidays);
    }
}
