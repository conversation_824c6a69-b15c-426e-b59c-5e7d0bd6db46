<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\CustomPage;

class CustomPageApiController extends Controller
{
    // Return all pages, or all from category if specified
    public function index($category) {
        $pages = CustomPage::where('publish', 1)
            ->where('category', $category)
            ->orderByRaw('show_on_top DESC, created_at DESC') // Conditional ordering
            ->get();

        // Hide heavy unused attributes
        $pages->makeHidden(['content', 'image', 'meta_title', 'meta_description']);

        return response()->api(true, $pages);
    }

    // Return a page by slug
    // TODO: Figure lout why <PERSON><PERSON> ignores $hidden fields in CustomPage queries
    public function show($slug) {
        $page = CustomPage::where(fn ($q) => $q->where('slug_fr', $slug)->orWhere('slug_en', $slug))->first();
        return response()->api(!!$page, $page);
    }

    // Return
    public function translate($current_slug) {
        $page = CustomPage::where(fn ($q) => $q->where('slug_fr', $current_slug)->orWhere('slug_en', $current_slug))->first();
        $slug = $current_slug == $page->slug_fr ? $page->slug_en : $page->slug_fr;
        return response()->api(!!$page, $slug);
    }
}
