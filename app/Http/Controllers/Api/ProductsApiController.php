<?php

namespace App\Http\Controllers\Api;

use App\Models\ProductClone;
use Illuminate\Http\Request;
use App\Models\ProductDetail;
use App\Models\ProductRegular;
use App\Mail\EmailNotification;
use App\Models\ProductAccessory;
use Prologue\Alerts\Facades\Alert;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Redirect;
use App\Jobs\WarmProductCache;
use App\Services\ProductCacheService;


class ProductsApiController extends Controller
{
    // List all products as json array
    public function index(Request $request) {

        // Set cache key
        $region = strtoupper($request->region);
        $lang = strtoupper($request->lang);
        $cacheKey = "products_{$region}_{$lang}";
    
        if (Cache::has($cacheKey) && !$request->is_cache_warming) {
            $products = Cache::get($cacheKey);
            Log::channel('cache')->info('Cache hit', ['key' => $cacheKey]);
        } else {
            Log::channel('cache')->info('Cache miss', ['key' => $cacheKey]);
            
            // Eager load relationships
            $query = ProductDetail::asApiModel()
                ->with(['productable', 'productable.category'])
                ->where('publish', 1)
                ->where('region', $region)
                ->where('bluelink_synced', 1)
                ->orderBy('created_at', 'desc');
    
            if ($only_ids = array_filter(explode(',', $request->only), fn ($e) => !!$e)) {
                $query->whereIn('id', $only_ids);
            }
    
            $productsCollection = $query->get();
            
            // Define a hardcoded order for categories
            $categoryOrder = [
                5,  // Cartouches
                8,  // Topiques
                4,  // Concentrés
                2,  // Huiles
                3,  // Comestibles
                1,  // Cannabis séché
                7,  // Gélules
                6,  // Pré-roulés
                9,  // Accessoires
            ];
            
            // Transform and sort the collection by the hardcoded category order
            $products = $productsCollection
                ->map(function($product) {
                    return $this->mergeProductableModel($product);
                })
                ->sortBy(function($product) use ($categoryOrder) {
                    $categoryId = $product['category']->id ?? 999;
                    $position = array_search($categoryId, $categoryOrder);
                    return $position !== false ? $position : 999;
                })
                ->values(); // Reset array keys
    
            Cache::put($cacheKey, $products, now()->addMinutes(10));
            
            // Store cache metadata for TTL tracking
            Cache::put($cacheKey . '_meta', [
                'cached_at' => now()->toISOString(),
                'region' => $region,
                'lang' => $lang
            ], now()->addMinutes(10));
        }
        

        // Force every product to use 2 digits (seems a bit odd to put here tho)
        $products = collect($products)->map(function($product) {
            if (isset($product['price'])) {
                $product['price'] = number_format((float)$product['price'], 2, '.', '');
            }
            if (isset($product['discounted_price']) && $product['discounted_price'] !== null) {
                $product['discounted_price'] = number_format((float)$product['discounted_price'], 2, '.', '');
            }
            return $product;
        })->all();
    
        return response()->api(true, $products);
    }

    // List single product as json object
    public function show($id) {
        // Fail if missing param
        if (!$id or !is_numeric($id))
            return response()->api(false, __('api.product.invalid-id'), 400);

        // Get product or fail
        $product = ProductDetail::asApiModel()->find($id);
        if (!$product)
            return response()->api(false, __('api.product.not-found'), 404);

        if(!$product->bluelink_synced){
            return response()->api(false, __('api.product.not-found'), 404);
        }
        // Simplify JSON response
        $product = $this->mergeProductableModel($product);

        return response()->api(true, $product);
    }

    // List single product as json object by slug (not used just in case)
    public function showBySlug($slug) {
        // Fail if missing param
        if (!$slug)
            return response()->api(false, __('api.product.invalid-slug'), 400);

        // Get product or fail
        $product = ProductDetail::asApiModel()->where('slug_fr', $slug)->first();
        if (!$product)
            return response()->api(false, __('api.product.not-found'), 404);

        if(!$product->bluelink_synced){
            return response()->api(false, __('api.product.not-found'), 404);
        }

        // Simplify JSON response
        $product = $this->mergeProductableModel($product);

        return response()->api(true, $product);
    }

    // Add or update a product evaluation
    public function evaluate(Request $request, $id) {
        // Fail if missing param
        if (!$id or !is_numeric($id))
            return response()->api(false, __('api.product.invalid-id'), 400);

        // Filter out invalid evaluation values
        ['value' => $value] = $request->all();
        if (empty($value) or $value < 1 or $value > config('constants.product.evaluation_max'))
            return response()->api(false, __('api.product.invalid-evaluation'), 400);

        // Check if user already evaluated this product
        if ($request->user()->evaluations()->where('fk_user_id', $request->user()->id)->where('fk_product_detail_id', $id)->exists())
            return response()->api(false, __('api.product.already-evaluated'), 202);

        // Add evaluation or update if existing
        $eval = $request->user()->evaluations()->updateOrCreate(
            ['fk_user_id' => $request->user()->id, 'fk_product_detail_id' => $id],
            ['value' => $value]
        );
        Mail::send(new EmailNotification(env('MAIL_FROM_ADDRESS_SUPPORT'), __('mail.evaluate.subject'), 'customer.evaluate', ['evaluation' => $eval]));
        
        // Invalidate products cache
        Log::channel('cache')->info('Invalidating product cache after evaluation', ['product_id' => $id]);
        ProductCacheService::refreshCache();

        // Return the new evaluation score
        return response()->api(true, ProductDetail::find($id)->evaluation, 200);
    }


    /*
    |--------------------------------------------------------------------------
    | HELPERS
    |--------------------------------------------------------------------------
    */

    // Merge type-specific fields into each model's root to simplify json response
    private function mergeProductableModel($product)
    {
        // Convert to array once
        $productArray = $product->toArray();
        $productableArray = $product->productable->toArray();
        
        // Define columns to ignore
        $ignoreColumns = ['id', 'fk_category_id', 'created_at', 'updated_at'];
        
        // Merge filtered productable attributes
        $productArray = array_merge(
            $productArray,
            array_diff_key(
                $productableArray,
                array_flip($ignoreColumns)
            )
        );
        
        // Add category
        $productArray['category'] = $product->productable->category;
        
        return $productArray;
    }
}
