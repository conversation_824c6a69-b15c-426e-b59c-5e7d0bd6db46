<?php

namespace App\Http\Controllers\Api;

use Exception;
use App\Models\Taxe;
use App\Models\User;
use App\Models\Order;
use App\Models\ApiLog;
use App\Models\Shipping;
use Illuminate\Http\Request;
use App\Models\ProductDetail;
use Illuminate\Support\Carbon;
use App\Mail\EmailNotification;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Mail;


class OrdersApiController extends Controller
{
    public function store(Request $request)
    {

        Log::info('Order request: ' . json_encode($request->all()));
        $user  = Auth::user();

        
        if(!$user->cartPrice) {
            return response()->api(false, __('api.cart.expired'), 400);
        }

        $promo = $user->cartPrice?->promo_code;

        $additionalOrderData = [
            'product_list' => [],
            'quota_weight' => $user->cartPrice->totalWeight,
            'price_before_taxes' => $user->cartPrice->total_price_discounted, // total price with global promo applied
            'cart_discount' => $user->cartPrice->promo_code_amount,
            'shipping_cost' => $user->cartPrice->shipping_cost,
            'taxes' => $user->cartPrice->taxes_monetary_value,
            'total_taxes' => $user->cartPrice->total_taxes,
            'final_order_price' => $user->cartPrice->final_price,
            'delivery_date' => $user->cartPrice->shipping_date,
            'delivery_time' => $user->cartPrice->shipping_time,
            'cart_discount_type' => $user->cartPrice->promo_code?->type ?? '',
            'cart_discount_name' => $user->cartPrice->promo_code?->code ?? '',
            'cart_discount_percentage' => Order::discountToPercentage(
                $user->cartPrice->total_price_discounted, 
                ($user->cartPrice->total_price_discounted - $user->cartPrice->promo_code_amount)
            ), // Only here for BL order discount
            'client_info' => [
                'name' => $user->fullname, 
                'email' => $user->email,
                'veteran_id' => $user->userdetail->veteran_id ?? '',
            ],
        ];

        if ($user->userdetail->veteran) {
            $additionalOrderData['final_order_price_veteran'] = $user->cartPrice->final_price_veteran;
        }
        
        // Add order items from user frozen cart
        foreach ($user->cartPrice->itemsFrozen as $item) {
            $additionalOrderData['product_list'][] = $this->mapCartItemToOrderItem($item);
        }

        // dd($additionalOrderData['product_list']);

        // Set client address
        $client_address = [
            'address' => $user->shipping_address->address,
            'city' => $user->shipping_address->city,
            'postal_code' => $user->shipping_address->pc,
            'province' => $user->shipping_address->province,
        ];
        $client_info =  [
            'billing_address' => $client_address,
            'shipping_address' => $client_address,
        ];

        // Validate request data
        // TODO: Use external request validation
        $request->merge($additionalOrderData);
        $validatedData = $request->validate([
            'product_list' => 'required|array',
            'product_list.*.id' => 'required|integer',
            'product_list.*.name' => 'required|string',
            'product_list.*.product_title_fr' => 'required|string',
            'product_list.*.product_title_en' => 'required|string',
            'product_list.*.sku' => 'required|string',
            'product_list.*.quantity' => 'required|integer|min:1',
            'product_list.*.type' => 'nullable|string',
            'product_list.*.format' => 'nullable|numeric',
            // 'product_list.*.unity' => 'required|string',
            'product_list.*.product_description_bl' => 'required|string',
            'product_list.*.uom' => 'required|string',
            'product_list.*.product_price' => 'required|numeric',
            'product_list.*.base_price' => 'required|numeric',
            'product_list.*.discount' => 'required|numeric',
            'product_list.*.total' => 'required|numeric',
            'product_list.*.base_total' => 'required|numeric',
            'price_before_taxes' => 'required|numeric',
            'cart_discount' => 'required|numeric',
            'total_taxes' => 'required|numeric',
            'quota_weight' => 'required|numeric',
            'taxes' => 'required|array',
            'shipping_cost' => 'required|numeric',
            'final_order_price' => 'required|numeric',
            'delivery_date' => 'nullable',
            'delivery_time' => 'nullable',
            'client_info' => 'required|array',
            'cart_discount_type' => 'nullable|string',
            'cart_discount_name' => 'nullable|string',
            'cart_discount_percentage' => 'required|numeric',
            'final_order_price_veteran' => 'nullable|numeric',
        ]);

        // ===== NEW STOCK VALIDATION SECTION =====
        // Check stock availability before processing payment
        $stockErrors = [];
        $insufficientStockProducts = [];

        foreach ($validatedData['product_list'] as $product) {
            $productId = $product['id'];
            $requestedQuantity = $product['quantity'];
            
            // Get current stock from database
            $currentProduct = ProductDetail::find($productId);
            
            if (!$currentProduct) {
                $stockErrors[] = "Product '{$product['name']}' (ID: {$productId}) no longer exists";
                continue;
            }

            // Check if product is still published
            if (!$currentProduct->publish) {
                $stockErrors[] = "Product '{$product['name']}' is no longer available";
                continue;
            }

            // Check stock availability
            if ($currentProduct->stock < $requestedQuantity) {
                $insufficientStockProducts[] = [
                    'name' => $product['name'],
                    'sku' => $product['sku'],
                    'requested' => $requestedQuantity,
                    'available' => $currentProduct->stock
                ];
                
                if ($currentProduct->stock <= 0) {
                    $stockErrors[] = "Product '{$product['name']}' is out of stock";
                } else {
                    $stockErrors[] = "Product '{$product['name']}' has insufficient stock. Requested: {$requestedQuantity}, Available: {$currentProduct->stock}";
                }
            }
        }

        // If there are stock issues, return error before processing payment
        if (!empty($stockErrors)) {
            Log::warning('Order cancelled due to stock issues', [
                'user_id' => $user->id,
                'order_id' => 'not_created',
                'stock_errors' => $stockErrors,
                'insufficient_products' => $insufficientStockProducts
            ]);

            // Clear the user's cart since it contains invalid items
            $user->cartPrice->delete();

            return response()->api(false,__('teedy/orders.stock_unavailable'), 201);


        }

        Log::info('Stock validation passed, proceeding with payment processing', [
            'user_id' => $user->id,
            'products_count' => count($validatedData['product_list'])
        ]);
        // ===== END STOCK VALIDATION SECTION =====

        // Get payment token from frontend
        $token = $request->token;

        Log::info('token used: ' . $token);
        // Log::info(print_r($request->all(), true));

        //new request object test

        ## MONERIS ##
        ## This program takes 3 arguments from the command line:
        ## 1. Store id
        ## 2. api token
        ## 3. order id ##
        ## Example php -q TestResPurchaseCC.php store3 yesguy unique_order_id 1.00
        ##
        /************************ Request Variables **********************************/ 
        $store_id = '';
        $api_token = '';
        if (env('APP_ENV') === 'local') {
            $store_id = env('MONERIS_STORE_ID_TEST');
            $api_token = env('MONERIS_API_TOKEN_TEST');
        } else {
            $store_id = env('MONERIS_STORE_ID');
            $api_token = env('MONERIS_API_TOKEN');
        }
        /************************ Transaction Variables ******************************/ 
        $data_key = $token;

        do {
            // Create base using timestamp (YmdHis format)
            $timestamp = Carbon::now()->format('YmdHis');
            
            // Generate random string (4 chars)
            $random = substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 6);
            
            // Combine timestamp with random string (format: 20250219123045-RAND)
            $orderid = $timestamp . '-' . $random;
            
            // Check if exists
            $exists = Order::where('moneris_id', $orderid)->exists();
        } while ($exists);

        //temp testing
        // if (env('APP_ENV') === 'local') {
        //     $amount = '1.00'; // moneris only accepts 1.00 for testing
        // } else {
        //     $amount = $validatedData['final_order_price'];
        // }

        $amount = number_format((float)$validatedData['final_order_price'], 2, '.', ''); // moneris only accepts 2 floating point numbers

        if (env('MONERIS_SYNC')) {
            if($validatedData['final_order_price'] > 0){
                $custid = $user->userdetail->teedy_client_id;
                $crypt_type = '1';
                // $expdate = '1911'; //For Temp Tokens only, expiry date is usually optional even with temp token. change if it fails in production
                /************************ Transaction Array **********************************/
                $txnArray = array(
                    'type' => 'res_purchase_cc',
                    'data_key' => $data_key,
                    'order_id' => $orderid,
                    'cust_id' => $custid,
                    'amount' => $amount,
                    'crypt_type' => $crypt_type,
                    // 'expdate'=>$expdate,
                    'dynamic_descriptor' => 'Teedy - Purchase'
                );
                /************************ Transaction Object *******************************/
                $mpgTxn = new \mpgTransaction($txnArray);
                /******************* Credential on File **********************************/
                $cof = new \CofInfo();
                $cof->setPaymentIndicator("U");
                $cof->setPaymentInformation("2");
                // $cof->setIssuerId("12345678901234"); //only used when user stored card (not implemented yet)
                $mpgTxn->setCofInfo($cof);
                /************************ Request Object **********************************/
                $mpgRequest = new \mpgRequest($mpgTxn);
                $mpgRequest->setProcCountryCode("CA"); //"US" for sending transaction to US environment
                $mpgRequest->setTestMode(env('MONERIS_TEST_MODE'));
                /************************ mpgHttpsPost Object ******************************/
                $mpgHttpPost = new \mpgHttpsPost($store_id,$api_token,$mpgRequest);
                /************************ Response Object **********************************/
                $mpgResponse = $mpgHttpPost->getMpgResponse();
        
                Log::info('mpgResponse: ' . json_encode($mpgResponse));
                Log::info('return code: ' . $mpgResponse->getResponseCode());
                $success = $mpgResponse->getResponseCode();
                Log::info('Return code type: ' . gettype($mpgResponse->getResponseCode()));
            }else {
                //free, dont process with moneris
                $success = '027';
                $mpgResponse = false;
            }

        } else {
            $success = '027';
            $mpgResponse = false;
        }

        $successCodes = [
            '000', '001', '002', '003', '004', '005', 
            '006', '007', '008', '009', '010', '023', 
            '024', '025', '026', '027', '028', '029'
        ];

        $success = in_array($success, $successCodes);

        Log::info('Payment Success: ' . $success);


        // Process successful order
        if ($success) {
            // ===== DOUBLE-CHECK STOCK BEFORE FINALIZING ORDER =====
            // Final stock check right before deducting
            $finalStockErrors = [];
            
            foreach ($validatedData['product_list'] as $product) {
                $productId = $product['id'];
                $requestedQuantity = $product['quantity'];
                
                // Fresh stock check
                $currentProduct = ProductDetail::find($productId);
                
                if ($currentProduct->stock < $requestedQuantity) {
                    $finalStockErrors[] = [
                        'product' => $product['name'],
                        'requested' => $requestedQuantity,
                        'available' => $currentProduct->stock
                    ];
                }
            }

            if (!empty($finalStockErrors)) {
                // Payment was successful but stock changed - need to handle this
                Log::error('CRITICAL: Payment processed but stock insufficient - manual intervention required', [
                    'user_id' => $user->id,
                    'order_id' => $orderid,
                    'moneris_response' => $mpgResponse ? $mpgResponse->getTxnNumber() : 'free_order',
                    'final_stock_errors' => $finalStockErrors
                ]);

                // For now, log for manual processing
                return response()->api(false, __('teedy/orders.paid_but_failed'), 201);
            }
            // ===== END FINAL STOCK CHECK =====

            // Create order
            $order = new Order();
            $order->moneris_id = $orderid;
            $order->fk_user_id = $user->id;
            $order->status = 'in_progress';
            $order->date_in_progress = Carbon::now();
            $order->order_content = json_encode($validatedData);
            $order->total = $validatedData['final_order_price'];
            $order->client_info = json_encode($client_info);
            $order->txn_number = $mpgResponse ? $mpgResponse->getTxnNumber() : 'free order';
            $order->reference_number = $mpgResponse ? $mpgResponse->getReferenceNum() : 'free order';
            $order->fk_promo_code_id = $promo?->id;
            $order->total_quota_weight = $user->cartPrice->totalWeight;
            $order->cardholder = $request->cardholder;

            // Clear frozen cart
            $user->cartPrice->delete();

            // Remove items from stock with enhanced logging
            // todo might check if after an order qty is updated with cache version of products
            foreach ($validatedData['product_list'] as $productData) {
                $productId = $productData['id'];
                $quantity = $productData['quantity'];
                
                $product = ProductDetail::find($productId);
                
                Log::info('Updating product stock', [
                    'product_id' => $productId,
                    'sku' => $product->sku,
                    'quantity_to_remove' => $quantity,
                    'stock_before' => $product->stock,
                    'stock_after' => $product->stock - $quantity
                ]);
                
                $product->stock -= $quantity;
                $this->delistProductIfOutOfStock($product);
                $product->save();
            }

            // Update user quota
            if($user->userdetail->veteran){
                $user->userdetail->quota_veteran_remaining -= $order->total_quota_weight;
            } else {
                $user->userdetail->quota_user_remaining -= $order->total_quota_weight;
            }
            $user->userdetail->save();

            // Send mail
            $mailData = [
                'name' => $user->fullname,
                'address' => $user->shipping_address,
                'shipping_type' => $user->getShippingTypeAttribute(),
                'phone' => $user->userdetail->phone,
                'email' => $user->email,
                'moneris_id' => $order->moneris_id,
                'order' => substr($order->moneris_id, -6),
                'order_content' => $order->order_content,
                'lang' => $user->userdetail->language,
                'veteran' => $user->userdetail->veteran,
            ];

            $order->save();

            // Send confirmation emails
            try {
                $mailData = [
                    'name' => $user->fullname,
                    'address' => $user->shipping_address,
                    'shipping_type' => $user->getShippingTypeAttribute(),
                    'phone' => $user->userdetail->phone,
                    'email' => $user->email,
                    'moneris_id' => $order->moneris_id,
                    'order' => substr($order->moneris_id, -6),
                    'order_content' => $order->order_content,
                    'lang' => $user->userdetail->language,
                    'veteran' => $user->userdetail->veteran,
                ];

                Mail::send(new EmailNotification($user->email, __('mail.order.subject'), 'customer.order_confirmation', $mailData));
                App::setLocale('fr');
                Mail::send(new EmailNotification('admin', __('mail.order.admin.subject', ['name' => $mailData['name'], 'order' => $mailData['order']]), 'admin.new_order', $mailData));
            } catch (Exception $e) {
                Log::error('Error sending order confirmation emails', [
                    'order_id' => $orderid,
                    'error' => $e->getMessage()
                ]);
            }

            return response()->api(true, 'Order created successfully', 201);
        } else {
            $errorCode = $mpgResponse ? $mpgResponse->getResponseCode() : 'unknown';
            $errorMessage = $this->getMonerisErrorMessage($errorCode);
            
            Log::error('Payment declined', [
                'code' => $errorCode,
                'user_id' => $user->id,
                'order_total' => $validatedData['final_order_price']
            ]);
            
            return response()->api(false, $errorMessage, 201);
        }

    }

    /**
     * Get friendly error message from Moneris response code
     * 
     * @param string $code The response code from Moneris
     * @return string User-friendly error message
     */
    private function getMonerisErrorMessage($code) {
        // Map error codes to translation keys
        $errorMap = [
            // Declined cards
            '050' => 'declined',
            '051' => 'expired_card',
            '052' => 'pin_error',
            '054' => 'security_issue',
            '055' => 'unsupported_card',
            '056' => 'unsupported_card',
            '057' => 'card_limitations',
            '059' => 'card_limitations',
            '076' => 'insufficient_funds',
            '077' => 'card_limit_exceeded',
            '078' => 'duplicate_transaction',
            '082' => 'usage_limit',
            '087' => 'refund_limit',
            '096' => 'pin_required',
            '481' => 'bank_declined',
            
            // Credit card specific
            '476' => 'processing_error',
            '477' => 'invalid_card',
            '482' => 'expired_card',
            '486' => 'cvv_error',
            '487' => 'cvv_error',
            '489' => 'cvv_error',
            '490' => 'cvv_error',
            
            // System errors
            '810' => 'timeout',
            '811' => 'system_error',
        ];
        
        // If we have a specific message for this code, return its translation
        if (isset($errorMap[$code])) {
            return __('api.moneris.' . $errorMap[$code]);
        }
        
        // If code starts with '05' (Do Not Honor errors)
        if (substr($code, 0, 2) === '05') {
            return __('api.moneris.declined');
        }
        
        // If code starts with '4' (Credit card declines)
        if (substr($code, 0, 1) === '4') {
            return __('api.moneris.declined');
        }
        
        // Default message for any other code
        return __('api.moneris.general_error');
    }

    //get the current open totals (transactions authorized but not posted yet) NOT USED YET
    public function getOpenTotals()
    {

        $store_id = env('MONERIS_STORE_ID_TEST'); 
        $api_token = env('MONERIS_API_TOKEN_TEST');
        $ecr_number= env('MONERIS_ECR_NUMBER_TEST');

        ## step 1) create transaction array ###
        $txnArray = array('type'=>'opentotals');
        $mpgTxn = new \mpgTransaction($txnArray);

        ## step 2) create mpgRequest object ###
        $mpgReq = new \mpgRequest($mpgTxn);
        $mpgReq->setProcCountryCode("CA"); //"US" for sending transaction to US environment
        $mpgReq->setTestMode(true); //false or comment out this line for production transactions

        ## step 3) create mpgHttpsPost object which does an https post ##
        $mpgHttpPost = new \mpgHttpsPost($store_id,$api_token,$mpgReq);

        ## step 4) get an mpgResponse object ##
        $mpgResponse = $mpgHttpPost->getMpgResponse();

        ##step 5) get array of all credit cards
        $creditCards = $mpgResponse->getCreditCards($ecr_number);

        ## step 6) loop through the array of credit cards and get information
        for($i=0; $i < count($creditCards); $i++)
        { print 
            "Card Type =
            $
            creditCards[$i]";
            print "\nPurchase Count = "
            . $mpgResponse->getPurchaseCount($ecr_number,$creditCards[$i]);
            print "\nPurchase Amount = "
            . $mpgResponse->getPurchaseAmount($ecr_number,$creditCards[$i]);
            print "\nRefund Count = "
            . $mpgResponse->getRefundCount($ecr_number,$creditCards[$i]);
            print "\nRefund Amount = "
            . $mpgResponse->getRefundAmount($ecr_number,$creditCards[$i]);
            print "\nCorrection Count = "
            . $mpgResponse->getCorrectionCount($ecr_number,$creditCards[$i]);
            print "\nCorrection Amount = "
            . $mpgResponse->getCorrectionAmount($ecr_number,$creditCards[$i]);
        }
    }

    public function showCart()
    {
        $user = Auth::user();
        return response()->api(true, 'Cart retrieved successfully', 200, $cart);
    }

    function mapCartItemToOrderItem($item) {
        $product = $item->product;
        $price = $product->discounted_price ?? $product->price;
        $total = round($item->qty * $price, 2);

        $base_total = round($item->qty * $product->price, 2);
       
        return [
            'id' => $product->id,
            'name' => $product->title,
            'product_title_fr'  => $product->title_fr,
            'product_description_bl' => $product->description_bl,
            'product_title_en'  => $product->title_en ?? $product->title_fr,
            'sku' => $product->sku,
            'quantity' => $item->qty,
            'type' => $product->productable->type['title'] ?? '',
            'format' => $product->productable->format ?? '',
            'unity' => $product->productable->unity ?? '',
            'uom' => $product->productable->bl_displayuom ?? '',
            'image' => $product->images->first()->url ?? '',
            'product_price' => $price, // actual price after global discount
            'base_price' => $product->price, // original price
            'discount' => Order::discountToPercentage($base_total, $total),
            'total' => $total,
            'base_total' => $base_total,
        ];
    }

        /**
     * Delist (unpublish) a product if it's out of stock
     * 
     * @param ProductDetail $product The product to check and potentially delist
     * @return bool Returns true if product was delisted, false otherwise
     */
    private function delistProductIfOutOfStock(ProductDetail $product)
    {
        // Check if stock is zero or negative
        if ($product->stock <= 0) {
            // Only delist if the product is currently published and check if there are related products. Only delist if all related products are at 0 as well
            $relatedProducts = $product->related_products()->where('publish', true)->get();

            $allRelatedOutOfStock = $relatedProducts->isEmpty() || $relatedProducts->every(function ($relatedProduct) {
                return $relatedProduct->stock <= 0;
            });
            if ($allRelatedOutOfStock) {
                //delist all products

                if ($product->publish) {
                    $originalStatus = $product->publish;
                    $product->publish = false;
                    
                    Log::info('Product delisted due to zero stock', [
                        'product_id' => $product->id,
                        'sku' => $product->sku,
                        'title' => $product->title_fr,
                        'stock' => $product->stock,
                        'previous_status' => $originalStatus
                    ]);

                    foreach ($relatedProducts as $relatedProduct) {
                        $relatedProduct->publish = false;
                        $relatedProduct->save();
                    }

                    return true;
                }
            }
                
        }

        return false;
    }
    
}
