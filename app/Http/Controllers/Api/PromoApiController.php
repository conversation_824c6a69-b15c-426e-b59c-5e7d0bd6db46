<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PromoBanner;
use App\Models\PromoSlide;

class PromoApiController extends Controller
{
    public function slides() {
        $slides = PromoSlide::where('published', 1)->orderBy('lft')->get();
        return response()->api(true, $slides);
    }

    public function banner() {
        $banners = PromoBanner::where('published', 1)
            ->whereIn('position', ['top', 'bottom'])
            ->get()
            ->groupBy('position')
            ->map(function ($group) {
                return $group->first();
            });

        return response()->api(true, $banners);
    }
}
