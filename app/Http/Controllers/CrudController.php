<?php

namespace App\Http\Controllers;

use Backpack\CRUD\app\Http\Controllers\CrudController as BaseCrudController;
use Backpack\CRUD\app\Library\CrudPanel\CrudPanelFacade as CRUD;

class CrudController extends BaseCrudController
{
    public function __construct()
    {
        parent::__construct();
    }

    // Append '_en' to slug_en if identical to slug_fr
    public function validateSlug() {
        $request = CRUD::getRequest();
        ['slug_fr' => $fr, 'slug_en' => $en] = $request->all();
        if ($fr and $en and $fr == $en) { $request->merge(['slug_en' => $en . '-en']); }
    }
}
