<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class StorageController extends Controller
{
    public function show($path)
    {
        $path = 'users/' . $path;
        if (!Storage::disk('private')->exists($path)) {
            abort(404); // Return a 404 error if the file does not exist
        }

        if(backpack_auth()->check()){
            return response()->file(Storage::disk('private')->path($path));
        }
        else{
            abort(403); // Return a 403 error if the user is not authenticated
        }
    }
}
