<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ShippingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // only allow updates if the user is logged in
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title_fr'     => 'required|min:3|max:255',
            'title_en'     => 'required|min:3|max:255',
            'price'        => 'required|numeric',
            'type'         => 'required|in:fixed,percent,free',
            'country'      => 'required',
            'province'     => 'nullable',
            'postal_codes' => [
                'nullable',
                'regex:/^([A-Z]\d[A-Z]\s*,\s*)*[A-Z]\d[A-Z]$/'
            ],
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'title_fr'     => __('teedy/orders.shippings.labels.title_fr'),
            'title_en'     => __('teedy/orders.shippings.labels.title_en'),
            'price'        => __('teedy/orders.shippings.labels.price'),
            'type'         => __('teedy/orders.shippings.labels.type'),
            'country'      => __('teedy/orders.shippings.labels.country'),
            'province'     => __('teedy/orders.shippings.labels.province'),
            'postal_codes' => __('teedy/orders.shippings.labels.postal_codes'),
        ];
    }
}
