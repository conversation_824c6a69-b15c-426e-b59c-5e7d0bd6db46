<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class TaxeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // only allow updates if the user is logged in
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'code'              => 'required|min:2|max:60',
            'value'             => 'required|numeric',
            'country'           => 'required',
            'province'          => 'nullable',
            'add_existance_tax' => 'boolean'
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'code'              => __('teedy/orders.taxes.labels.code'),
            'value'             => __('teedy/orders.taxes.labels.value'),
            'country'           => __('teedy/orders.taxes.labels.country'),
            'province'          => __('teedy/orders.taxes.labels.province'),
            'add_existance_tax' => __('teedy/orders.taxes.labels.add_existance_tax'),
        ];
    }
}
