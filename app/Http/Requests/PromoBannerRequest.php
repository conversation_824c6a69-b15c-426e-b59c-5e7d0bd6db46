<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PromoBannerRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            'content_fr'    => 'required|max:255',
            'content_en'    => 'required|max:255',
        ];
    }

    public function attributes()
    {
        return [
            'content_fr'    => 'texte (FR)',
            'content_en'    => 'texte (EN)',
        ];
    }
}
