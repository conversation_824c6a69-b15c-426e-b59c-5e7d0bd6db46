<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PrescriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // only allow updates if the user is logged in
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fk_user_id'            => 'required|integer',
            'status'                => 'required|string',
            'start_date'            => 'required|date',
            'end_date'              => 'required|date',
            // 'clinic_name'           => 'required|string',
            'doc_name'              => 'required|string',
            'daily_dosage'          => 'required|string',
            'prescription_number'   => 'string',
            // 'prescription_photo'    => 'required', // not re validate if existings
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'user'                  => 'utilisateur',
            'status'                => 'status',
            'start_date'            => 'date de début',
            'end_date'              => 'date d\'expiration',
            // 'clinic_name'           => 'nom de la clinique',
            'doc_name'              => 'nom du médecin',
            'daily_dosage'          => 'dosage',
            'prescription_number'   => 'numéro de prescription',
            'prescription_photo'    => 'photo de la prescription',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            //
        ];
    }
}
