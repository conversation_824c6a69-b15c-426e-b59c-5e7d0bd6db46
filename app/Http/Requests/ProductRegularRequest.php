<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductRegularRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            'category'      => 'required',
            'format'        => 'required',
            'bl_displayuom' => 'required',
            'quota_value'   => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'category'      => 'catégorie',
            'format'        => 'format',
            'bl_displayuom' => 'unité de mesure bluelink',
            'quota_value'   => 'valeur de quota',
        ];
    }
}
