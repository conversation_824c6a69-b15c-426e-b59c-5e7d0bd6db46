<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DiscountRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            'title_fr'      => 'required|max:60',
            'amount'        => 'required|numeric|gt:0',
            'type'          => 'required',
        ];
    }

    public function messages()
    {
        return [
            'start_date.before' => 'La date de début doit être avant la date de fin',
            'amount.gt' => 'Le montant du rabais doit être supérieur à zéro.',
            'amount.numeric' => 'Le montant du rabais doit être une valeur numérique.'
        ];
    }

    public function attributes()
    {
        return [
            'title_fr' => 'titre',
            'amount' => 'montant',
            'type' => 'type',

        ];
    }

    protected function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $products = $this->input('products', []);
            $producers = $this->input('producers', []);
            $categories = $this->input('categories', []);

            $selectedCount = 0;
            if (!empty($products)) $selectedCount++;
            if (!empty($producers)) $selectedCount++;
            if (!empty($categories)) $selectedCount++;

            if ($selectedCount > 1) {
                $validator->errors()->add('discount_type', 'Vous ne pouvez appliquer ce rabais sur qu\'un seul type à la fois: produits, producteurs, ou catégories.');
            }
        });
    }
}
