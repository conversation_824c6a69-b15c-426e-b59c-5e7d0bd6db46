<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class OrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // only allow updates if the user is logged in
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'moneris_id'         => 'required',
            'status'             => 'required',
            'date_in_progress'   => 'nullable',
            'date_shipped'       => 'nullable',
            'date_delivered'     => 'nullable',
            'date_cancelled'     => 'nullable',
            // 'order_content'      => 'required',
            'total'              => 'required',
            // 'additional_content' => 'required',
            // order content subfield
            // 'order_content.*.product_title_fr' => 'required',
            // 'order_content.*.product_title_en' => 'required',
            // 'order_content.*.product_sku'      => 'required',
            // 'order_content.*.quantity'         => 'required',
            // 'order_content.*.unit_price'       => 'required',
            // 'order_content.*.total'            => 'required',
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'user'               => __('teedy/orders.orders.labels.user'),
            'moneris_id'         => __('teedy/orders.orders.labels.moneris_id'),
            'status'             => __('teedy/orders.orders.labels.status'),
            'date_in_progress'   => __('teedy/orders.orders.labels.date_in_progress'),
            'date_shipped'       => __('teedy/orders.orders.labels.date_shipped'),
            'date_delivered'     => __('teedy/orders.orders.labels.date_delivered'),
            'date_cancelled'     => __('teedy/orders.orders.labels.date_cancelled'),
            'order_content'      => __('teedy/orders.orders.labels.order_content'),
            'total'              => __('teedy/orders.orders.labels.total'),
            'additional_content' => __('teedy/orders.orders.labels.additional_content'),
            // order content subfield
            'order_content.*.product_title_fr' => __('teedy/orders.orders.labels.order_content_fields.product_title_fr'),
            'order_content.*.product_title_en' => __('teedy/orders.orders.labels.order_content_fields.product_title_en'),
            'order_content.*.product_sku'      => __('teedy/orders.orders.labels.order_content_fields.product_sku'),
            'order_content.*.quantity'         => __('teedy/orders.orders.labels.order_content_fields.quantity'),
            'order_content.*.unit_price'       => __('teedy/orders.orders.labels.order_content_fields.unit_price'),
            'order_content.*.total'            => __('teedy/orders.orders.labels.order_content_fields.total'),
        ];
    }
}
