<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProductCategoryRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        $id = $this->get('id');

        return [
            'title_fr' => [
                'required', 
                'max:140'
            ],
            'title_en' => [
                'required', 
                'max:140'
            ],
            'slug_fr' => [
                'required', 
                Rule::unique('product_categories')->ignore($id), 
                Rule::unique('product_categories', 'slug_en')->ignore($id),
                'max:140'
            ],
            'slug_en' => [
                'required', 
                Rule::unique('product_categories')->ignore($id), 
                Rule::unique('product_categories', 'slug_fr')->ignore($id),
                'max:140'
            ],
        ];
    }

    public function attributes()
    {
        return [
            'title_fr'  => 'nom (français)',
            'slug_fr'   => 'slug (français)',
            'title_en'  => 'nom (anglais)',
            'slug_en'   => 'slug (anglais)',
        ];
    }

    public function messages()
    {
        return [
            'slug_fr.unique'   => 'Ce slug est déjà utilisé',
            'slug_en.unique'   => 'Ce slug est déjà utilisé',
        ];
    }

}
