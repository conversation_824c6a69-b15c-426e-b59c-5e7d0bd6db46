<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PhysicianRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // only allow updates if the user is logged in
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            //set the rules for the physician, bluelink_id maximum field is 10 characters
            'name' => 'required',
            'bluelink_id' => 'required|max:10|unique:physicians,bluelink_id,'.$this->id,
            // 'address' => 'required',
            // 'city' => 'required',
            // 'zip' => 'required',
            // 'province' => 'required',
            // 'phone' => 'required',
            // 'email' => 'required|email',
        ];
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'name',
            'bluelink_id' => 'code professionnel',
            // 'address' => 'address',
            // 'city' => 'city',
            // 'zip' => 'zip',
            // 'province' => 'province',
            // 'phone' => 'phone',
            // 'email' => 'email',
        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            //
        ];
    }
}
