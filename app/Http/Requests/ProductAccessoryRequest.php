<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductAccessoryRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            // 'size' => 'required',
            // 'unity' => 'required',
            'bl_displayuom' => 'required',
        ];
    }

    public function attributes()
    {
        return [
            // 'size' => 'taille',
            // 'unity' => 'unité',
            'bl_displayuom' => 'Display UOM',

        ];
    }
}
