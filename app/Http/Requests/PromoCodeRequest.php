<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PromoCodeRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            'code' => 'required|max:25' . ($this->method() == 'POST' ? '|unique:promo_codes,code' : '') . $this->id,
            'start_date'    => 'required|date',
            'end_date'      => 'required|date|after:start_date',
            'amount'        => 'required',
            'type'          => 'required',
            'nb_per_user'   => '',
        ];
    }

    public function attributes()
    {
        return [
            'title_fr' => 'titre',
            'start_date' => 'date de début',
            'end_date' => 'date de fin',
            'amount' => 'montant',
            'type' => 'type',
        ];
    }

    public function messages()
    {
        return [
            'minimum_price.lt'  => 'Le prix minimum doit être inférieur au prix maximum',
            'minimum_items.lt'  => 'La quantité minimum doit être inférieure à la quantité maximum',
            'start_date.before' => 'La date de début doit être avant la date de fin'
        ];
    }
}
