<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ProductDetailRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        $id = request()->route('id');
        return [
            // These fields are placed inside the relation field
            'productdetail'             => 'required',
            'productdetail.*.producer'  => 'required',
            'productdetail.*.sku'       => 'required',
            'productdetail.*.stock'     => 'required',
            'productdetail.*.price'     => 'required',
            'productdetail.*.description_bl'     => 'required',
            'productdetail.*.packaging_date' => 'nullable|date_format:Y-m-d',
            'productdetail.*.featured'  => [
                'boolean',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        // Extract the index from the attribute path (e.g., "productdetail.0.featured" -> 0)
                        preg_match('/productdetail\.(\d+)\.featured/', $attribute, $matches);
                        $index = $matches[1] ?? 0;
                        
                        // Get the region for this specific product detail
                        $region = request()->input("productdetail.{$index}.region");
                        
                        if (!$region) {
                            $fail('La région est requise pour les produits en vedette.');
                            return;
                        }
                        
                        // Count featured products for this specific region
                        $featuredCount = \App\Models\ProductDetail::where('featured', true)
                            ->where('region', $region)
                            ->count();
                        
                        if ($featuredCount >= 20) {
                            $regionName = $region === 'qc' ? 'Québec' : 'Ontario';
                            $fail("Vous ne pouvez pas ajouter un autre produit en vedette pour la région {$regionName}. La limite maximale de 20 produits en vedette par région a été atteinte.");
                        }
                    }
                },
            ],

            // These fields are placed outside the relation field (tabs FR/EN)
            'title_fr'              => 'required|max:255',
            'title_en'              => 'max:255',
            'slug_fr'               => 'required|max:140',
            'slug_en'               => 'nullable|max:140',
            'description_fr'        => 'required',
            'meta_title_fr'         => 'max:60',
            'meta_title_en'         => 'max:60',
            'meta_description_fr'   => 'max:160',
            'meta_description_en'   => 'max:160',
        ];
    }

    public function attributes()
    {
        return [
            'title_fr'              => 'nom (français)',
            'title_en'              => 'nom (anglais)',
            'slug_fr'               => 'slug (français)',
            'slug_en'               => 'slug (anglais)',
            'description_fr'        => 'description (français)',
            'description_en'        => 'description (anglais)',
            'meta_title_fr'         => 'titre meta (français)',
            'meta_title_fr'         => 'titre meta (français)',
            'meta_title_en'         => 'titre meta (anglais)',
            'meta_description_fr'   => 'description meta (français)',
            'meta_description_en'   => 'description meta (anglais)',

            'productdetail.*.producer'  => __('teedy/products.fields.producer'),
            'productdetail.*.sku'       => __('teedy/products.fields.sku'),
            'productdetail.*.stock'     => __('teedy/products.fields.stock'),
            'productdetail.*.price'     => __('teedy/products.fields.price'),
            'productdetail.*.description_bl'     => 'description bluelink',
        ];
    }
}
