<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class PromoSlideRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            'image'      => 'required',
            'image_en'   => 'required',
            'title_fr'   => 'required',
            'title_en'    => 'required',
            'link_fr'       => 'required',
            'link_en'     => 'required'
        ];
    }

    public function attributes()
    {
        return [
            'image'      => 'image',
            'title_fr'   => 'titre FR',
            'title_en'    => 'titre EN',
            'link_fr'       => 'lien FR',
            'link_en'     => 'lien EN'
        ];
    }
}
