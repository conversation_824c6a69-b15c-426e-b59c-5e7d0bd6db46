<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        // only allow updates if the user is logged in
        return backpack_auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $id = $this->get('id') ?? request()->route('id');

        $rules = [
            'lastname'                => 'required|max:255',
            'firstname'               => 'required|max:255',
            'password'                => 'sometimes|confirmed',
            'roles'                   => 'required',
            'email'                   => 'required|unique:' . config('permission.table_names.users', 'users') . ',email,' . $id,
            'userdetail'              => 'required',
            'userdetail.*.birth_date' => 'required|date',
            'userdetail.*.language'   => 'required|in:fr,en',
            'userdetail.*.gender'     => 'nullable|in:m,f,x',
            'userdetail.*.veteran'    => 'required',
            'userdetail.*.veteran_id' => 'nullable|required_if:userdetail.*.veteran,1',
            'userdetail.*.quota_veteran_remaining' => 'nullable|required_if:userdetail.*.veteran,1',
            'userdetail.*.quota_veteran_allowed'   => 'nullable|required_if:userdetail.*.veteran,1',
            'userdetail.*.veteran_quota_date'      => 'nullable|required_if:userdetail.*.veteran,1|date',
            'addresses'               => 'required',
            'addresses.*.address'     => 'required',
            'addresses.*.city'        => 'required',
            'addresses.*.pc'          => 'required',
            'addresses.*.delivery'    => 'required',
            'addresses.*.billing'     => 'required',
            'addresses.*.last_use'    => 'required',
            'addresses.*.shipname'    => 'required',
        ];


        return $rules;
    }

    /**
     * Get the validation attributes that apply to the request.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'lastname'                => 'nom de famille',
            'firstname'               => 'prénom',
            'password'                => 'mot de passe',
            'email'                   => 'courriel',
            'userdetail'              => 'Détails',
            'userdetail.*.birth_date' => 'date de naissance',
            'userdetail.*.language'   => 'langue de contact',
            'userdetail.*.gender'     => 'genre',
            'addresses.*.address'     => 'numéro, rue, appartement',
            'addresses.*.city'        => 'ville',
            'addresses.*.pc'          => 'code postal',
            'addresses.*.delivery'    => 'addresse de livraison',
            'addresses.*.billing'     => 'addresse de facturation',
            'addresses.*.last_use'    => 'active',
            'addresses.*.shipcode'    => 'Code Bluelink de l\'addresse de livraison',
            'addresses.*.shipname'    => 'Nom de l\'addresse de livraison',
            //veteran fields
            'userdetail.*.veteran'    => 'Vétéran',
            'userdetail.*.veteran_id' => 'ID de vétéran',
            'userdetail.*.quota_veteran_remaining' => 'Quota vétéran restant',
            'userdetail.*.quota_veteran_allowed'   => 'Quota permis',
            'userdetail.*.veteran_quota_date'      => 'Date d\'initialisation quotas',

        ];
    }

    /**
     * Get the validation messages that apply to the request.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'email.unique_'.config('permission.table_names.users', 'users') => 'blop',
            'veteran_id.required' => 'The veteran ID is required when veteran is true.',
            'quota_veteran_remaining.required' => 'The remaining quota is required when veteran is true.',
            'quota_veteran_allowed.required' => 'The allowed quota is required when veteran is true.',
            'veteran_quota_date.required' => 'The quota date is required when veteran is true.',
        ];
    }
}
