<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProducerRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        $id = $this->get('id');

        return [
            'title_fr' => [
                'required',
                'max:255'
            ],
            'title_en' => [
                'max:255'
            ],
            'slug_fr' => [
                'required', 
                Rule::unique('producers')->ignore($id), 
                Rule::unique('producers', 'slug_en')->ignore($id),
                'max:140'
            ],
            'slug_en' => [
                Rule::unique('producers')->ignore($id), 
                Rule::unique('producers', 'slug_en')->ignore($id),
                'max:140'
            ],
        ];
    }

    public function attributes()
    {
        return [
            'title_fr'          => 'nom (français)',
            'slug_fr'           => 'slug (français)',
            'title_en'          => 'nom (anglais)',
            'slug_en'           => 'slug (anglais)',
        ];
    }

    public function messages()
    {
        return [
            'slug_fr.unique'   => 'Ce slug est déjà utilisé',
            'slug_en.unique'   => 'Ce slug est déjà utilisé',
        ];
    }
}
