<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class HolidayRequest extends FormRequest
{
    public function authorize() { return backpack_auth()->check(); }

    public function rules()
    {
        return [
            'title_fr'      => 'required|max:60',
            'date'        => 'required',
            'active'          => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'title_fr' => 'titre',
            'date' => 'date',
            'active' => 'active',
        ];
    }

    public function messages()
    {
        return [
            //
        ];
    }
}
