<?php

namespace App\Providers;

use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use App\Models\Address;
use App\Models\User;
use App\Models\UserDetail;
use App\Models\ProductImage;
use App\Models\Order;
use App\Models\Prescription;
use App\Observers\UserObserver;
use App\Observers\ProductImageObserver;
use App\Observers\UserDetailObserver;
use App\Observers\OrderObserver;
use App\Observers\PrescriptionObserver;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        User::observe(UserObserver::class);
        UserDetail::observe(UserDetailObserver::class);
        ProductImage::observe(ProductImageObserver::class);
        Order::observe(OrderObserver::class);
        Prescription::observe(PrescriptionObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
