<?php

namespace App\Providers;
use Carbon\Carbon;
use Cmixin\BusinessDay;

use Illuminate\Support\Facades\App;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\URL;

use App\Models\ProductDetail;
use App\Observers\ProductDetailObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (App::environment(['production'])) {
            URL::forceScheme('https');
        }

        $this->app->bind(
            \Backpack\PermissionManager\app\Http\Controllers\UserCrudController::class, //this is package controller
            \App\Http\Controllers\Admin\UserCrudController::class //this should be your own controller
        );


        Carbon::mixin(new BusinessDay());

        // Harmonize all API responses with the same format
        Response::macro('api', function ($success, $message = '', $code = 200, $additional_fields = []) {
            $message = $message ? ['message' => $message] : [];
            $response = array_merge(
                ['success' => $success],
                $message,
                $additional_fields
            );

            return Response::json($response, $code);
        });
        ProductDetail::observe(ProductDetailObserver::class);
    }
}
