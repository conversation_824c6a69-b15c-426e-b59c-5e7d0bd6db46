<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class EmailNotification extends Mailable
{
    use Queueable, SerializesModels;

    public $mail_to;
    public $subject;
    public $view;
    public $data;

    public function __construct(string $mail_to, string $subject, string $view, $data = [])
    {
        $this->mail_to = $mail_to == 'admin' ? config('mail.from.address') : $mail_to;
        $this->subject = $subject;
        $this->view = $view;
        $this->data = $data;
    }

    public function build()
    {
        return $this
            ->to($this->mail_to)
            ->from(config('mail.from'))
            ->subject($this->subject)
            ->view('mails.' . $this->view)
            ->with($this->data);
            
    }
}
