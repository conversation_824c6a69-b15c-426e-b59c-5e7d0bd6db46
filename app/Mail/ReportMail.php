<?php

// namespace App\Notifications;
namespace App\Mail;

use Illuminate\Queue\SerializesModels;
use Illuminate\Mail\Mailable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Bus\Queueable;

class ReportMail extends Mailable
{
    use Queueable, SerializesModels;

    public $view = 'emails.users_report';
    public $subject = 'Teedy';
    public $data = [];

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct($view, $subject, $data)
    {
        $this->view = $view;
        $this->data = $data;
        $this->subject = $subject;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->view($this->view)
            ->subject($this->subject)
            ->with($this->data)
            ->attach(storage_path('app/public/users.xlsx'), [
                'as' => 'users.xlsx', // The name of the attachment in the email
                'mime' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // MIME type for Excel
            ]);
    }
}
