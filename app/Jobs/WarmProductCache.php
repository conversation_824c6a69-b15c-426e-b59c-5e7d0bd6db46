<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use App\Http\Controllers\Api\ProductsApiController;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\App;

class WarmProductCache implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $region;
    protected $lang;

    public function __construct($region, $lang)
    {
        $this->region = $region;
        $this->lang = $lang;
    }

    public function handle()
    {
        Log::channel('cache')->info('Cache warming job started', ['region' => $this->region, 'lang' => $this->lang]);
        
        // Create a fake request that simulates an API request context
        // Not the best but for some reason model dont get correctly loaded when loading the controller via route
        $appUrl = parse_url(config('app.url'));
        $request = \Illuminate\Http\Request::create(
            '/' . strtolower($this->lang) . '/api/products', // URI
            'GET', // Method
            [ // Parameters
                'region' => $this->region,
                'lang' => $this->lang,
                'is_cache_warming' => true
            ],
            [], // Cookies
            [], // Files
            [ // Server variables
                'REQUEST_URI' => '/' . strtolower($this->lang) . '/api/products',
                'HTTP_HOST' => $appUrl['host'],
                'REQUEST_METHOD' => 'GET',
                'SERVER_NAME' => $appUrl['host'],
                'HTTPS' => ($appUrl['scheme'] ?? 'http') === 'https'
            ]
        );

        // Store the original request if there is one
        $originalRequest = app()->bound('request') ? app('request') : null;
        
        // Store the original locale
        $originalLocale = App::getLocale();
        
        try {
            // Set the application locale explicitly for this request (ensure lowercase)
            App::setLocale(strtolower($this->lang));

            // Bind the request to the application container
            app()->instance('request', $request);
            
            // Also set it as the current request for the Request facade
            \Illuminate\Support\Facades\Request::swap($request);
            
            // Set the application URL context for proper route matching
            app('url')->forceRootUrl(config('app.url'));

            // Call the controller method directly to refresh the cache
            $controller = new ProductsApiController();
            $response = $controller->index($request);
            
            Log::channel('cache')->info('Cache warming completed successfully', [
                'region' => $this->region,
                'lang' => $this->lang,
                'method' => 'direct_controller_call'
            ]);
        } catch (\Exception $e) {
            Log::channel('cache')->error('Cache warming failed', [
                'region' => $this->region,
                'lang' => $this->lang,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        } finally {
            // Restore the original locale
            App::setLocale($originalLocale);
            
            // Restore the original request if there was one
            if ($originalRequest) {
                app()->instance('request', $originalRequest);
                \Illuminate\Support\Facades\Request::swap($originalRequest);
            }
        }      
    }
}