<?php

namespace App\Jobs;

use Exception;
use Illuminate\Bus\Queueable;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class CloseBatch implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            if (env('APP_ENV') === 'local') {
                $store_id = env('MONERIS_STORE_ID_TEST');
                $api_token = env('MONERIS_API_TOKEN_TEST');
                $ecr_number = env('MONERIS_ECR_NUMBER_TEST');
            } else {
                $store_id = env('MONERIS_STORE_ID');
                $api_token = env('MONERIS_API_TOKEN');
                $ecr_number = env('MONERIS_ECR_NUMBER');
            }


            ## step 1) create transaction array ###
            $txnArray = array(
                'type' => 'batchclose',
                'ecr_number' => $ecr_number
            );
            $mpgTxn = new \mpgTransaction($txnArray);

            ## step 2) create mpgRequest object ###
            $mpgReq = new \mpgRequest($mpgTxn);
            $mpgReq->setProcCountryCode("CA"); //"US" for sending transaction to US environment
            $mpgReq->setTestMode(true); //false or comment out this line for production transactions

            ## step 3) create mpgHttpsPost object which does an https post ##
            $mpgHttpPost = new \mpgHttpsPost($store_id,$api_token,$mpgReq);

            ## step 4) get an mpgResponse object ##
            $mpgResponse = $mpgHttpPost->getMpgResponse();

            Log::info('ResponseCode = ' . $mpgResponse->getCreditCards($ecr_number));
        } catch (Exception $e) {
            Log::info('Error closing transactions: ' . $e->getMessage());
        }
    }
}
