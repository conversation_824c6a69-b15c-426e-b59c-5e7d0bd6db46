<?php

namespace App\Imports;

use Carbon\Carbon;
use App\Models\ProductDetail;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\ToCollection;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Maatwebsite\Excel\Concerns\WithValidation;
use PhpOffice\PhpSpreadsheet\Calculation\TextData\Text;

class PackagingDateImport implements ToCollection
{
    protected $command;
    protected $updatedCount = 0;
    protected $errorCount = 0;
    protected $skippedCount = 0;
    protected $emptyCount = 0;

    public function __construct(Command $command)
    {
        $this->command = $command;
    }

    public function collection(Collection $rows)
    {
    
        foreach ($rows as $row) 
        {

            try {
                $productCode = $row[0] ?? null;
                $packagingDateRaw = $row[1] ?? null;
                
                // Convert Excel date value to a proper date if it exists and is numeric
                $packagingDate = null;
                if ($packagingDateRaw) {
                    if (is_numeric($packagingDateRaw)) {
                        // Convert Excel date value to Carbon date
                        $packagingDate = Date::excelToDateTimeObject($packagingDateRaw)->format('Y-m-d');
                    } else if (is_string($packagingDateRaw)) {
                        // Try to parse the string date in common formats
                        try {
                            // Parse formats like DD/MM/YYYY, MM/DD/YYYY, YYYY-MM-DD
                            $parsedDate = Carbon::parse($packagingDateRaw);
                            $packagingDate = $parsedDate->format('Y-m-d');
                        } catch (\Exception $e) {
                            $this->command->warn("Could not parse date '{$packagingDateRaw}' for product: {$productCode}");
                            $packagingDate = null;
                        }
                    }
                }
                
                // Only product code is required
                if (!$productCode) {
                    $this->emptyCount++;
                    $this->command->warn("Missing product code in row: " . json_encode($row));
                    continue;
                }

                // Find the product by SKU
                $product = ProductDetail::where('sku', $productCode)->first();

                if ($product) {
                    // Update packaging date only if it exists in the import data
                    if ($packagingDate && $this->isValidDate($packagingDate)) {
                        $product->packaging_date = $packagingDate;
                        $product->save();
                        $this->updatedCount++;
                        $this->command->info("Updated product: {$product->sku} with date: {$product->packaging_date}");
                    }
                    else {
                        $this->skippedCount++;
                        $this->command->info("Skipped product: {$product->sku} (no packaging date)");
                    }
                }
                else {
                    $this->errorCount++;
                    $this->command->warn("Product not found: {$productCode}");
                }

            }catch (\Exception $e) {
                throw $e;
            }
            
        }
    }

    private function isValidDate($date)
    {
        if (!$date || !is_string($date)) {
            return false;
        }
        
        // Check if date matches Y-m-d format and is a valid date
        $dateTime = \DateTime::createFromFormat('Y-m-d', $date);
        return $dateTime && $dateTime->format('Y-m-d') === $date;
    }

    /**
     * Report import results
     */
    public function __destruct()
    {
        $this->command->info("Import completed: {$this->updatedCount} products updated, {$this->skippedCount} skipped (no packaging date), {$this->errorCount} product not found, {$this->emptyCount} empty rows");
    }

    /**
     * Validation rules
     */
    public function rules(): array
    {
        return [
            1 => 'required',
        ];
    }
}