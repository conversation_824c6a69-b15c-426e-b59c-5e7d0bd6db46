<?php

namespace App\Exports;

use App\Models\Product;
use App\Models\ProductDetail;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\DB;

class ProductsExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $product = DB::table('product_details')
            ->select(
                'sku',
                'title_fr',
                'title_en',
                'stock'
            )->get();

        return $product;
    }

    public function headings(): array
    {
        return [
            'SKU',
            'Titre FR',
            'Titre EN',
            'Stock',
        ];
    }
}
