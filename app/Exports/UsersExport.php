<?php

namespace App\Exports;

use App\Models\User;
use App\Models\UserDetail;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\DB;

class UsersExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        $users = DB::table('users')
            ->join('user_details', 'users.id', '=', 'user_details.fk_user_id')
            ->select(
                'users.id',
                'firstname',
                'lastname',
                'email',
                'status',
                'archived',
                'proof_identity',
                'users.created_at',
                'users.updated_at',
                'teedy_client_id',
                'phone',
                'birth_date',
                'gender',
                'quota_user_remaining',
                'language',
                'veteran',
                'quota_veteran_remaining',
                'quota_veteran_allowed',
                'veteran_quota_date',
                'licence_acmpr'
            )->get();

        return $users;
    }

    public function headings(): array
    {
        return [
            'ID',
            'First Name',
            'Last Name',
            'Email',
            'Status',
            'Archived',
            'Proof of Identity',
            'Created At',
            'Updated At',
            'Teedy Client ID',
            'Phone',
            'Birth Date',
            'Gender',
            'Quota User Remaining',
            'Language',
            'Veteran',
            'Veteran Quota Remaining',
            'Veteran Quota Allowed',
            'Veteran Quota Date',
            'ACMPR License',
        ];
    }
}
