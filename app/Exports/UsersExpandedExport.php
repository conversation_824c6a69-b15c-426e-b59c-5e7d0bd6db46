<?php

namespace App\Exports;

use App\Models\User;
use App\Models\UserDetail;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\FromCollection;
use Illuminate\Support\Facades\DB;

class UsersExpandedExport implements FromCollection, WithHeadings
{
    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        // Get users
        $users = DB::table('users')
            ->join('user_details', 'users.id', '=', 'user_details.fk_user_id')
            ->select(
                'users.id',
                'users.firstname',
                'users.lastname',
                'phone',
                'email',
                'users.status'
            )->get();
            
        // Get prescriptions
        $prescriptions = DB::table('prescriptions')
            ->where('prescriptions.fk_user_id', '!=', null)
            ->select(
                'prescriptions.id as prescription_id',
                'prescriptions.fk_user_id',
                'prescriptions.prescription_number',
                'prescriptions.start_date',
                'prescriptions.end_date',
                'prescriptions.status',
                'prescriptions.daily_dosage'
            )->get();
        
        // Create a flattened collection for export
        $exportData = collect();
        
        foreach ($users as $user) {
            // Get all prescriptions for this user
            $userPrescriptions = $prescriptions->where('fk_user_id', $user->id);
            
            // If user has no prescriptions, add one row with user data only
            if ($userPrescriptions->isEmpty()) {
                $exportData->push([
                    'id' => $user->id,
                    'firstname' => $user->firstname,
                    'lastname' => $user->lastname,
                    'phone' => $user->phone,
                    'email' => $user->email,
                    'status' => $user->status,
                    'prescription_id' => null,
                    'prescription_number' => null,
                    'start_date' => null,
                    'end_date' => null,
                    'prescription_status' => null,
                    'daily_dosage' => null
                ]);
            } else {
                // For each prescription, create a row with user data and prescription data
                foreach ($userPrescriptions as $prescription) {
                    $exportData->push([
                        'id' => $user->id,
                        'firstname' => $user->firstname,
                        'lastname' => $user->lastname,
                        'phone' => $user->phone,
                        'email' => $user->email,
                        'status' => $user->status,
                        'prescription_id' => $prescription->prescription_id,
                        'prescription_number' => $prescription->prescription_number,
                        'start_date' => $prescription->start_date,
                        'end_date' => $prescription->end_date,
                        'prescription_status' => $prescription->status,
                        'daily_dosage' => $prescription->daily_dosage
                    ]);
                }
            }
        }
        
        return $exportData;
    }

    public function headings(): array
    {
        return [
            'User ID',
            'First Name',
            'Last Name',
            'Phone',
            'Email',
            'User Status',
            'Prescription ID',
            'Prescription Number',
            'Start Date',
            'End Date',
            'Prescription Status',
            'Daily Dosage'
        ];
    } 
}
