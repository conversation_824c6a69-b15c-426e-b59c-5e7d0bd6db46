APP_NAME=Teedy
APP_ENV=production
APP_KEY=base64:nXd8YhqAOGy5WQx4BsVvTpLsQjBOTZWjM2nIdKryPD0=
APP_DEBUG=true
APP_URL=https://api.teedy.com

CAPTCHA_SECRET=6Le1ThsbAAAAAMUaYxh6pI5SDgpPh3Pr-wbjDy3_

SEND_MAIL=true

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=teedy
DB_USERNAME=""
DB_PASSWORD=""

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=*********************************************************************
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_ADDRESS_SUPPORT=<EMAIL>
MAIL_FROM_NAME=Teedy

# Mailtrap
#MAIL_DRIVER=smtp
#MAIL_HOST=sandbox.smtp.mailtrap.io
#MAIL_PORT=2525
#MAIL_USERNAME=e773f7dfd163cb
#MAIL_PASSWORD=90f2533061a48a
#MAIL_ENCRYPTION=null
#MAIL_FROM_ADDRESS="<EMAIL>"
#MAIL_FROM_NAME="Teedy"

# (mailchimp)
NEWSLETTER_DRIVER=api
NEWSLETTER_API_KEY=************************************
NEWSLETTER_LIST_ID=a08983f6f4
NEWSLETTER_LIST_INTEREST_ID_1=
NEWSLETTER_LIST_INTEREST_ID_2=

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

BACKPACK_LICENSE=6F1K-LZ6Y-1636-GFC8-30EF-8664

FRONT_URL_FR=https://teedy.com/fr
FRONT_URL_EN=https://teedy.com/en
FRONT_URL_RESET_PASSWORD_FR=https://teedy.com/fr/retrouver-mot-de-passe
FRONT_URL_RESET_PASSWORD_EN=https://teedy.com/en/recover-password

MEDICAL_OCEAN_URL="https://ocean.cognisantmd.com/intake/IntakePortal.html?eReqRef=a06fb2a0-2a5e-46dc-b2d5-2d9f18e99d2e"

MONERIS_STORE_ID=gwca041000
MONERIS_API_TOKEN=gVPZNvICI0fDDlI7MWhs
MONERIS_ECR_NUMBER=********

# test account
MONERIS_STORE_ID_TEST=monca10176
MONERIS_API_TOKEN_TEST=O1UBSMpelItCCvE9iLxT
MONERIS_ECR_NUMBER_TEST=******** # electronic commerce number

# this will prevent to finalize an order
MONERIS_SYNC=true
# IMPORTANT: This needs to be true when testing Moneris
MONERIS_TEST_MODE=false

# For activate sync in your environment
SYNC_BLUELINK=true
# PROD
BL_HOST="https://saas.BlueLinkERP.com:38183"
BL_API_KEY=B3DB4EF6-A894-46C7-8C5F-CA4CF8976E98
# DEV
#BL_HOST="https://saas.bluelinkerp.com:37183"
#BL_API_KEY=BC8F7DEE-0B5E-4FDC-83AB-CCDFD28CD99F

#google sheet connection
GOOGLE_SERVICE_ENABLED=true
GOOGLE_SERVICE_ACCOUNT_JSON_LOCATION=/storage/app/google-service-account.json
GOOGLE_SHEET_ID=16QTpN2oP7wcB7wqI-gI_sOFYYuVwtunjvsk3UVQqT8U
GOOGLE_SHEET_NAME=Sheet1

#Sentry
SENTRY_LARAVEL_DSN=https://<EMAIL>/****************
SENTRY_TRACES_SAMPLE_RATE=1.0
VITE_SENTRY_DSN_PUBLIC="${SENTRY_LARAVEL_DSN}"

BASSET_DEV_MODE=false
