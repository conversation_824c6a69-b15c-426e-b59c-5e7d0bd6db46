APP_NAME=Teedy
APP_ENV=local
APP_KEY=base64:gepNRkZ9pZsRjkciula3+JlMhAWiwexg1D6XZ5CEr7w=
APP_DEBUG=true
APP_URL=http://127.0.0.1:8000

FRONT_URL_FR=http://localhost:4200/fr
FRONT_URL_EN=http://localhost:4200/en

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=teedy
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

# MAIL_MAILER=smtp
# MAIL_HOST=smtp.mailtrap.io
# MAIL_PORT=2525
# MAIL_USERNAME=c42372a24b3ca7
# MAIL_PASSWORD=3c0244a1dafdd6
# MAIL_ENCRYPTION=tls
# MAIL_FROM_ADDRESS=<EMAIL>
# MAIL_FROM_NAME=Teedy


# mail stuff
MAIL_MAILER=smtp
MAIL_DRIVER=smtp
MAIL_HOST=smtp.sendgrid.net
MAIL_PORT=587
MAIL_USERNAME=apikey
MAIL_PASSWORD=*********************************************************************
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_ADDRESS_SUPPORT=<EMAIL>
MAIL_FROM_NAME=Teedy

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

MONERIS_STORE_ID=Gwca041000
MONERIS_API_TOKEN=gVPZNvICI0fDDlI7MWhs
MONERIS_ECR_NUMBER=******** # electronic commerce number

# MONERIS_STORE_ID_TEST=store3
# MONERIS_API_TOKEN_TEST=yesguy

# this will prevent to finalize an order
MONERIS_SYNC=true

# For activate sync in your environment
SYNC_BLUELINK=true
# IMPORTANT: This needs to be true when testing Moneris
MONERIS_TEST_MODE=false

# test account
MONERIS_STORE_ID_TEST=monca10176
MONERIS_API_TOKEN_TEST=O1UBSMpelItCCvE9iLxT
MONERIS_ECR_NUMBER_TEST=******** # electronic commerce number

BL_HOST=https://saas.BlueLinkERP.com:38183
BL_API_KEY=B3DB4EF6-A894-46C7-8C5F-CA4CF8976E98

QUEUE_CONNECTION=database

BASSET_DEV_MODE=false
