<?php

return [
    'orders' => [
        'labels' => [
            'user'                 => 'client',
            'moneris_id'           => 'numéro de commande (identifiant Moneris)',
            'status'               => 'Statut',
            'date_in_progress'     => 'date de la commande',
            'date_shipped'         => 'date d\'expédition',
            'date_delivered'       => 'date de livraison',
            'date_cancelled'       => 'date d\'annulation',
            'cardholder'           => 'Titulaire de la carte',
            'order_content'        => 'contenu de la commande',
            'order_content_fields' => [
                'repeatable'       => 'liste des produits',
                'product_title_fr' => 'Titre du produit (Français)',
                'product_title_en' => 'Titre du produit (Anglais)',
                'product_sku'      => 'SKU',
                'quantity'         => 'Quantité',
                'unit_price'       => 'Prix unitaire',
                'total'            => 'Prix total',
            ],
            'change_status'      => 'Changer le statut',
            'total'              => 'total',
            'additional_content' => 'contenu additionnel',
            'section_content'    => 'contenu de la commande',
            'section_address'    => 'Adresses de facturation et d\'expédition',
        ],
        'status_values' => [
            'in_progress' => 'En traitement',
            'shipped'     => 'Expédiée',
            'delivered'   => 'Livrée',
            'cancelled'   => 'Annulée',
        ],
        'status_changing' => 'Le statut de la commande à été changé',
        'singular' => 'Commande',
        'plural'   => 'Commandes',
    ],
    'taxes' => [
        'labels' => [
            'code'              => 'code',
            'value'             => 'valeur',
            'country'           => 'pays',
            'province'          => 'province',
            'add_existance_tax' => 's\'aditionne aux autres taxes',
        ],
        'country_values' => [
            'ca' => 'Canada',
        ],
        'province_values' => [
            'qc' => 'Québec',
            'on' => 'Ontario',
            // 'bc' => 'Colombie-Britannique',
            // 'ab' => 'Alberta',
            // 'mb' => 'Manitoba',
            // 'sk' => 'Saskatchewan',
            // 'nb' => 'Nouveau-Brunswick',
            // 'ns' => 'Nouvelle-Écosse',
            // 'pe' => 'Île-du-Prince-Édouard',
            // 'nl' => 'Terre-Neuve-et-Labrador',
            // 'yt' => 'Yukon',
            // 'nt' => 'Territoires du Nord-Ouest',
            // 'nu' => 'Nunavut',
        ],
        'singular' => 'regle de taxe',
        'plural'   => 'regles sur les taxes',
    ],
    'shippings' => [
        'labels' => [
            'title_fr'     => 'titre en français',
            'title_en'     => 'titre en anglais',
            'price'        => 'tarif',
            'type'         => 'type',
            'country'      => 'pays',
            'province'     => 'province',
            'postal_codes' => 'codes postaux',
            'delivery_type' => 'type de livraison'
        ],
        'country_values' => [
            'ca' => 'Canada',
        ],
        'province_values' => [
            'qc' => 'Québec',
            'on' => 'Ontario',
            // 'bc' => 'Colombie-Britannique',
            // 'ab' => 'Alberta',
            // 'mb' => 'Manitoba',
            // 'sk' => 'Saskatchewan',
            // 'nb' => 'Nouveau-Brunswick',
            // 'ns' => 'Nouvelle-Écosse',
            // 'pe' => 'Île-du-Prince-Édouard',
            // 'nl' => 'Terre-Neuve-et-Labrador',
            // 'yt' => 'Yukon',
            // 'nt' => 'Territoires du Nord-Ouest',
            // 'nu' => 'Nunavut',
        ],
        'delivery_type_values' => [
            '1h' => 'Livraison en 1h',
            '2h' => 'Livraison en 2h',
            '24h' => 'Livraison en 24h',
        ],
        'type_values' => [
            'percent' => '% (pourcentage)',
            'fixed'   => '$ (prix fixe)',
            'free'    => 'gratuite',
        ],
        'hint_pc'  => 'Suite de code postaux en majuscules, juste la zone principal (H9P), séparés par des , sans caratères spéciaux ( - ; . _ )',
        'singular' => 'règle d\'expédition',
        'plural'   => 'règles pour les expéditions',
    ],
    'promo' => [
        'fields' => [
            'title_fr'      => 'Titre (français)',
            'title_en'      => 'Titre (anglais)',
            'code'          => 'Code promo',
            'amount'        => 'Montant',
            'type'          => 'Type',
            'minimum_price' => 'Montant minimum',
            'maximum_price' => 'Montant maximum',
            'minimum_items' => 'Quantité minimum',
            'maximum_items' => 'Quantité maximum',
            'nb_per_user'   => 'Maximum par utilisateur',
            'start_date'    => 'Date de début',
            'end_date'      => 'Date de fin',
            'date'          => 'Date',
            'active'       => 'Actif ?'
        ],
        'types' => [
            'percent'       => '%',
            'amount'        => '$',
            'free_shipping' => 'Livraison gratuite'
        ],
        'types_global' => [
            'percent'       => '%',
            'amount'        => '$',
        ]
    ],
    'discount' => [
        'fields' => [
            'title_fr'      => 'Titre (français)',
            'title_en'      => 'Titre (anglais)',
            'amount'        => 'Montant',
            'type'          => 'Type',
            'start_date'    => 'Date de début',
            'end_date'      => 'Date de fin',
            'producers'     => 'Producteurs',
            'products'      => 'Produits',
            'categories'    => 'Catégories',
        ]
    ],
    'schedules' => [
        'labels' => [
            'hour' => 'Heure',
            'enabled' => 'Livraison possible'
        ]
    ],
    'stock_unavailable' => 'La commande ne peut pas être traitée en raison de problèmes de disponibilité de stock, actualisez la page pour un panier mis à jour',
    'paid_but_failed' => 'La commande a été payée mais n\'a pas pu être traitée, veuillez contacter le support pour obtenir de l\'aide.',


];
