<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | The following language lines contain the default error messages used by
    | the validator class. Some of these rules have multiple versions such
    | as the size rules. Feel free to tweak each of these messages here.
    |
    */

    'accepted'             => 'Le champ :attribute must be accepted.',
    'active_url'           => 'Le champ :attribute n\'est pas une URL valide.',
    'after'                => 'Le champ :attribute doit être supèrieur à :date.',
    'after_or_equal'       => 'Le champ :attribute doit être supèrieur ou égale à :date.',
    'alpha'                => 'Le champ :attribute ne doit contenir uniquement des lettres.',
    'alpha_dash'           => 'Le champ :attribute ne doit contenir que des lettres, nombres, et tirets.',
    'alpha_num'            => 'Le champ :attribute ne doit contenir que des lettres et des nombres.',
    'array'                => 'Le champ :attribute doit être un tableau.',
    'before'               => 'Le champ :attribute doit être inférieur à :date.',
    'before_or_equal'      => 'Le champ :attribute doit être inférieur ou égale à :date.',
    'between'              => [
        'numeric' => 'Le champ :attribute must be between :min and :max.',
        'file'    => 'Le champ :attribute must be between :min and :max kilobytes.',
        'string'  => 'Le champ :attribute must be between :min and :max characters.',
        'array'   => 'Le champ :attribute must have between :min and :max items.',
    ],
    'boolean'              => 'Le champ :attribute field must be true or false.',
    'confirmed'            => 'Le champ de confirmation de :attribute ne correspond pas.',
    'date'                 => 'Le champ :attribute n\'est pas une date valide.',
    'date_format'          => 'Le champ :attribute does not match the format :format.',
    'different'            => 'Le champ :attribute and :other must be different.',
    'digits'               => 'Le champ :attribute must be :digits digits.',
    'digits_between'       => 'Le champ :attribute must be between :min and :max digits.',
    'dimensions'           => 'Le champ :attribute has invalid image dimensions.',
    'distinct'             => 'Le champ :attribute field has a duplicate value.',
    'email'                => 'L\'adresse n\'est pas conforme à une adresse courriel',
    'exists'               => 'Le champs :attribute n\'est pas valide.',
    'file'                 => 'Le champ :attribute doit être un fichier',
    'filled'               => 'Le champ :attribute field is required.',
    'image'                => 'Le champ :attribute must be an image.',
    'in'                   => 'Le champs :attribute n\'est pas valide.',
    'in_array'             => 'Le champ :attribute field does not exist in :other.',
    'integer'              => 'Le champ :attribute must be an integer.',
    'ip'                   => 'Le champ :attribute must be a valid IP address.',
    'json'                 => 'Le champ :attribute must be a valid JSON string.',
    'max'                  => [
        'numeric' => 'Le champ :attribute ne doit pas dépasser :max caractères.',
        'file'    => 'Le champ :attribute may not be greater than :max kilobytes.',
        'string'  => 'Le champ :attribute may not be greater than :max characters.',
        'array'   => 'Le champ :attribute may not have more than :max items.',
    ],
    'mimes'     => 'Le champ :attribute doit être dans un des formats suivant: :values.',
    'mimetypes' => 'Le champ :attribute doit être dans un des formats suivant: :values.',
    'ends_with' => 'Le champ :attribute doit être dans un des formats suivant: :values.',
    'min'       => [
        'numeric' => 'Le champ :attribute doit contenir minimum :min caractères.',
        'file'    => 'Le champ :attribute must be at least :min kilobytes.',
        'string'  => 'Le champ :attribute doit contenir minimum :min caractères.',
        'array'   => 'Le champ :attribute must have at least :min items.',
    ],
    'not_in'               => 'Le champs :attribute n\'est pas valide.',
    'numeric'              => 'Le champ :attribute ne doit contenir que des chiffres.',
    'present'              => 'Le champ :attribute field must be present.',
    'regex'                => 'Le champ :attribute ne correspond pas au format désiré.',
    'required'             => 'Le champ :attribute est obligatoire.',
    'required_if'          => 'Le champ :attribute est obligatoire si le champ :other est :value.',
    'required_unless'      => 'Le champ :attribute est requis avec le champ :other.',
    'required_with'        => 'Le champ :attribute est obligatoire si :values est présent.',
    'required_with_all'    => 'Le champ :attribute field is required when :values is present.',
    'required_without'     => 'Le champ :attribute field is required when :values is not present.',
    'required_without_all' => 'Le champ :attribute field is required when none of :values are present.',
    'same'                 => 'Le champ :attribute et :other ne correspondent pas.',
    'size'                 => [
        'numeric' => 'Le champ :attribute must be :size.',
        'file'    => 'Le champ :attribute must be :size kilobytes.',
        'string'  => 'Le champ :attribute must be :size characters.',
        'array'   => 'Le champ :attribute must contain :size items.',
    ],
    'string'               => 'Le champ :attribute doit être une chaine de caractères.',
    'timezone'             => 'Le champ :attribute must be a valid zone.',
    'unique'               => 'Le champ :attribute doit être unique.',
    'uploaded'             => 'Le téléversement du champ :attribute à échoué.',
    'url'                  => 'Le champ :attribute doit commencer par https://',

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Language Lines
    |--------------------------------------------------------------------------
    |
    | Here you may specify custom validation messages for attributes using the
    | convention "attribute.rule" to name the lines. This makes it quick to
    | specify a specific custom language line for a given attribute rule.
    |
    */

    'custom' => [
        'attribute-name' => [
            'rule-name' => 'custom-message',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Custom Validation Attributes
    |--------------------------------------------------------------------------
    |
    | The following language lines are used to swap our attribute placeholder
    | with something more reader friendly such as "E-Mail Address" instead
    | of "email". This simply helps us make our message more expressive.
    |
    */

    'attributes' => [
        'user'             => 'nom de l\'utilisateur',
        'email'            => 'courriel',
        'password'         => 'mot de passe',
        'firstname'        => 'prénom',
        'lastname'         => 'nom de famille',
        'birth_date'       => 'date de naissance',
        'language'         => 'langue de contact',
        'gender'           => 'genre',
        'delivery_address' => 'adresse',
        'delivery_city'    => 'ville',
        'delivery_pc'      => 'code postal',
        'billing_address'  => 'adresse',
        'billing_city'     => 'ville',
        'billing_pc'       => 'code postal',
    ],

];
