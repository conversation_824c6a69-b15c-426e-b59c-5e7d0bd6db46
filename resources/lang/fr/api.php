<?php

return [
    'user' => [
        'registered' => 'Votre inscription est complétée',
        'deactivated' => 'L\'utilisateur a été désactivé',
        'language-changed' => 'La langue de l\'utilisateur a été modifiée',
        'language-unchanged' => 'Aucun changement requis',
        'not-found' => 'Utilisateur invalide',
        'inactive' => 'Cet utilisateur n\'est pas actif',
        'register-failed' => 'Votre inscription a échoué',
        'recaptcha-failed' => 'Échec de la validation reCAPTCHA',
    ],

    'prescription' => [
        'complete' => 'Votre prescription à été soumise',
        'invalid_file' => 'Le fichier est invalide',
        'failed'   => 'L\'enregistrement de votre prescription à rencontré une erreur.',
    ],

    'product' => [
        'no-items' => 'La requête ne contient aucun item',
        'invalid-id' => 'ID de produit invalide: :id',
        'invalid-qty' => 'Quantité invalide: :qty',
        'over-stock' => 'La quantité demandée excède le stock du produit :item',
        'not-found' => 'Ce produit est introuvable: :id',
        'invalid-evaluation' => 'Évaluation invalide',
        'already-evaluated' => 'Vous avez déjà évalué ce produit',
    ],

    'cart' => [
        'empty' => 'Le panier est vide',
        'over-stock' => 'Stock insuffisant : :product',
        'acmpr' => 'Une licence valide est requise pour acheter :product',
        'over-weight' => 'Poids maximal dépassé (maximum :max)',
        'under-price' => 'Valeur insuffisante (minimum :min avant taxes)',
        'veteran-limit' => 'Quantité maximale permise par vétéran pour :product atteinte (maximum :max)',
        'veteran-quota' => 'Quota mensuel de vétéran dépassé (maximum :max)',
        'user-limit' => 'Quantité maximale permise pour :product atteinte (maximum :max)',
        'user-quota' => 'Quota mensuel dépassé (maximum :max)',
        'expired' => 'Le panier a expiré, veuillez rafraîchir la page',
        'not-found' => 'Aucun panier trouvé pour cet utilisateur',
    ],

    'shipping' => [
        'unsupported-zipcode' => 'La livraison n\'est pas disponible pour ce code postal: :zipcode',
        'invalid' => 'La livraison est impossible le :date entre :time',
    ],

    'support_demand' => [
        'done' => 'Votre demande de support a été envoyée',
    ],
    'promo' => [
        'no-code' => 'Aucun code renseigné',
        'not-found' => 'Code promo invalide',
        'not-in-time-frame' => 'Code promo innactif',
        'under-minimum' => 'Le montant total (hors taxes) de votre commande est inférieur au minimum de :min $ requis pour ce code promo',
        'over-maximum' => 'Le montant total (hors taxes) de votre commande dépasse les :max $ autorisés pour ce code promo',
        'under-minimum-items' => 'Votre panier est inférieur au nombre de :min produits requis pour ce code promo',
        'over-maximum-items' => 'Votre panier dépasse le nombre de :max produits autorisés pour ce code promo',
        'used-too-many-times' => 'Vous avez atteint la limite de :max utilisations par personne pour ce code promo',
        'applied' => 'Code promo appliqué',
        'cant-apply' => 'Code promo ne peut pas être appliqué',
        'removed' => 'Code promo retiré',
    ],
    'password' => [
        'user_not_found' => 'Utilisateur introuvable',
        'token_not_found' => 'Lien de réinitialisation invalide',
    ],
    'moneris' => [
        'general_error' => 'Un problème est survenu lors du traitement de votre paiement. Veuillez réessayer ou utiliser un autre mode de paiement.',
        'declined' => 'Votre carte a été refusée par la banque émettrice. Veuillez essayer une autre méthode de paiement.',
        'expired_card' => 'Cette carte est expirée. Veuillez utiliser une autre carte.',
        'pin_error' => 'Trop de tentatives de NIP incorrectes. Veuillez contacter votre banque.',
        'security_issue' => 'Un problème de sécurité est survenu lors de votre transaction. Veuillez réessayer.',
        'unsupported_card' => 'Ce type de transaction n\'est pas pris en charge par votre carte.',
        'card_limitations' => 'Votre carte a des limitations d\'utilisation. Veuillez contacter votre banque.',
        'insufficient_funds' => 'Fonds insuffisants sur votre carte. Veuillez essayer une autre méthode de paiement.',
        'card_limit_exceeded' => 'Vous avez dépassé la limite de votre carte. Veuillez essayer une autre méthode de paiement.',
        'duplicate_transaction' => 'Cette transaction semble être un doublon. Veuillez attendre un moment avant de réessayer.',
        'usage_limit' => 'Vous avez atteint le nombre maximum de transactions autorisées pour cette carte.',
        'refund_limit' => 'Vous avez atteint le nombre maximum de remboursements autorisés.',
        'pin_required' => 'La saisie du NIP est requise pour cette carte. Veuillez essayer une autre méthode de paiement.',
        'bank_declined' => 'Cette transaction a été refusée par votre banque.',
        'invalid_card' => 'Numéro de carte invalide. Veuillez vérifier et réessayer.',
        'processing_error' => 'Un problème est survenu lors du traitement de votre carte de crédit. Veuillez vérifier vos informations.',
        'cvv_error' => 'Le code de sécurité (CVV) est incorrect. Veuillez vérifier et réessayer.',
        'timeout' => 'Le système de paiement a expiré. Veuillez réessayer.',
        'system_error' => 'Une erreur système est survenue lors du traitement de votre paiement. Veuillez réessayer plus tard.',
        'success' => 'Commande créée avec succès',
    ]
];
