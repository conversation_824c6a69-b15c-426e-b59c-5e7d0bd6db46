<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>{{ $subject }}</title>
    <style>
        @font-face {
            font-family: 'Inter';
            font-weight: normal;
            font-style: normal;
            font-display: block;
            src: url("fonts/Inter-Regular.ttf") format("woff2");
        }
        @font-face {
            font-family: 'Inter';
            font-weight: 600;
            font-style: normal;
            font-display: block;
            src: url("fonts/Inter-Bold.woff2") format("woff2");
        }
        @font-face {
            font-family: 'Inter';
            src: url('/fonts/Inter-Regular.woff2') format('woff2');
            font-weight: 400;
            font-style: normal;
        }
        @font-face {
            font-family: 'Inter';
            src: url('/fonts/Inter-SemiBold.woff2') format('woff2');
            font-weight: 500;
            font-style: normal;
        }
        @font-face {
            font-family: 'Montserrat';
            font-weight: normal;
            font-style: normal;
            font-display: block;
            src: url("fonts/Montserrat-Regular.ttf") format("truetype");
        }

        @font-face {
            font-family: 'Montserrat';
            font-weight: 500;
            font-style: normal;
            font-display: swap;
            src: url("fonts/Montserrat-Medium.ttf") format("truetype");
        }

        @font-face {
            font-family: 'Montserrat';
            font-weight: 600;
            font-style: normal;
            font-display: swap;
            src: url("fonts/Montserrat-SemiBold.ttf") format("truetype");
        }

        @font-face {
            font-family: 'Montserrat';
            font-weight: 700;
            font-style: normal;
            font-display: swap;
            src: url("fonts/Montserrat-SemiBold.ttf") format("truetype");
        }
        @page {
            margin-top: 160px;
            margin-right: 25px;
            margin-left: 25px;
            margin-bottom: 60px;
        }

        body {
            font-family: 'Inter', sans-serif;
            font-size: 12px;
            color: #333;
            margin: 0;
            padding: 0;
        }
        .header {
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .pdf-header {
            position: fixed;
            top: -140px;
            left: 0;
            right: 0;
            height: 140px;
        }

        .header img {
            max-width: 160px;
        }
        .clear {
            clear: both;
        }

        .section {
            margin-bottom: 25px;
        }

        .title {
            color: #017D38;
            font-family: 'Montserrat', sans-serif;
            font-size: 11px;
            font-weight: 700;
            line-height: 13px;
            text-transform: uppercase;
            margin-top: 0;
            letter-spacing: 0;
            margin-bottom: 6px;
        }
        .teedy-id {
            color: #017D38;
            font-size: 10px;
            font-weight: 600;
            line-height: 13px;
            margin-top: 6px;
            margin-bottom: 0;
        }
        .numberPhone {
            font-weight: 600;
            margin-top: 6px;
            margin-bottom: 0;
        }
        .two-cols {
            width: 100%;
        }

        .col {
            width: 48%;
            display: inline-block;
            vertical-align: top;
            color: #000;
            font-size: 10px;
            font-weight: 400;
            line-height: 13px;
        }

        .label {
            display: inline-block;
            min-width: 100px;
        }

        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .table th, .table td {
            padding: 6px;
            text-align: left;
        }

        .table th {
            background-color: #F5F6F2;
        }

        .totals {
            margin-top: 20px;
            width: 100%;
        }
        .shipSold {
            background-color: #F5F6F2;
            padding: 20px;
        }
        .totals .row {
            margin-bottom: 6px;
        }

        .totals .label {
            float: left;
        }

        .totals .value {
            float: right;
        }
        .pdf-footer {
            position: fixed;
            bottom: 0;
            left: 25px;
            right: 25px;
            padding-top: 10px;
            font-size: 12px;
        }
       
    </style>
</head>

<body>
     @php
        use Carbon\Carbon;
    @endphp
    <div class="pdf-header">
        <div class="header">
            <div style="width: 100%; margin-bottom: 10px; position: relative;">
                <div style="display: inline-block; vertical-align: middle;">
                    <img src="{{ public_path('images/teedy-logo.png') }}" alt="Teedy Logo" style="max-height: 50px;">
                </div>

                <div style="display: inline-block; vertical-align: middle; margin-left: 15px; font-size: 9.5px; font-weight: 600; color: #017D38;">
                    {!! __('mail.pdf.cannabis_delivery') !!}
                </div>

                <div style="float: right; text-align: center; background: #017D38; color: #FFFFFF; padding: 6px 14px; width: 150px;">
                    <p style="text-transform: uppercase; font-weight: 600; line-height: 13px; font-size: 15px; margin-top: 0; margin-bottom: 5px;">
                        {{ __('mail.order.order-2') }}
                    </p>
                    <span style="font-size: 13px;">{{ Str::substr($data->moneris_id,-6) }}</span>
                </div>
            </div>

            <div style="width: 50%; float: left; font-weight: 500;">
                <div>2225 Hymus<br> Montreal, QC H9P 1J8</div>
                <div>Canada</div>
                <div>Tel: +****************</div>
            </div>
            <div style="width: 50%; float: right; text-align: right;">
                {{ __('mail.pdf.order_date') }}
                <span style="margin-left: 22px; font-weight: 600;">
                    {{ Carbon::parse($data->date_in_progress)->format('Y/m/d') }}
                </span>
            </div>
            <div class="clear"></div>
        </div>
    </div>
    <div class="main">

        {{-- SOLD TO & SHIP TO --}}
        <div class="section shipSold">
            <div class="two-cols">
                <div class="col">
                    <p class="title">{{ __('mail.order.client_infos.billing_address') }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->user->firstname }} {{ $data->user->lastname }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->client_info->billing_address->address }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->client_info->billing_address->city }}, {{ $data->client_info->billing_address->postal_code }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->client_info->billing_address->province }}</p>
                   <p class="teedy-id">{{ __('mail.order.teedy_id') }}#{{ $data->user->userdetail->teedy_client_id ?? 'N/A' }}</p>
                    @if ($data->order_content->client_info->veteran_id)
                    <p class="teedy-id">{{ __('mail.order.veteran_id') }} #{{ $data->order_content->client_info->veteran_id }}</p>
                    @endif
                </div>

                <div class="col">
                    <p class="title">{{ __('mail.order.client_infos.shipping_address') }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->user->firstname }} {{ $data->user->lastname }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->client_info->shipping_address->address }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->client_info->shipping_address->city }}, {{ $data->client_info->shipping_address->postal_code }}</p>
                    <p style="margin-bottom: 5px; margin-top: 0;">{{ $data->client_info->shipping_address->province }}</p>
                    <p class="numberPhone">{{ $data->phone_formatted }}</p>
                </div>
            </div>
        </div>

        {{-- Patient Info --}}
        <div class="section">
            <div class="two-cols">
                <div class="col">
                    <p class="title">{{ __('mail.order.client_infos.title') }}</p>
                    <div style="margin-bottom: 5px;"><span class="label">{{ __('mail.order.client_infos.name') }}</span> <b style="font-weight: 600;">{{ $data->user->firstname }} {{ $data->user->lastname }}</b></div>
                    <div style="margin-bottom: 5px;"><span class="label">{{ __('mail.pdf.doc_name') }}</span> <b>{{ $data->prescription->doc_name }}</b></div>
                    <div style="margin-bottom: 5px;"><span class="label">{{ __('mail.order.client_infos.date_birth') }}</span> <b>{{ Carbon::parse($data->user->userdetail->birth_date)->format('Y/m/d') ?? 'N/A' }}</b></div>
                </div>
                <div class="col">
                    <p class="title">{{ __('mail.order.client_infos.title_3') }}</p>
                    <div style="margin-bottom: 5px;"><span class="label">{{ __('mail.pdf.prescription_doctor') }}</span><b> {{ $data->prescription->doc_name }}</b></div>
                    <div style="margin-bottom: 5px;"><span class="label">{{ __('mail.pdf.prescription_validity_date') }}</span> <b>{{ Carbon::parse($data->prescription->end_date)->format('Y/m/d') }}</b></div>
                    <div style="margin-bottom: 5px;"><span class="label">{{ __('mail.pdf.prescription_dosage') }}</span> <b>{{ $data->prescription->daily_dosage }}</b></div>
                </div>
            </div>
        </div>

        {{-- Order Table --}}
        <div class="section">
            <table class="table">
                <thead>
                    <tr>
                        <th>{{ __('mail.order.order-recap.product_code') }}</th>
                        <th>{{ __('mail.order.order-recap.product_name') }}</th>
                        <th>{{ __('mail.order.order-recap.quantity') }}</th>
                        <th>Quotas</th>
                        <th>{{ __('mail.order.order-recap.price') }}</th>
                        <th>{{ __('mail.order.order-recap.discount') }}</th>
                        <th>{{ __('mail.order.order-recap.final_price') }}</th>
                    </tr>
                </thead>
                <tbody>
                    @php
                        $totalQuota = 0;
                    @endphp
                   @foreach ($data->order_content->product_list as $product)
                        <tr>
                            <td>{{ $product->sku ?? '-' }}</td>
                            <td>{{ $product->product_description_bl ?? 'N/A' }}</td>
                            <td>{{ $product->quantity ?? 'N/A' }}</td>
                            <td>
                                @if (!empty($data->order_content->quota_weight))
                                    {{ number_format($data->order_content->quota_weight, 2) }} GR
                                @else
                                    N/A
                                @endif
                            </td>
                            <td>
                                {{ number_format($product->base_price ?? 0, 2) }}
                            </td>
                            <td>
                                @if ($product->discount != 0)
                                    {{ number_format($product->discount * 100, 0) }} %
                                @else
                                    -
                                @endif
                            </td>
                            <td>
                                {{ number_format(($product->product_price ?? 0) * ($product->quantity ?? 0), 2) }}
                            </td>
                        </tr>

                        @php
                            $totalQuota += $data->order_content->quota_weight ?? 0;
                        @endphp
                    @endforeach

                </tbody>
            </table>
        </div>
        {{-- Totals --}}
        <div class="pdf-footer">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%; vertical-align: top; font-size: 12px;">
                        <p class="title" style="font-size: 9.5px;">{{__('mail.order.thank_you')}}</p>
                    </td>

                    <td style="width: 50%;">
                        <table style="width: 100%; font-size: 12px; border-collapse: collapse; ">
                            <tr>
                                <td style="text-align: left; padding-bottom: 6px;">{{ __('mail.order.order-recap.total_before_tax') }}</td>
                                <td style="text-align: right; padding-bottom: 6px;"><b>{{ number_format($data->order_content->price_before_taxes, 2) }}</b></td>
                            </tr>
                            <tr>
                                <td style="text-align: left; padding-bottom: 6px;">{{ __('mail.order.order-recap.shipping') }}</td>
                                <td style="text-align: right; padding-bottom: 6px;"><b>{{ number_format($data->order_content->shipping_cost, 2) }}</b></td>
                            </tr>
                            @if (!empty($data->order_content->taxes))
                                @foreach ($data->order_content->taxes as $name => $value)
                                    <tr>
                                        <td style="text-align: left; padding-bottom: 6px;">
                                            {{ __('mail.order.order-recap.' . $name) }}
                                        </td>
                                        <td style="text-align: right; padding-bottom: 6px;">
                                            <b>{{ number_format($value, 2) }}</b>
                                        </td>
                                    </tr>
                                @endforeach
                            @else 
                                <tr>
                                    <td style="text-align: left; padding-bottom: 6px;">
                                        {{ __('mail.order.order-recap.taxes') }}
                                    </td>
                                    <td style="text-align: right; padding-bottom: 6px;">
                                        <b>{{ number_format($data->order_content->total_taxes, 2) }}</b>
                                    </td>
                                </tr>
                            @endif                        

                            @if (optional($data->order_content)->cart_discount)
                                <tr>
                                    <td style="text-align: left; padding-bottom: 6px;">
                                        {{ __('mail.order.order-recap.promo_code') }} <br>

                                        @if(isset($data->order_content->cart_discount_name))
                                            {{ $data->order_content->cart_discount_name }}
                                        @endif
                                        @if(isset($data->order_content->cart_discount_type))
                                            ({{ $data->order_content->cart_discount_type == 'percent' ? '-' . number_format($data->order_content->cart_discount_percentage  * 100, 0) . '%' : '-' . number_format($data->order_content->cart_discount, 2) . '$' }})
                                        @endif
                                    </td>
                                    <td style="text-align: right; padding-bottom: 6px;">
                                      <b>-{{ number_format($data->order_content->cart_discount, 2) }}</b>
                                    </td>
                                </tr>
                            @endif

                            <tr style="background: #017D38; font-size: 12px; color: #FFF; font-weight: 700;">
                                <td style="font-weight: bold; padding-top: 5px; padding: 5px;">{{ __('mail.order.order-recap.total') }}</td>
                                <td style="text-align: right; padding: 5px;">
                                    {{ number_format($data->order_content->final_order_price, 2) }} $
                                </td>
                            </tr>
                            <tr style="font-size: 10px;">
                                <td style="padding-top: 5px; padding: 5px;">{{ __('mail.order.order-recap.total_quotas') }}</td>
                                <td style="text-align: right; padding: 5px;">
                                   <b>{{ (isset($totalQuota) && is_numeric($totalQuota) && (float)$totalQuota != 0)
                                        ? number_format((float)$totalQuota, 2) . ' GR'
                                        : 'N/A' }}
                                    </b>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>
