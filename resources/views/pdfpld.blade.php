<!doctype html>
<html>

    <head>
        <meta name="viewport" content="width=device-width" />
        <meta content="IE=edge" http-equiv="X-UA-Compatible">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>{{ $subject }}</title>


        <style>

            @font-face {
                font-family: 'Montserrat';
                font-weight: normal;
                font-style: normal;
                font-display: block;
                src: url("fonts/Montserrat-Regular.ttf") format("truetype");
            }

            @font-face {
                font-family: 'Montserrat';
                font-weight: 500;
                font-style: normal;
                font-display: swap;
                src: url("fonts/Montserrat-Medium.ttf") format("truetype");
            }

            @font-face {
                font-family: 'Montserrat';
                font-weight: 600;
                font-style: normal;
                font-display: swap;
                src: url("fonts/Montserrat-SemiBold.ttf") format("truetype");
            }

            .page-break {
                page-break-after: always;
            }

            body {
                font-family: "Montserrat", sans-serif;
                width: 100% !important; 
                min-width: 100%; 
                font-size: 13px; 
                line-height: 19px; 
                margin: 0; 
                padding: 0;
            }

            b {
                font-family: "Montserrat", sans-serif;
                font-weight: 600;
            }

            .top {
                
            }

            .number {
                font-weight: 500;
                float: right;
                margin: 0;
            }

            .btn {
                padding: 10px 20px;
                background-color: #1C7F40;
                color: white;
                border-radius: 10px;
                text-decoration: none;
            }

            h1 {
                font-size: 24px;
                font-weight: 600;
            }

            h2 {
                font-size: 20px;
                font-weight: 600;
                padding-bottom: 15px;
                border-bottom: 2px solid #1C7F40;
                margin-bottom: 15px;
            }

            h3 {
                font-size: 16px;
                font-weight: 600;
            }

            .text {
                margin: 60px 0;
            }

            .text p {
                max-width: 400px;
                margin: 30px auto;
            }

            .item-line {
                padding: 10px 0 25px 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.15);
                height: 30px;
            }

            .item-line .item {
                float: left;
                width: 70%;
            }

            .item-line .price {
                float: right;
            }

            .recap {
                margin-top: 30px;
            }

            .recap-price {
                margin-top: 50px;
                height: 270px;
            }

            .recap-price p {
                padding: 0 0 15px 0;
                border-bottom: 1px solid rgba(0, 0, 0, 0.15); 
            }

            .recap-price p:last-child {
                border-bottom: none;
            }

            .section {
                margin-top: 40px;
            }

            .section.address{
                margin-top: 180px;
            }

            .client-info {

            }

            .client-info p {
                margin: 0;
            }

            .column {
                width: 50%;
            }

            .client-info .column:first-child, .recap-price .column:first-child {
                float: left;
            }

            .client-info .column:last-child, .recap-price .column:last-child {
                float: right;
            }

            .signature {
                margin-top: 200px;
                text-align: center;
            }
            .pdf-page {
                width: 794px; 
                min-height: 1123px; 
                margin: 20px auto; 
                padding: 40px;
                background: white;
                box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
            }
        </style>
    </head>

    <body>
        <div class="pdf-page">
        <div class="main">
            @php
                use Carbon\Carbon;
            @endphp

            <div class="top">
                
                <img style="max-width: 179px;" alt="Teedy-Logo" title="Teedy-Logo" src="{{ public_path('images/teedy-logo.png') }}"  />
                    
                <p>MEDICAL CANNABIS DELIVERY</p>
            
                <p class="number">
                    <br><span class="number">{{ __('mail.order.order-2') }} {{ Str::substr($data->moneris_id,-6) }}</span>
                    <br><span class="number">{{ __('mail.pdf.order_date') }} {{ Carbon::parse($data->date_in_progress)->format('Y-m-d') }}</span>
                </p>
                @if ($data->order_content->client_info->veteran_id)
                    <br><br><p class="veteran-number number">{{ __('mail.order.veteran_id') }} {{ $data->order_content->client_info->veteran_id }}</p>
                @endif
            </div>

            <div class="section">
                <h2>{{ __('mail.order.client_infos.title') }}</h2>
                <div class="client-info">
    
                    <div class="column">
                        <h3>{{ __('mail.order.client_infos.title_2') }}</h3>
                        <p>{{ $data->user->firstname }} {{ $data->user->lastname }}</p>
                        <p>{{ $data->user->email }}</p>
                        <p>{{ $data->user->userdetail->phone }}</p>
                    </div>
    
                    <div class="column">
                        <h3>{{ __('mail.order.client_infos.title_3') }}</h3>
                        <p>{{ __('mail.pdf.prescription_number') }} {{ $data->prescription->prescription_number }}</p>
                        <p>{{ __('mail.pdf.prescription_doctor') }} {{ $data->prescription->doc_name }}</p>
                        <p>{{ __('mail.pdf.prescription_validity_date') }} {{ $data->prescription->end_date }}</p>
                        <p>{{ __('mail.pdf.prescription_dosage') }} {{ $data->prescription->daily_dosage }}</p>
                    </div>
                </div>
            </div>

            <div class="section address">
                <h2>{{ __('mail.order.client_infos.title_address') }}</h2>
                <div class="client-info">
                    <div class="column">
                        <h3>{{ __('mail.order.client_infos.shipping_address') }}</h3>
                        <p>{{ $data->client_info->shipping_address->address }}</p>
                        <p>{{ $data->client_info->shipping_address->city}} {{ $data->client_info->shipping_address->postal_code}}</p>
                        <p>{{ $data->client_info->shipping_address->province }}</p>
                    </div>
                    
                    <div class="column">
                        <h3>{{ __('mail.order.client_infos.billing_address') }}</h3>
                        <p>{{ $data->client_info->billing_address->address }}</p>
                        <p>{{ $data->client_info->billing_address->city }} {{ $data->client_info->billing_address->postal_code }}</p>
                        <p>{{ $data->client_info->billing_address->province }}</p>
                    </div>
                </div>
            </div>

            <div class="page-break"></div>

            <div class="recap">
                <h2><b>{{ __('mail.order.order-recap.title') }}</b></h2>
        
                @foreach ($data->order_content->product_list as $product)
                    <div class="item-line">
                        <p class="item">{{ $product->name }} x {{ $product->quantity }}</p>
                        <p class="price"><b>{{ $product->product_price }} $</b></p>
                    </div>
                @endforeach
            </div>

            <div class="recap-price">
                <div class="column"></div>

                <div class="column">
                    <p>
                        <span>{{ __('mail.order.order-recap.total_before_tax') }}</span>
                        <span style="float: right;">{{ $data->order_content->price_before_taxes }} $</span>
                    </p>
                    <p>
                        <span>{{ __('mail.order.order-recap.shipping') }}</span>
                        <span style="float: right;">{{ $data->order_content->shipping_cost }} $</span>
                    </p>
                    <p>
                        <span>{{ __('mail.order.order-recap.taxes') }}</span>
                        <span style="float: right; margin: 0;">{{ $data->order_content->total_taxes }} $</span></br>
                    </p>
                    @if (optional($data->order_content)->promo)
                        <p>
                            <span>{{ __('mail.order.order-recap.promo_code') }}</span>
                            <span style="float: right;">
                                @if($data->order_content->promo->type == 'amount')
                                    {{ $data->order_content->promo->amount }} $
                                @elseif($data->order_content->promo->type == 'percent')
                                    {{ $data->order_content->promo->amount }} %
                                @endif
                            </span>
                        </p>
                    @endif
                    <p>
                        <span><b>{{ __('mail.order.order-recap.total') }}</b></span>
                        <span style="float: right;"><b>{{ number_format($data->order_content->final_order_price, 2) }} $</b></span>
                    </p>
                </div>
            </div>
        

            <p class="signature">{{ __('mail.layout.signature') }}</p>

        </div>
        </div>
    </body>

</html>

