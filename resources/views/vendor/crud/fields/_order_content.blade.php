<!-- textarea -->
@include('crud::fields.inc.wrapper_start')
@php
    $order_count = 0;
    $content_order = (!empty($field['value'])) ? json_decode($field['value']) : false;

@endphp
<label>{!! $field['label'] !!}</label>
    <input type="hidden" name="{{ $field['name'] }}" value="{{ old_empty_or_null($field['name'], '') ??  $field['value'] ?? $field['default'] ?? '' }}" />
    {{-- <textarea class="form-control"name="{{ $field['name'] }}">{{ old_empty_or_null($field['name'], '') ?? $field['value'] ?? $field['default'] ?? ''}}</textarea> --}}
    @if (!empty($content_order))
        
        @foreach ($content_order->product_list as $product)
            <p><b>{{ $product->sku ?? 'no sku'}}</b> | {{ $product->{'product_title_' . App::getLocale()} ?? 'no product_title_' . App::getLocale() }} - {{ $product->product_price ?? 'no product_price' }}$ x {{ $product->quantity ?? 'no quantity' }} = {{ $product->total ?? 'no total' }}</p>
        @endforeach
        <p>Nombre de produit : {{ $order_count }}</p>
        <p>Sous total : {{ $content_order->price_before_taxes }}</p>
        <p><b>{{ $content_order->shipping->{'name_' . App::getLocale()} ?? 'no name'}}</b> - {{ $content_order->shipping->price ?? 'no value' }}$</p>
        <p>Taxes : </p>
        @foreach ($content_order->taxes as $tax)
            {{-- <p><b>{{ $tax->name ?? 'no name'}}</b> | {{ $tax->value ?? 'no value' }}% => {{ ($tax->value/100)*$content_order->final_order_price }}</p> --}}
        @endforeach
    @endif
{{-- HINT --}}
@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif
@include('crud::fields.inc.wrapper_end')