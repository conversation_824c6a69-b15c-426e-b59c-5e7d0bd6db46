<!-- textarea -->
@include('crud::fields.inc.wrapper_start')
@php
    $client_info = (!empty($field['value'])) ? json_decode($field['value']) : false;
@endphp
<label>{!! $field['label'] !!}</label>
    <input type="hidden" name="{{ $field['name'] }}" value="{{ old_empty_or_null($field['name'], '') ??  $field['value'] ?? $field['default'] ?? '' }}" />
    {{-- <textarea class="form-control"name="{{ $field['name'] }}">{{ old_empty_or_null($field['name'], '') ?? $field['value'] ?? $field['default'] ?? ''}}</textarea> --}}
    @if (!empty($client_info))
        <p><b>Adresse de livraison</b></p>
        <p>Adresse: {{ $client_info->shipping_address->address }}</p>
        <p>Ville: {{ $client_info->shipping_address->city }}</p>
        <p>Code Postal: {{ $client_info->shipping_address->postal_code}} </p>
        <p>Province: {{ $client_info->shipping_address->province}}</p>

        <p><b>Adresse de facturation</b></p>
        <p>Adresse: {{ $client_info->billing_address->address }}</p>
        <p>Ville: {{ $client_info->billing_address->city }}</p>
        <p>Code Postal: {{ $client_info->billing_address->postal_code}} </p>
        <p>Province: {{ $client_info->billing_address->province}}</p>
    @endif
{{-- HINT --}}
@if (isset($field['hint']))
    <p class="help-block">{!! $field['hint'] !!}</p>
@endif
@include('crud::fields.inc.wrapper_end')
