<!-- field_type_name -->
@include('crud::fields.inc.wrapper_start')

@php
    $client_type = isset($entry)
        ? ($entry->userdetail->veteran ? 'veteran' : 'regular')
        : Route::current()->parameters['client_type'];

    $route_params = ['client_type' => $client_type, 'user_id' => isset($entry) ? $entry->id : null];
@endphp

<div class="card-head">
    @if (isset($entry))
        <a href="{{ route('prescription/{client_type}.create', $route_params) }}" class="btn btn-primary" data-style="zoom-in">
            <span class="ladda-label"><i class="la la-plus"></i> {{ __('teedy/users.prescriptions.add') }}</span>
        </a>
    @endif
</div>

<div class="card-body">
    <table class="table table-responsive-sm table-striped">
        <thead>
            <tr>
                <th class="align-top">{{ __('teedy/users.prescriptions.labels.code_admin') }}</th>
                <th class="align-top">{{ __('teedy/users.prescriptions.labels.number') }}</th>
                <th class="align-top">{{ __('teedy/users.prescriptions.labels.start_date') }}</th>
                <th class="align-top">{{ __('teedy/users.prescriptions.labels.end_date') }}</th>
                <th class="align-top">{{ __('teedy/users.prescriptions.labels.status') }}</th>
                <th class="align-top">Actions</th>
            </tr>
        </thead>
        <tbody>
            @if (isset($entry) && count($entry->prescriptions))
                @foreach ($entry->prescriptions as $prescription)
                    <tr>
                        <td>{{ $prescription->code_admin }}</td>
                        <td>{{ $prescription->prescription_number }}</td>
                        <td>{{ Carbon\Carbon::create($prescription->start_date)->format('d M Y') }}</td>
                        <td>{{ $prescription->end_date ? Carbon\Carbon::create($prescription->end_date)->format('d M Y') : null }}</td>
                        <td>
                            @php
                                $status_class = 'badge-info';
                                switch ($prescription->status) {
                                    case 'Approved':
                                        $status_class = 'badge-success';
                                        break;
                                    case 'Invalid':
                                    case 'Expired':
                                        $status_class = 'badge-danger';
                                        break;
                                    case 'Pending':
                                        $status_class = 'badge-warning';
                                        break;
                                    default:
                                        $status_class = 'badge-info';
                                        break;
                                }
                            @endphp
                            <span class="badge {{ $status_class }}">{{ __('common.prescriptions.status.' . $prescription->status) }}</span>
                        </td>
                        <td>
                            @php
                                $presc_params = array_merge($route_params, ['id' => $prescription->id]);
                            @endphp
{{-- 
                            <a href="{{ route('prescription/{client_type}.show', $presc_params) }}" class="btn btn-sm btn-link">
                                <i class="la la-eye"></i> {{ __('teedy/users.prescriptions.show') }}
                            </a> --}}
                            <a href="{{ route('prescription/{client_type}.edit', $presc_params) }}" class="btn btn-sm btn-link">
                                <i class="la la-edit"></i> {{ __('teedy/users.prescriptions.update') }}
                            </a>
                            {{-- <a href="{{ route('prescription/{client_type}.destroy', $presc_params) }}" class="btn btn-sm btn-link">
                                <i class="la la-edit"></i> {{ __('teedy/users.prescriptions.delete') }}
                            </a> --}}
                        </td>
                    </tr>
                @endforeach
            @else
                <td colspan="6"><h4 class="text-center">{{ __('teedy/users.prescriptions.no_result')}}</h4></td>
            @endif
        </tbody>
    </table>
</div>

{{-- HINT --}}
@if (isset($field['hint']))
<p class="help-block">{!! $field['hint'] !!}</p>
@endif
@include('crud::fields.inc.wrapper_end')

@if ($crud->fieldTypeNotLoaded($field))
    @php
    $crud->markFieldTypeAsLoaded($field);
    @endphp

    {{-- FIELD EXTRA CSS --}}
    {{-- push things in the after_styles section --}}
    @push('crud_fields_styles')
    <!-- no styles -->
    @endpush

    {{-- FIELD EXTRA JS --}}
    {{-- push things in the after_scripts section --}}
    @push('crud_fields_scripts')
    <!-- no scripts -->
    @endpush
@endif
