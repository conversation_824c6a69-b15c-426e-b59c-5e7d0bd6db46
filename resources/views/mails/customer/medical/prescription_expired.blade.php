@extends('mails.layout')

@section('title')
    {{ __('mail.prescription_invalid.subject')}}
@endsection


@section('content')

@include('mails.partials.banner', ['image' => 'overdue-', 'lang' => $data['user']->userdetail->language])

<!-- Content -->
<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px; padding-top: 0;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{!! __('mail.prescription_expired.baseline_1', [ 'firstname' => $data['firstname'], 'lastname' => $data['lastname'] ]) !!}</p>
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.prescription_expired.baseline_2') }}</p>
    </td>
</tr>
<tr style="padding: 0;">
    <td align="center" style="box-sizing: border-box;">
        <a href="{{ __('mail.urls.medical_profile_prescription', [], $data['user']->userdetail->language) }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.prescription_expired.user_cta')}}</a>
        <br><br>
        <a href="{{ __('mail.urls.medical_ocean', [], $data['user']->userdetail->language) }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.prescription_expired.user_cta_2')}}</a>
    </td>
</tr>

<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px 40px;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{!! __('mail.prescription_expired.baseline_3') !!}</p>
    </td>
</tr>

@include('mails.partials.footer')

<tr style="padding: 0;">
    <td align="center" style="padding: 20px 50px; border-top: 1px solid #e0e0e0;">
        <p style="font-size: 12px; color: #666; margin: 0;">
            {{ __('mail.unsubscribe.text') }}
            <a href="{{ env('APP_URL') }}/api/unsubscribe-expiry/{{ $data['user']->generateUnsubscribeToken() }}" 
               style="color: #008d4c; text-decoration: underline;">
                {{ __('mail.unsubscribe.link') }}
            </a>
        </p>
    </td>
</tr>

@endsection