@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')

@php
    $order_content = json_decode($data['order_content']);
@endphp

<!-- Content -->

<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 40px 50px; padding-top: 0;">
        <p style="font-size: 14px; padding-bottom: 20px; margin-top: -5px;">{{ __('mail.order.order', ['order' => $data['order'] ]) }}</p>
        <h2 style="margin-top: 0;">{{ __('mail.order.baseline_1') }}</h2>
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{!! __('mail.order.baseline_2', ['name' => $data['name'] ]) !!}</p>
        {{-- <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{!! __('mail.order.baseline_3', ['date' => \Carbon\Carbon::parse($order_content->delivery_date)->translatedFormat('j F Y'), 'time' => $order_content->delivery_time ]) !!}</p>    </td> --}}
    </td>
</tr>

<!-- Order recap array -->
@include('mails.partials.order-recap', ['admin' => false])

<tr>
    <td align="center" style="box-sizing: border-box;">
        <a href="{{ env('APP_URL') .'/' . $data['lang'] .'/api/user/order/generate/' . $data['moneris_id'] }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.order.admin.cta')}}</a>
    </td>
</tr>


@include('mails.partials.footer', ['lang' => $data['lang']])    

@endsection