@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')

@if($data['status'] == 'shipped')
    @include('mails.partials.banner', ['image' => $data['status'] . '_', 'lang' => $data['user']->language])
@endif

<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px; padding-top: 0;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.status.hello') }} <span style="text-transform: uppercase;color: #008d4c;font-weight: 600;"> {{ __('mail.status.hello_name', [ 'firstname' => $data['firstname'], 'lastname' => $data['lastname'] ]) }}</span></p>
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;color: #008d4c;">
            {!! __('mail.status.' . $data['status'] . '.baseline_1') !!}
        </p>
        </td>
    </tr>

@include('mails.partials.footer')

@endsection
