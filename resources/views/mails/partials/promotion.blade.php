<style>
    a {
        color: #008d4c;
        text-decoration: underline;
        font-weight: 600;
    }
    .promo-code strong {
        color: #008d4c;
        font-weight: 600;
    }
</style>
<td align="center" valign="top" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 20px 50px;">
    <table border="0" cellpadding="0" cellspacing="0" width="100%" style="border-spacing: 0; border-collapse: collapse;">
        <tr style="padding: 0; border-top: 1px solid rgba(0, 0, 0, 0.3);">
            <td width="50%" align="left" valign="top" style="padding: 0 10px;text-align: left;padding: 50px 0;">
                <img alt="Teedy-Logo" title="Teedy-Logo" src="{{ asset('images/mails/icons/laptop.png') }}" style="-ms-interpolation-mode: bicubic; width: 100% !important; max-width: 100%; outline: none; text-decoration: none; vertical-align: middle; border: none;" />
            </td>
            <td width="50%" align="left" valign="middle" style="padding: 0 10px;text-align: center;">
                @if(isset($promo) && $promo)
                    <p class="promo-code" style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 400;">{!! __('mail.promotion.laptop.promo_code') !!}</p>
                @else
                    <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 600;color: #008d4c;">{!! __('mail.promotion.laptop.baseline_1') !!}</p>
                    <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 400;">{!! __('mail.promotion.laptop.baseline_2') !!}</p>
                    <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;color: #008d4c;">{!! __('mail.promotion.laptop.baseline_3') !!}</p>
                @endif
            </td>
        </tr>
        <tr style="padding: 0; border-top: 1px solid rgba(0, 0, 0, 0.3);">
            <td width="50%" align="left" valign="top" style="padding: 0 10px;text-align: left;padding: 50px 0;">
                <img alt="Teedy-Logo" title="Teedy-Logo" src="{{ asset('images/mails/icons/bag.png') }}" style="-ms-interpolation-mode: bicubic; width: 100% !important; max-width: 100%; outline: none; text-decoration: none; vertical-align: middle; border: none;" />
            </td>
            <td width="50%" align="left" valign="middle" style="padding: 0 10px;text-align: center;">
                <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 600;color: #008d4c;">{!! __('mail.promotion.bag.baseline_1') !!}</p>
                <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 400;">{!! __('mail.promotion.bag.baseline_2') !!}</p>
                <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 400;">{!! __('mail.promotion.bag.baseline_3') !!}</p>
            </td>
        </tr>
        <tr style="padding: 0; border-top: 1px solid rgba(0, 0, 0, 0.3);">
            <td width="50%" align="left" valign="top" style="padding: 0 10px;text-align: left;padding: 50px 0;">
                <img alt="Teedy-Logo" title="Teedy-Logo" src="{{ asset('images/mails/icons/truck.png') }}" style="-ms-interpolation-mode: bicubic; width: 100% !important; max-width: 100%; outline: none; text-decoration: none; vertical-align: middle; border: none;" />
            </td>
            <td width="50%" align="left" valign="middle" style="padding: 0 10px;text-align: center;">
                <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 600;color: #008d4c;">{!! __('mail.promotion.truck.baseline_1') !!}</p>
                <p style="font-size: 18px; line-height: 19px; padding-bottom: 10px; margin: 0; text-align: left;font-weight: 400;">{!! __('mail.promotion.truck.baseline_2') !!}</p>
            </td>
        </tr>
    </table>
</td>