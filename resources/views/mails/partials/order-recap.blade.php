<tr>
    <td style="margin: 0; padding: 0 50px 0">
        <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" >
            <tr>
                <td>
                    <h2>{{ __('mail.order.admin.resume') }}</h2>
                    <hr>
                </td>
            </tr>
            <tr>
                <td>
                    <table width="100%" border="0" vertical-align="top" cellspacing="0" cellpadding="5" style="text-align: left;">
                        <tr>
                            <th style="padding-bottom: 16px; font-weight: 500;">{{ __('mail.order.admin.products') }}</th>
                            <th align="right" style="padding-bottom: 16px; font-weight: 500;">{{ __('mail.order.admin.quantity') }}</th>
                            <th align="right" style="padding-bottom: 16px; font-weight: 500;">{{ __('mail.order.admin.price') }}</th>
                        </tr>
                        
                        @foreach($order_content->product_list as $product)
                            <tr style="vertical-align: top;">
                                <td width="60%" style="font-weight: bold;">{{ $product->product_title_fr }}<br><small style="font-weight: normal; color:grey">SKU: {{ $product->sku }}</small></td>
                                <td align="right" style="font-size: 14px;">
                                    {{ $product->quantity }} x {{ number_format($product->product_price, 2) }} $
                                    @if(!$data['veteran'] && $product->product_price != $product->base_price)
                                        <br>
                                        <small style="font-weight: normal; color:#E03A3A; text-decoration: line-through;">({{ $product->quantity . ' x ' . number_format($product->base_price, 2) }} $)</small>
                                    @endif
                                </td>
                                <td align="right" style="font-size: 14px;">
                                    {{ number_format($product->total, 2) }} $
                                    @if(!$data['veteran'] && $product->total != $product->base_total)
                                        <br>
                                        <small style="font-weight: normal; color:#E03A3A; text-decoration: line-through;">({{ number_format($product->base_total, 2) }} $)</small>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </table>
                    <hr>
                    <table width="100%" border="0" cellspacing="0" cellpadding="5">
                        <tr>
                            <td>{{ __('mail.order.admin.subtotal') }}</td>
                            <td style="font-size: 14px;" align="right">{{ number_format($order_content->price_before_taxes, 2) }} $</td>
                        </tr>
                        @if($order_content->cart_discount > 0)
                            <tr>
                                <td>{{ __('mail.order.admin.discount') }}</td>
                                <td style="font-size: 14px;" align="right">-{{ number_format($order_content->cart_discount, 2) }} $</td>
                            </tr>
                        @endif
                        <tr>
                            <td>{{ __('mail.order.admin.shipping') }}</td>
                            <td style="font-size: 14px;" align="right">{{ number_format($order_content->shipping_cost, 2) > 0 ? number_format($order_content->shipping_cost, 2) . '$' : __('mail.order.admin.shipping_free') }}</td>
                        </tr>
                        <tr>
                            <td>{{ __('mail.order.admin.tax') }}</td>
                            <td style="font-size: 14px;" align="right">{{ number_format($order_content->total_taxes, 2) }} $</td>
                        </tr>
                        <tr>
                            <td><strong>{{ __('mail.order.admin.total') }}</strong> {{ $order_content->client_info->veteran_id ? __('mail.order.admin.veteran') : '' }}</td>
                            <td style="font-size: 14px;" align="right">
                                @if(!$data['veteran'] && !empty($order_content->client_info->veteran_id) && !empty($order_content->final_order_price_veteran))
                                    <span style="text-decoration: line-through;">{{ number_format($order_content->final_order_price_veteran, 2) }}$</span>
                                @endif
                                <strong>{{ number_format($order_content->final_order_price, 2) }} $</strong>
                            </td>
                        </tr>
                    </table>
                    <hr>
                </td>
            </tr>
            <tr>
                <td style="padding-top: 30px;">
                    <strong>{{ __('mail.order.admin.shipping_type') }}</strong><br>
                    {{ __('mail.order.admin.shipping_type_' . $data['shipping_type']) }}
                    @if(!\Carbon\Carbon::parse($order_content->delivery_date)->isToday() && $data['shipping_type'] != '24h')
                        <strong> - planifiée </strong>
                    @endif
                    <br>
                    {{ __('mail.order.admin.shipping_prevision') }} 
                    <strong>
                        {{ \Carbon\Carbon::parse($order_content->delivery_date)->translatedFormat('j F Y') }}
                        @if($data['shipping_type'] != '24h')
                            - {{ $order_content->delivery_time }}
                        @endif
                    </strong>
                </td>
            </tr>
            <tr>
                <td>
                    <p><strong>{{ __('mail.order.admin.shipping_address') }}</strong><br>
                        {{ $data['name'] }}<br>
                        {{$data['address']->address}}<br>
                        {{$data['address']->city}}, {{$data['address']->province}} {{ preg_replace('/^([A-Z]\d[A-Z])(\d[A-Z]\d)$/', '$1 $2', $data['address']->pc) }}<br>
                        Canada</p>
                    @if($admin)
                        @if(!$data['veteran'] && $order_content->client_info->veteran_id)
                            <p><strong>{{ __('mail.order.admin.veteran_code') }}</strong> {{ $order_content->client_info->veteran_id }}</p>
                        @endif
                        <p><strong>{{ __('mail.order.admin.phone') }}</strong> {{ preg_replace('/^(\d{3})(\d{3})(\d{4})$/', '$1 $2 $3', $data['phone']) }}</p>
                        <p><strong>{{ __('mail.order.admin.email') }}</strong> <a style="color: #008d4c;" href="mailto:{{ $data['email'] }}">{{ $data['email'] }}</a></p>
                    @endif
                </td>
            </tr>
        </table>
    </td>
</tr>