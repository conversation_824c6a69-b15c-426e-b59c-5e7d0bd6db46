@extends('mails.layout')

@section('title')
    {{ __('mail.veteran.quota_warning_remaining.subject')}}
@endsection

@section('content')
<!-- Content -->
<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px 40px;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.veteran.quota_warning_remaining.baseline_1', [ 'firstname' => $user['firstname']]) }}</p>
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.veteran.quota_warning_remaining.baseline_2', [ 'quota' => $user['quota_veteran_remaining']]) }}</p>
    </td>
</tr>
<tr style="padding: 0;">
    <td align="center" style="box-sizing: border-box;">
        <a href="{{ env('FRONT_URL') . '/' . $user->language . ($user->language == 'fr' ?'/boutique' : '/store') }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.veteran.quota_warning_remaining.user_cta')}}</a>
    </td>
</tr>

@include('mails.partials.footer')
@endsection