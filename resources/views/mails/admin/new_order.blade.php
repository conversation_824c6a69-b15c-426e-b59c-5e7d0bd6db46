@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')

@php
    $order_content = json_decode($data['order_content']);
@endphp


<tr style="padding: 0;">
    <td valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px 40px;">
        <p style="font-size: 18px; margin: 0;">{!! __('mail.order.admin.baseline_2', ['name' => $data['name'], 'order' => $data['order'] ]) !!}</p>
    </td>
</tr>

<!-- Order recap array -->
@include('mails.partials.order-recap', ['admin' => true])

<tr style="padding-top: 30px;">
    <td align="center" style="box-sizing: border-box; padding-top: 30px;">
        <a href="{{ env('APP_URL') .'/' . $data['lang'] .'/api/user/order/generate/' . $data['moneris_id'] }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.order.admin.cta')}}</a>
    </td>
</tr>
@endsection