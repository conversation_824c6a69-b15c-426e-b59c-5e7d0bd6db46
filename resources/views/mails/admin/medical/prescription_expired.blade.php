@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')
<!-- Content -->
<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px 40px;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.prescription_expired_admin.baseline_1', [ 'firstname' => $data['firstname'], 'lastname' => $data['lastname'] ]) }}</p>
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.prescription_expired_admin.baseline_2') }}</p>
    </td>
</tr>
<tr style="padding: 0;">
    <td align="center" style="box-sizing: border-box;">
        @if (isset($data['veteran']) && $data['veteran'])
            <a href="{{ env('APP_URL') . '/admin/client/veteran/' . $data['id'] . '/edit' }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.prescription_expired_admin.user_cta')}}</a>
        @else
            <a href="{{ env('APP_URL') . '/admin/client/regular/' . $data['id'] . '/edit' }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.prescription_expired_admin.user_cta')}}</a>
        @endif
    </td>
</tr>
@endsection