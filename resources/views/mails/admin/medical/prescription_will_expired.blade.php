@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')

@include('mails.partials.banner', ['image' => 'overdue-', 'lang' => 'fr'])

<!-- Content -->
<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px; padding-top: 0;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.prescription_will_expired_admin.baseline_1') }}</p>
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;font-weight: 600;color: #008d4c;">{{ __('mail.prescription_will_expired_admin.baseline_2', ['firstname' => $data['firstname'], 'lastname' => $data['lastname'], 'nb_jour' => (int) $data['nb_jour']]) }}</p>
        {{-- <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.prescription_will_expired_admin.baseline_3') }}</p> --}}
        
        <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-left: 4px solid #008d4c;">
            <h3 style="font-size: 20px; margin-bottom: 15px; color: #008d4c;">{{ __('mail.prescription_will_expired_admin.user_info.title') }}</h3>
            <p style="font-size: 16px; margin: 5px 0;"><strong>{{ __('mail.prescription_will_expired_admin.user_info.name') }}:</strong> {{ $data['firstname'] }} {{ $data['lastname'] }}</p>
            <p style="font-size: 16px; margin: 5px 0;"><strong>{{ __('mail.prescription_will_expired_admin.user_info.email') }}:</strong> {{ $data['user']->email }}</p>
            <p style="font-size: 16px; margin: 5px 0;"><strong>{{ __('mail.prescription_will_expired_admin.user_info.status') }}:</strong> {{ ucfirst($data['user']->status) }}</p>
            @if($data['user']->userdetail->veteran)
                <p style="font-size: 16px; margin: 5px 0;"><strong>{{ __('mail.prescription_will_expired_admin.user_info.veteran') }}:</strong> {{ __('mail.prescription_will_expired_admin.user_info.yes') }}</p>
                <p style="font-size: 16px; margin: 5px 0;"><strong>{{ __('mail.prescription_will_expired_admin.user_info.veteran_id') }}:</strong> {{ $data['user']->userdetail->veteran_id ?? 'N/A' }}</p>
            @endif
            <p style="font-size: 16px; margin: 5px 0;"><strong>{{ __('mail.prescription_will_expired_admin.user_info.days_remaining') }}:</strong> {{ (int) $data['nb_jour'] }} {{ __('mail.prescription_will_expired_admin.user_info.days') }}</p>
        </div>
        
        <p style="font-size: 18px; padding-top: 20px; margin: 0;">{{ __('mail.prescription_will_expired_admin.baseline_4') }}</p>
    </td>
</tr>
<tr style="padding: 0;">
    <td align="center" style="box-sizing: border-box;">
        <a href="{{ config('backpack.base.route_prefix') ? url(config('backpack.base.route_prefix') . '/user/' . $data['user']->id . '/edit') : '#' }}" class="button button-primary" target="_blank" style="box-sizing: border-box; border-radius: 8px; color: #fff; display: inline-block; text-decoration: none; -webkit-text-size-adjust: none; background-color: #008d4c; border-top: 10px solid #008d4c; border-right: 18px solid #008d4c; border-bottom: 10px solid #008d4c; border-left: 18px solid #008d4c;">{{ __('mail.prescription_will_expired_admin.user_cta')}}</a>
    </td>
</tr>

@include('mails.partials.footer')  

@endsection