@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')

    <tr style="padding: 0;">
        <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 0 50px 0;">
            <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">Voici les produits qui sont tombés à zéro en stock au cours de la journée :</p>
        </td>
    </tr>

    <tr style="padding: 0;">
        <td style="margin: 0; padding: 0 50px 0">
            <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" >
                <tr>
                    <td>
                        <h3>Liste des produits</h3>
                        <hr>
                    </td>
                </tr>
                <tr>
                    <td>
                        <table width="100%" border="0" vertical-align="top" cellspacing="0" cellpadding="5" style="text-align: left;">
                            <tr>
                                <th style="padding-bottom: 16px; font-weight: 500;">{{ __('mail.order.admin.products') }}</th>
                                <th align="right" style="padding-bottom: 16px; font-weight: 500;">SKU</th>
                            </tr>
                            @foreach ($products as $product)
                                <tr style="vertical-align: top;">
                                    <td width="60%" style="font-weight: 500;">{{ $product->title_fr }}</td>
                                    <td align="right" style="font-size: 14px;">
                                        {{ $product->sku }}
                                    </td>
                                </tr>
                            @endforeach
                        </table>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
@endsection