@extends('mails.layout')

@section('title')
    {{ $subject }}
@endsection

@section('content')
<!-- Content -->
<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 50px 50px 40px;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.support.baseline_1') }}</p>
    </td>
</tr>

<!-- Content -->
<tr style="padding: 0;">
    <td align="left" valign="top" class="content" style="word-break: break-word; -webkit-hyphens: auto; -ms-hyphens: auto; hyphens: auto; border-collapse: collapse !important; mso-table-lspace: 0pt; mso-table-rspace: 0pt; padding: 0px 50px 40px;">
        <p style="font-size: 18px; padding-bottom: 10px; margin: 0;">{{ __('mail.support.baseline_2') }}</p>
        @foreach ($data as $key => $item)
            @if ($key !== 'recaptcha_token')
                <p><span style="font-weight: bold">@lang('mail.support.'.$key) </span><br>
                    @if ($item == null)
                        NC
                        @continue
                    @endif
                    {{ $item }}
                </p>
            @endif
        @endforeach
    </td>
</tr>
@endsection
