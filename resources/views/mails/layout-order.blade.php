<!doctype html>
<html>

    <head>
        <meta name="viewport" content="width=device-width" />
        <meta content="IE=edge" http-equiv="X-UA-Compatible">
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>@yield('subject')</title>

        <style>
            body {
                font-family: 'Mont<PERSON><PERSON>', Arial, sans-serif; 
                width: 100% !important; 
                min-width: 100%; 
                font-size: 16px; 
                line-height: 1.4; 
                margin: 0; 
                padding: 0;
                background-color: "#ecf0f5"
            }

            .main {
                width: 100%; 
                min-width: 580px;
            }

            .body {
                width: 100% !important; 
                min-width: 100%; 
                -webkit-text-size-adjust: 100%; 
                -ms-text-size-adjust: 100%; 
                border-spacing: 0; 
                border-collapse: collapse; 
                table-layout: fixed; 
                mso-table-lspace: 0pt; 
                mso-table-rspace: 0pt; 
                font-size: 13px; 
                line-height: 19px; 
                margin: 0; 
                padding: 0;
            }
        
            .email-container {
                width: 100% !important; 
                min-width: 100%; 
                -webkit-text-size-adjust: 100%; 
                -ms-text-size-adjust: 100%; 
                word-break: break-word; 
                -webkit-hyphens: auto; 
                -ms-hyphens: auto; 
                hyphens: auto; 
                border-collapse: collapse !important; 
                mso-table-lspace: 0pt; 
                mso-table-rspace: 0pt; 
                margin: 0; 
                padding: 20px 10px;
            }

            .email-body {
                border-spacing: 0; 
                border-collapse: collapse; 
                table-layout: auto; 
                mso-table-lspace: 0pt; 
                mso-table-rspace: 0pt; 
                border-radius: 8px; 
                -webkit-box-shadow: 1px 2px 5px rgba(1,1,1,0.16); 
                box-shadow: 1px 2px 5px rgba(1,1,1,0.16); 
                padding: 0;
                background-color: #fff;
            }

            .header {
                style="word-break: break-word; 
                -webkit-hyphens: auto; 
                -ms-hyphens: auto; 
                hyphens: auto; 
                border-collapse: collapse !important; 
                mso-table-lspace: 0pt; 
                mso-table-rspace: 0pt; 
                padding: 40px 40px 0 40px;
            }

            .image {
                -ms-interpolation-mode: bicubic; 
                width: 107px !important; 
                /* max-width: 100%;  */
                outline: none; 
                text-decoration: none; 
                vertical-align: middle; 
                height: 35px !important; 
                border: none;"
            }

            tr {
                padding: 0;
            }

            td {
                style="word-break: break-word; 
                -webkit-hyphens: auto; 
                -ms-hyphens: auto; 
                hyphens: auto; 
                border-collapse: collapse !important; 
                mso-table-lspace: 0pt; 
                mso-table-rspace: 0pt; 
                padding: 20px 50px 10px;
            }

            p {
                margin: 0;
            }

            h1 {
                font-size: 24px; 
                font-weight: 600;
                text-align: center;
                margin: 60px 0 20px;
            }

            h2 {
                font-size: 20px;
                font-weight: 600;
                border-bottom: #1C7F40 solid 2px;
                padding-bottom: 15px;
                margin-bottom: 0;
            }
            
            h3 {
                font-size: 16px;
                font-weight: 600;
                margin: 10px 0;
            }

            .button {
                style="box-sizing: border-box; 
                border-radius: 8px; 
                color: #fff; display: inline-block; 
                text-decoration: none; 
                -webkit-text-size-adjust: none; 
                background-color: #1C7F40; 
                border-radius: 8px;
                padding: 10px 20px 8px 20px;
            }

            /* specific */

            .order-header {
                width: 100%;
                padding: 40px 40px 0 40px;
            }
            .number {
                font-weight: 500;
                text-align: end;
            }

            .order-msg {
                text-align: center;
            }
            .product-info {
                padding: 20px 15px 20px 0px;
            }

            .product-info-price {
                padding: 10px 0px 10px 15px;
            }

            .product-list {
                border-bottom: rgba(1,1,1,0.15) 1px solid;
            }
            .product-list .product-info {
                width: 480px
            }
            .block {
                display: block;
                width: 540px;
                margin: 0 50px;
            }
            .total {
                margin-top: 40px; 
                padding: 0;
            }

            .total-desc {
                padding: 0px 0 0 280px !important;
                width: 200px;
            }

            .total td {
                padding: 0;
            }

            .total td p {
                padding: 5px 0;
            }

            .shipping {
                padding: 0 100px 0 0;
            }

            .billing {
                padding: 0 0 0 10px;
            }

            .signature { 
                margin-top: 40px;
                margin-bottom: 20px;
            }
        </style>
    </head>

    <body>
        <div class="main">
            <table border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" class="body">
                <tr>
                    <td align="center" valign="top" class="email-container">

                        <table border="0" cellpadding="0" cellspacing="0" width="580" class="email-body">

                            <!-- Header -->
                            <tr style="display:block;">
                                <td class="header">
                                    <img class="image" style="max-width: 179px;" alt="Teedy-Logo" title="Teedy-Logo" src="{{ asset('images/teedy-logo.png') }}" />
                                </td>
                                <td class="order-header">
                                    <p class="number">{{ __('mail.order.order') }} {{ $data['order_nb'] }}</p>
                                </td>
                            </tr>

                            @yield('content')

                            <tr>
                                <td colspan="2" align="center">
                                    <p class="signature">{{ __('mail.layout.signature') }}</p>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
        </div>
    </body>

</html>
