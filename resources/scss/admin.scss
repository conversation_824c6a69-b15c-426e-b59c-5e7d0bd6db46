.app-header {
    background-color: #176935 !important;
    a.nav-link.dropdown-toggle {
        color: #fff !important;
        &:hover {
            color: #fff;
            opacity: .8;
        }
    }
}
.app-header.bg-light .navbar-brand {
    background-color: #fff;
    opacity: 1;
    transition: all 0.2s;

    img { margin: 0 auto; }

    &:hover {
        opacity: .9;
    }
}
a, .btn-link {
    color: #1c7f40;
    &:hover {
        color: #176935;
    }
}
.btn-primary {
    background-color: #176935;
    border-color: #176935;
    border-radius: 30px;
    &:hover {
        background-color: #1c7f40;
        border-color: #1c7f40;
    }
}
.btn-primary, .page-item.active .page-link {
    background-color: #176935;
    border-color: #176935;
    &:hover {
        background-color: #1c7f40;
        border-color: #1c7f40;
    }
}

.sidebar.sidebar-pills {
    a.nav-link, .nav-icon {
        color: #1c7f40 !important;
        &:hover {
            color: #176935 !important;
        }
    }
    .nav-item.open a.nav-link {
        color: #176935 !important;
        &:hover {
            color: #1c7f40 !important;
        }
        i.nav-icon {
            color: #176935 !important;
            &:hover {
                color: #1c7f40 !important;
            }
        }
    }
    hr {
        border-color: rgb(28, 127, 64, .2);
        width: 100%;
    }
}
.bg-success {
    background-color: #176935 !important;

    a.nav-link, .nav-icon {
        color: #fff !important;
        &:hover {
            color: #1c7f40 !important;
        }
        i.nav-icon {
            color: #fff !important;
            &:hover {
                color: #1c7f40 !important;
            }
        }
    }
}
.btn-outline-primary {
    color: #176935 !important;
    border-color: #176935 !important;
    &:hover {
        background-color: #176935 !important;
    }
    a.nav-link, .nav-icon {
        color: #fff !important;
        background-color: #176935 !important;
        border-color: #176935 !important;
        &:hover {
            color: #1c7f40 !important;
        }
        i.nav-icon {
            color: #fff !important;
            &:hover {
                color: #1c7f40 !important;
            }
        }
    }
}
.pace .pace-progress {
    background-color: #1c7f40 !important;

}
.form-control {
    &:focus {
        border-color: #176935;
        box-shadow: 0 0 0 1px #176935;
    }
}
.dropdown-item.active, .dropdown-item:active {
    background-color: transparent;
    color: #176935;
    i.nav-icon {
        color: #176935 !important;
        &:hover {
            color: #1c7f40 !important;
        }
    }
}

// readonly input
input[readonly="readonly"] {
    background-color: #f8f9fa;
    color: #495057;
    cursor: not-allowed;
}
