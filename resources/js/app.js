require('./bootstrap');

// window.addEventListener('load',function(){
const slugify = require('slugify');
const slugify_options = { strict: true, lower: true }

// Auto-fill slug fields based on related titles
document.querySelectorAll('input[name^="slug_"]').forEach(i => {
  const lang = i.name.slice(-2);
  const titleField = document.querySelector(`input[name^="title_${ lang }"]`);

  // Enable auto-replace slug only if the field is initially empty
  if (!i.value) {
    titleField.addEventListener('input', e => i.value = slugify(e.target.value, slugify_options));
  }
})

// Add a label on range fields to show current value
document.querySelectorAll('input[type="range"]').forEach(i => {
  const output = document.createElement('output');
  output.classList.add('range');
  output.innerHTML = i.value;
  i.parentNode.append(output);
  i.addEventListener('input', () => i.parentNode.lastChild.innerHTML = i.value)
})

//
// const container = document.querySelector('.product-detail-subform > .container-repeatable-elements > div');
// console.log(container.querySelector('div'));

// document.querySelectorAll('input[name*="productdetail[-1][title_"]').forEach(i => {
// document.querySelectorAll('input[name^="productdetail"][name*="title_"]').forEach(i => {
  // const lang = i.name.replaceAll(']', '').slice(-2);
  // const ref = document.querySelector(`input[name*="variation_title_${ lang }"]`);
  // i.addEventListener('change', () => {console.log('oidsuhf')});
  // i.addEventListener('input', e => ref.value = e.target.value);
  // console.log(i.value);
// });

// const blop = document.querySelector('input[name*="productdetail[-1][title_fr"]');
// blop.addEventListener('input', function() { console.log('BLOP!') })
// console.log(blop);
// });

// $(function() {
//   $('input').on('click', function () { console.log('blopsdf')})
//   // blop.addEventListener('input', function() { console.log('BLOP!') })
// });
