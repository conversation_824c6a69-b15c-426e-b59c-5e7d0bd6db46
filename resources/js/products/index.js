// crud.field('productdetail').subfield('title_fr').onChange(function(field) {
//   console.log('sdifuh');
// }).change();

//
// const container = document.querySelector('.product-detail-subform > .container-repeatable-elements > div');
// console.log(container.querySelector('div'));

// document.querySelectorAll('input[name*="productdetail[-1][title_"]').forEach(i => {
// // document.querySelectorAll('input[name^="productdetail"][name*="title_"]').forEach(i => {
//   // const lang = i.name.replaceAll(']', '').slice(-2);
//   // const ref = document.querySelector(`input[name*="variation_title_${ lang }"]`);
//   // i.addEventListener('input', e => console.log('oidsuhf'));
//   i.addEventListener('input', e => console.log('sdjkfh'));
//   console.log(i);
// });