<?php

namespace Tests\Browser;

use App\Models\User;
use Tests\DuskTestCase;
use <PERSON><PERSON>\Dusk\Browser;
use Illuminate\Foundation\Testing\DatabaseMigrations;

class LoginTest extends DuskTestCase
{
    /**
     * A Dusk test example.
     */
    public function testExample(): void
    {
        $this->browse(function (Browser $browser) {
            // $browser->visit('/admin/login')
            //         ->assertSee('Kryzalid');
            $browser->loginAs(User::find(1))
            ->visit('/admin/login')
            ->assertSee('Bienvenue!');
        });
    }
}
