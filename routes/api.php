<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\ProductsApiController;
use App\Http\Controllers\Api\UserApiController;
use App\Http\Controllers\Api\PrescriptionApiController;
use App\Http\Controllers\Api\CartApiController;
use App\Http\Controllers\Api\ShippingApiController;
use App\Http\Controllers\Api\OrdersApiController;
use App\Http\Controllers\Api\CustomPageApiController;
use App\Http\Controllers\Api\ProducersApiController;
use App\Http\Controllers\Api\PromoApiController;
use App\Http\Controllers\Api\ContactApiController;
use App\Http\Controllers\Api\PdfApiController;
use App\Http\Controllers\Api\GlobalContentApiController;
use App\Http\Controllers\Api\StaticSettingApiController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// User
Route::post('login', [UserApiController::class, 'login'])->name('login');
Route::post('register', [UserApiController::class, 'store']);
Route::post('/reset-password', [UserApiController::class, 'resetPassword']);
Route::post('/recover-password', [UserApiController::class, 'changePassword']);

    // Protected by bearer token
    Route::middleware('auth:sanctum')->group(function () {
        // User
        Route::get('/user', [UserApiController::class, 'show']);
        Route::get('/logout', [UserApiController::class, 'logout']);
        Route::get('/check', [UserApiController::class, 'checkToken']);
        Route::post('/user/delete', [UserApiController::class, 'deleteAccount']);
        Route::post('/user/language', [UserApiController::class, 'changeLanguage']);
        Route::get('/user/shipping-schedule', [UserApiController::class, 'getShipping']);
        Route::get('/user/orders', [UserApiController::class, 'getOrders']);
        // Route::post('/user/edit', [UserApiController::class, 'update']); // Deactivated in frontend

        // Prescription
        Route::post('/prescription/create', [PrescriptionApiController::class, 'store']);

        // Evaluation product
        Route::post('/products/{id}/evaluate', [ProductsApiController::class, 'evaluate']);

        // Cart
        Route::get('/cart', [CartApiController::class, 'show']);
        Route::post('/cart/update-items', [CartApiController::class, 'updateItems']);
        Route::post('/cart/update-shipping', [CartApiController::class, 'updateShipping']);
        Route::post('/cart/validate-items', [CartApiController::class, 'validateItems']);
        Route::post('/cart/clear', [CartApiController::class, 'delete']);

        // Cart frozen
        Route::get('/cart/freeze', [CartApiController::class, 'freeze']);
        Route::get('/cart/TTL', [CartApiController::class, 'getTimeLeftFrozen']);

        // Promo
        Route::post('/cart/verify-promo', [CartApiController::class, 'verifyPromo']);
        Route::post('/cart/remove-promo', [CartApiController::class, 'removePromo']);

        // Cart frozen
        // Route::get('/cart/freeze', [CartApiController::class, 'freeze']);
        
        // Orders
        Route::post('/order/store', [OrdersApiController::class, 'store']);
        // Route::get('/order/moneris', [OrdersApiController::class, 'testMoneris']);
    });

Route::get('/order/moneris', [OrdersApiController::class, 'testMoneris']);
Route::get('/order/totals', [OrdersApiController::class, 'getOpenTotals']);

// Products
Route::get('/products', [ProductsApiController::class, 'index']);
Route::get('/product/{id}', [ProductsApiController::class, 'show']);
Route::get('/product/slug/{slug}', [ProductsApiController::class, 'showBySlug']);

// Route::get('/product/{id}/sync', [ProductsApiController::class, 'sync'])->defaults('type', 'regular');
// Route::get('/accessories/{id}/sync', [ProductsApiController::class, 'sync'])->defaults('type', 'accessory');
// Route::get('/clone/{id}/sync', [ProductsApiController::class, 'sync'])->defaults('type', 'clone');


// Producers
Route::get('/producers', [ProducersApiController::class, 'index']);
Route::get('/producer/{slug}', [ProducersApiController::class, 'show']);

// Pages
// Route::get('/pages', [PageApiController::class, 'index']); // ALL
Route::get('/pages/{category}', [CustomPageApiController::class, 'index']);
Route::get('/page/{slug}/tr', [CustomPageApiController::class, 'translate']);
Route::get('/page/{slug}', [CustomPageApiController::class, 'show']);

// General
Route::get('shipping', [ShippingApiController::class, 'index']);
Route::get('holidays', [ShippingApiController::class, 'holidays']);
// Route::get('global-content/{type}', [GlobalContentApiController::class, 'show']);
Route::get('static-content/{identifiant}', [StaticSettingApiController::class, 'show']);

// Promo slides and banner for home page
Route::get('slides', [PromoApiController::class, 'slides']);
Route::get('banner', [PromoApiController::class, 'banner']);

// Contact
Route::post('contact', [ContactApiController::class, 'supportContact']);

// Newsletter
Route::post('newsletter', [ContactApiController::class, 'newsletter']);

// Invoice
Route::get('/user/order/generate/{id}', [PdfApiController::class, 'generatePdf']);

// Unsubscribe link for expiry reminders
Route::get('/unsubscribe-expiry/{token}', [UserApiController::class, 'unsubscribeExpiry']);
