<?php

use App\Models\User;
use App\Models\Order;
use App\Models\Holiday;
use Illuminate\Support\Carbon;
use App\Mail\EmailNotification;
use App\Services\BluelinkService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\StorageController;
use App\Http\Controllers\MailTestingController;
use App\Http\Controllers\Api\ProductsApiController;
use App\Http\Controllers\Admin\ProductRegularCrudController;
use App\Http\Controllers\Api\PdfApiController;
use Revolution\Google\Sheets\Facades\Sheets;
/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    // return redirect()->guest(backpack_url('login'));
    return redirect('admin');
});

Route::view('/icons', 'icons', [
    'icons' => ['glass', 'star-o', 'remove', 'close', 'gear', 'trash-o', 'file-o', 'clock-o', 'arrow-circle-o-down', 'arrow-circle-o-up', 'play-circle-o', 'repeat', 'rotate-right', 'refresh', 'dedent', 'video-camera', 'picture-o', 'photo', 'image', 'pencil', 'map-marker', 'pencil-square-o', 'share-square-o', 'check-square-o', 'arrows', 'times-circle-o', 'check-circle-o', 'mail-forward', 'warning', 'calendar', 'arrows-v', 'arrows-h', 'bar-chart', 'bar-chart-o', 'gears', 'thumbs-o-up', 'thumbs-o-down', 'heart-o', 'sign-out', 'linkedin-square', 'thumb-tack', 'external-link', 'sign-in', 'lemon-o', 'square-o', 'bookmark-o', 'facebook', 'facebook-f', 'feed', 'hdd-o', 'hand-o-right', 'hand-o-left', 'hand-o-up', 'hand-o-down', 'arrows-alt', 'group', 'chain', 'scissors', 'files-o', 'floppy-o', 'navicon', 'reorder', 'google-plus', 'money', 'unsorted', 'sort-desc', 'sort-asc', 'linkedin', 'rotate-left', 'legal', 'tachometer', 'dashboard', 'comment-o', 'comments-o', 'flash', 'paste', 'lightbulb-o', 'exchange', 'cloud-download', 'cloud-upload', 'bell-o', 'cutlery', 'file-text-o', 'building-o', 'hospital-o', 'tablet', 'mobile', 'mobile-phone', 'circle-o', 'mail-reply', 'folder-o', 'folder-open-o', 'smile-o', 'frown-o', 'meh-o', 'keyboard-o', 'flag-o', 'mail-reply-all', 'star-half-o', 'star-half-empty', 'star-half-full', 'code-fork', 'chain-broken', 'shield', 'calendar-o', 'ticket', 'minus-square-o', 'level-up', 'level-down', 'pencil-square', 'external-link-square', 'caret-square-o-down', 'toggle-down', 'caret-square-o-up', 'toggle-up', 'caret-square-o-right', 'toggle-right', 'eur', 'euro', 'gbp', 'usd', 'dollar', 'inr', 'rupee', 'jpy', 'cny', 'rmb', 'yen', 'rub', 'ruble', 'rouble', 'krw', 'won', 'bitcoin', 'file-text', 'sort-alpha-asc', 'sort-alpha-desc', 'sort-amount-asc', 'sort-amount-desc', 'sort-numeric-asc', 'sort-numeric-desc', 'youtube-play', 'bitbucket-square', 'long-arrow-down', 'long-arrow-up', 'long-arrow-left', 'long-arrow-right', 'gittip', 'sun-o', 'moon-o', 'arrow-circle-o-right', 'arrow-circle-o-left', 'caret-square-o-left', 'toggle-left', 'dot-circle-o', 'try', 'turkish-lira', 'plus-square-o', 'institution', 'bank', 'mortar-board', 'spoon', 'automobile', 'cab', 'envelope-o', 'file-pdf-o', 'file-word-o', 'file-excel-o', 'file-powerpoint-o', 'file-image-o', 'file-photo-o', 'file-picture-o', 'file-archive-o', 'file-zip-o', 'file-audio-o', 'file-sound-o', 'file-video-o', 'file-movie-o', 'file-code-o', 'life-bouy', 'life-buoy', 'life-saver', 'support', 'circle-o-notch', 'ra', 'resistance', 'ge', 'y-combinator-square', 'yc-square', 'wechat', 'send', 'paper-plane-o', 'send-o', 'circle-thin', 'header', 'sliders', 'futbol-o', 'soccer-ball-o', 'newspaper-o', 'bell-slash-o', 'trash', 'eyedropper', 'area-chart', 'pie-chart', 'line-chart', 'cc', 'ils', 'shekel', 'sheqel', 'meanpath', 'diamond', 'intersex', 'facebook-official', 'hotel', 'yc', 'battery-4', 'battery', 'battery-3', 'battery-2', 'battery-1', 'battery-0', 'sticky-note-o', 'hourglass-o', 'hourglass-1', 'hourglass-2', 'hourglass-3', 'hand-rock-o', 'hand-grab-o', 'hand-paper-o', 'hand-stop-o', 'hand-scissors-o', 'hand-lizard-o', 'hand-spock-o', 'hand-pointer-o', 'hand-peace-o', 'television', 'calendar-plus-o', 'calendar-minus-o', 'calendar-times-o', 'calendar-check-o', 'map-o', 'commenting', 'commenting-o', 'vimeo', 'credit-card-alt', 'pause-circle-o', 'stop-circle-o', 'wheelchair-alt', 'question-circle-o', 'volume-control-phone', 'asl-interpreting', 'deafness', 'hard-of-hearing', 'signing', 'google-plus-official', 'google-plus-circle', 'fa', 'handshake-o', 'envelope-open-o', 'address-book-o', 'vcard', 'address-card-o', 'vcard-o', 'user-circle-o', 'user-o', 'drivers-license', 'id-card-o', 'drivers-license-o', 'thermometer-4', 'thermometer', 'thermometer-3', 'thermometer-2', 'thermometer-1', 'thermometer-0', 'bathtub', 's15', 'times-rectangle', 'window-close-o', 'times-rectangle-o', 'eercast', 'snowflake-o']
]);




Route::get('/users/{path}', [StorageController::class, 'show'])->where('path', '.*');
Route::get('storage/users/{path}', [StorageController::class, 'show'])->where('path', '.*');

Route::middleware(['web'])->group(function () {
    Route::get('/product/{id}/sync', [ProductRegularCrudController::class, 'sync'])->defaults('type', 'regular');
    Route::get('/accessories/{id}/sync', [ProductsApiController::class, 'sync'])->defaults('type', 'accessory');
    Route::get('/clones/{id}/sync', [ProductsApiController::class, 'sync'])->defaults('type', 'clone');
});


// temp testing route, will delete once mail testing is done and validated
// Route::get('/mail', function () {
//     $user = User::where('email', '<EMAIL>')->first();

//     $lang = 'fr';
//     App::setLocale( $lang);
//     $user->userdetail->language = $lang;

//     $subject = __('mail.prescription_will_expired.subject');
//     $mailData = [
//         'name' => $user->fullname,
//         'firstname' => $user->firstname,
//         'lastname' => $user->lastname,
//         'email' => $user->email,
//         'user' => $user,
//         'id' => $user->id,
//         'veteran' => false,
//         'phone' => $user->userdetail->phone,
//         'address' => $user->shipping_address,
//         'shipping_type' => $user->getShippingTypeAttribute(),
//         'order' => "123456",
//         'order_content' => '{"price_before_taxes":281.34,"cart_discount":0,"total_taxes":42.13,"shipping_cost":0,"final_order_price":323.47,"delivery_date":"2025-03-31","delivery_time":"10:00 - 12:00","client_info":{"name":"Team Kryzalid","email":"<EMAIL>","veteran_id":""},"cart_discount_percentage":0,"product_list":[{"id":230,"name":"Crescendo - Sol vivant organique","product_title_fr":"Crescendo - Sol vivant organique","product_title_en":"Crescendo - Sol vivant organique","sku":"Fuga05","quantity":2,"type":"Sativa","format":14,"product_description_bl":"Test","uom":"PK14","product_price":106.17,"base_price":124.9,"discount":0.15,"total":212.34,"base_total":249.8},{"id":435,"name":"Balanc\u00e9 en Sol Vivant","product_title_fr":"Balanc\u00e9 en Sol Vivant","product_title_en":"Balanc\u00e9 en Sol Vivant","sku":"Fuga10","quantity":1,"type":"Hybride","format":7,"product_description_bl":"Testt","uom":"PK7","product_price":69,"base_price":69,"discount":0,"total":69,"base_total":69}]}',
//         'lang' => $user->userdetail->language,
//         'date' => $user->cartPrice->shipping_date ?? 'soon',
//         'time' => $user->cartPrice->shipping_time ?? '9am - 5pm',
//         'status' => 'shipped',
//         'nb_jour' => 15,
//     ];
//     return new EmailNotification('admin', __('mail.order.admin.subject'), 'customer.register', $mailData);
//     Mail::send(new EmailNotification($user->email, $subject, 'customer.medical.prescription_expired', ['firstname' => $user->firstname, 'lastname' => $user->lastname]));
    // return new EmailNotification('admin', $subject, 'customer.medical.prescription_expired', $mailData);

// return new EmailNotification($user->email, $subject, 'admin.medical.prescription_will_expired', $mailData);
    // return new EmailNotification($user->email, $subject, 'customer.medical.prescription_will_expired', $mailData);

    // return new EmailNotification(
    //             $user->email, 
    //             $subject, 
    //             'customer.medical.prescription_expired', 
    //             $mailData
    // );
//     Mail::send(new EmailNotification($user->email, $subject, 'customer.medical.no_prescription', ['firstname' => $user->firstname, 'lastname' => $user->lastname]));
//     Mail::send(new EmailNotification($user->email, __('mail.refund_order.subject'), 'customer.refund-order', ['amount' => '2', 'moneris_id' => 'dawdwaed']));
//     Mail::send(new EmailNotification($user-adm>email, __('mail.support.subject_mail'), 'customer.confirm_support_notifications', $mailData));
//     Mail::send(new EmailNotification('admin', __('mail.order.admin.subject'), 'admin.new_order', $mailData));

// });