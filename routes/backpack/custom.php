<?php

use Illuminate\Support\Facades\Route;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization;

Route::prefix(LaravelLocalization::setLocale() . '/' . config('backpack.base.route_prefix', 'admin'))
    ->middleware(['localize', config('backpack.base.web_middleware', 'web'), config('backpack.base.middleware_key', 'admin')])
    ->namespace('App\Http\Controllers\Admin')
    ->group(function () {

    // Users
    Route::crud('user', 'UserCrudController');
    Route::crud('client/{client_type}', 'ClientCrudController');
    Route::crud('prescription/{client_type}', 'PrescriptionCrudController');
    Route::get('user/ajax-search-filter', 'ClientCrudController@searchFilter')->name('user.filter');

    // Products
    Route::crud('products', 'ProductRegularCrudController');
    Route::crud('accessories', 'ProductAccessoryCrudController');

    Route::crud('featured-products-qc', 'FeaturedProductsQcCrudController');
    Route::get('featured-products-qc/{id}/remove-featured', [
        'uses' => 'FeaturedProductsQcCrudController@removeFeatured',
        'as' => 'featured-products-qc.remove-featured',
    ]);

    Route::crud('featured-products-on', 'FeaturedProductsOnCrudController');
    Route::get('featured-products-on/{id}/remove-featured', [
        'uses' => 'FeaturedProductsOnCrudController@removeFeatured',
        'as' => 'featured-products-on.remove-featured',
    ]);
    // Route::crud('clones', 'ProductCloneCrudController');
    Route::crud('product-categories', 'ProductCategoryCrudController');

    // Producers
    Route::crud('producers', 'ProducerCrudController');
    
    // Contents
    Route::crud('custom-page', 'CustomPageCrudController');
    Route::crud('promo-banners', 'PromoBannerCrudController');
    Route::crud('promo-slides', 'PromoSlideCrudController');

    // Route::group(['prefix' => 'admin', 'middleware' => ['web', 'admin']], function () {
        // Route::crud('global-content', 'GlobalContentCrudController');
    // });

    // E-commerce
    Route::crud('order', 'OrderCrudController');
    Route::crud('promo-codes', 'PromoCodeCrudController');
    Route::crud('discounts', 'DiscountCrudController');
    
    // Settings
    Route::crud('taxes', 'TaxeCrudController');
    Route::crud('shipping', 'ShippingCrudController');
    Route::crud('holidays', 'HolidayCrudController');
    Route::crud('physician', 'PhysicianCrudController');
    Route::crud('api-log', 'ApiLogCrudController');
    Route::crud('parametres-statiques', 'StaticSettingCrudController');
});