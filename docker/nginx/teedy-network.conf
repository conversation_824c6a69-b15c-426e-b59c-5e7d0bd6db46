server {
    listen 80;
    client_max_body_size 100M;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /var/www/public;
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
        add_header Access-Control-Allow-Origin "localhost";
        add_header Access-Control-Allow-Origin "localhost:4200";
        add_header Access-Control-Allow-Origin "localhost:8000";
        add_header Access-Control-Allow-Origin "127.0.0.1";
        add_header Access-Control-Allow-Origin "127.0.0.1:4200";
        add_header Access-Control-Allow-Origin "127.0.0.1:8000";
        add_header Access-Control-Allow-Origin "http://localhost";
        add_header Access-Control-Allow-Origin "http://localhost:4200";
        add_header Access-Control-Allow-Origin "http://localhost:8000";
        add_header Access-Control-Allow-Origin "http://127.0.0.1";
        add_header Access-Control-Allow-Origin "http://127.0.0.1:8000";
        add_header Access-Control-Allow-Origin "http://127.0.0.1:4200";
        add_header Access-Control-Allow-Origin *;
    }
}