version: "3.7"
services:
  app:
    build:
      args:
        user: teedy
        uid: 1000
      context: ./
      dockerfile: docker/app/Dockerfile
    image: teedy-backend-v2
    container_name: teedy-backend-v2-app
    restart: unless-stopped
    working_dir: /var/www/
    volumes:
      - ./:/var/www
    networks:
      - teedy-backend-v2-network

  db:
    image: mariadb:latest
    container_name: teedy-backend-v2-db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_PASSWORD: ${DB_PASSWORD}
      MYSQL_USER: ${DB_USERNAME}
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - teedy-backend-v2-data:/var/lib/mysql
    networks:
      - teedy-backend-v2-network
  
  phpmyadmin:
    image: phpmyadmin:latest
    container_name: teedy-backend-v2-pma
    ports:
      - 8080:80
    environment:
      PMA_HOST: db
      MYSQL_USERNAME: "${DB_USERNAME}"
      MYSQL_ROOT_PASSWORD: "${DB_PASSWORD}"
      UPLOAD_LIMIT: 300M
    restart: unless-stopped
    networks:
      - teedy-backend-v2-network

  nginx:
    image: nginx:alpine
    container_name: teedy-backend-v2-nginx
    restart: unless-stopped
    ports:
      - 8000:80
    volumes:
      - ./:/var/www
      - ./docker/nginx:/etc/nginx/conf.d/
    networks:
      - teedy-backend-v2-network

networks:
  teedy-backend-v2-network:
    driver: bridge

volumes:
  teedy-backend-v2-data:

# install
# 0. Copy env file, change APP_ENV to local, APP_URL for 127.0.0.1 and DB_HOST for db (service name from docker-compose)
# 1. docker-compose build app (enter github token when prompted)
# 2. docker-compose up -d
# 3. docker-compose ps (to test)
# 4. docker-compose exec app rm -rf vendor composer.lock (not for teedy-backend-v2-closion - generates error for wrong version of pro available)
# 4. docker-compose exec app mkdir bootstrap/cache && mkdir storage/framework && cd storage/framework && mkdir cache/data && mkdir sessions && mkdir views
# 5. docker-compose exec app composer install
# 6. docker-compose exec app php artisan key:generate
# 7. docker-compose logs nginx (to see logs)
# 8. docker-compose pause/unpause (keeps state)
# 6. Clone DB from kryzastage1 plesk into PMA